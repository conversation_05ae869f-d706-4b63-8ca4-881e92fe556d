package connect

import (
	"context"
	"errors"
	"reflect"
	"testing"

	"github.com/redis/go-redis/v9"
)

// --- Test WithConnections and GetConnections ---

func TestWithAndGetConnections(t *testing.T) {
	ctx := context.Background()
	// Create a dummy connections instance. (Here we're not concerned with non-nil fields.)
	dummy := &Connections{
		Redis:    nil,
		Postgres: nil,
		Bigquery: nil,
		Pubsub:   nil,
	}

	// Inject the dummy connections into the context.
	ctxWith := WithConnections(ctx, dummy)

	// Retrieve them back.
	got, err := GetConnections(ctxWith, false)
	if err != nil {
		t.Fatalf("expected no error, got %v", err)
	}
	if got != dummy {
		t.Fatalf("expected %p, got %p", dummy, got)
	}
}

func TestGetConnections_NotPresent(t *testing.T) {
	ctx := context.Background()
	_, err := GetConnections(ctx)
	if err == nil {
		t.Fatal("expected an error when connections are not set in context, got nil")
	}
}

// --- Test NewConnections by overriding dependency functions ---
//
// For this test we override the globals so that NewConnections returns
// dummy (but recognizable) connection objects.

func TestNewConnections(t *testing.T) {
	// Backup the original functions.
	origGRI := GetReleaseIdentifier
	origRedis := Redis
	origRedisAwait := RedisAwait
	origPubSub := PubSub
	origBigQuery := BigQuery
	origPostgres := Postgres

	// Restore the original functions after the test.
	defer func() {
		GetReleaseIdentifier = origGRI
		Redis = origRedis
		RedisAwait = origRedisAwait
		PubSub = origPubSub
		BigQuery = origBigQuery
		Postgres = origPostgres
	}()

	// Dummy values for testing.
	dummyRelease := "dummy-release"

	// Create dummy structs for connections.
	dummyRedis := &redis.Client{}
	dummyPubsub := WrapPubsubClient(nil)
	dummyBQ := &BigQueryExecutor{}
	dummyPG := &PostgresExecutor{}

	// Override dependencies.
	GetReleaseIdentifier = func() (string, error) {
		return dummyRelease, nil
	}
	Redis = func(ctx context.Context) (*redis.Client, error) {
		return dummyRedis, nil
	}
	RedisAwait = func(ctx context.Context, client *redis.Client, key, release string) error {
		if release != dummyRelease {
			return errors.New("release mismatch")
		}
		return nil
	}
	PubSub = func(ctx context.Context) (PsClient, error) {
		return dummyPubsub, nil
	}
	BigQuery = func(ctx context.Context, config *DatabaseConfig, factory BQClientFactory) (*BigQueryExecutor, error) {
		return dummyBQ, nil
	}
	Postgres = func(ctx context.Context, _ *DatabaseConfig) (*PostgresExecutor, error) {
		return dummyPG, nil
	}

	// Finally, check that the connections are what we expect them to be.
	conns := NewConnections(context.Background())
	if conns == nil {
		t.Fatal("expected non-nil connections")
	}
	if conns.Redis != dummyRedis {
		t.Errorf("expected Redis client %v, got %v", dummyRedis, conns.Redis)
	}
	if conns.Pubsub != dummyPubsub {
		t.Errorf("expected PubSub client %v, got %v", dummyPubsub, conns.Pubsub)
	}
	if conns.Bigquery != dummyBQ {
		t.Errorf("expected BigQuery executor %v, got %v", dummyBQ, conns.Bigquery)
	}
	if conns.Postgres != dummyPG {
		t.Errorf("expected Postgres executor %v, got %v", dummyPG, conns.Postgres)
	}
}

// Here we simply demonstrate that calling Close does not panic.
func TestConnectionsClose_Nil(t *testing.T) {
	// If the Connections pointer itself is nil, Close() should do nothing.
	var conns *Connections = nil
	// Should not panic.
	conns.Close()
}

// --- Test SplitSQLStatements ---

func TestSplitSQLStatements(t *testing.T) {
	tests := []struct {
		name  string
		input string
		want  []string
	}{
		{
			name:  "Single statement without semicolon",
			input: "SELECT * FROM users",
			want:  []string{"SELECT * FROM users"},
		},
		{
			name:  "Multiple statements",
			input: "SELECT * FROM users; SELECT * FROM orders;",
			want:  []string{"SELECT * FROM users", "SELECT * FROM orders"},
		},
		{
			name:  "Semicolon inside single quotes",
			input: "INSERT INTO logs (message) VALUES ('Value; not end'); SELECT * FROM logs;",
			want:  []string{"INSERT INTO logs (message) VALUES ('Value; not end')", "SELECT * FROM logs"},
		},
		{
			name:  "Semicolon inside double quotes",
			input: "INSERT INTO logs (message) VALUES (\"Value; not end\"); SELECT * FROM logs;",
			want:  []string{"INSERT INTO logs (message) VALUES (\"Value; not end\")", "SELECT * FROM logs"},
		},
		{
			name:  "Line comment with semicolon",
			input: "SELECT * FROM users -- comment; not a delimiter\n;SELECT * FROM orders;",
			want:  []string{"SELECT * FROM users -- comment; not a delimiter", "SELECT * FROM orders"},
		},
		{
			name:  "Block comment with semicolon",
			input: "SELECT * FROM users /* block; comment */; SELECT * FROM orders;",
			want:  []string{"SELECT * FROM users /* block; comment */", "SELECT * FROM orders"},
		},
		{
			name:  "Empty string",
			input: "",
			want:  nil,
		},
		{
			name:  "Single semicolon",
			input: ";",
			want:  nil,
		},
		{
			name:  "Multiple semicolons",
			input: "SELECT * FROM users;;",
			want:  []string{"SELECT * FROM users"},
		},
		{
			name:  "Multiple newlines",
			input: "\n\n\n\nSELECT * FROM users;\n\n\n\n\n\n\n;\n\n;\n\nSELECT 1; SELECT 2;",
			want:  []string{"SELECT * FROM users", "SELECT 1", "SELECT 2"},
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			got := SplitSQLStatements(tc.input)
			if !reflect.DeepEqual(got, tc.want) {
				t.Errorf("SplitSQLStatements() = %#v, want %#v", got, tc.want)
			}
		})
	}
}

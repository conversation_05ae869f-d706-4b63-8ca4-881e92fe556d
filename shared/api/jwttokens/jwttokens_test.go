package jwttokens

import (
	"bytes"
	"crypto/rand"
	"crypto/rsa"
	"crypto/x509"
	"encoding/json"
	"encoding/pem"
	"testing"
	"time"

	security "synapse-its.com/shared/api/security"
)

// generateTestKeyPair creates a new RSA key pair and returns the PEM-encoded keys.
func generateTestKeyPair(t *testing.T) (privatePEM, publicPEM []byte) {
	key, err := rsa.GenerateKey(rand.Reader, 2048)
	if err != nil {
		t.Fatalf("failed to generate test key: %v", err)
	}
	privDER := x509.MarshalPKCS1PrivateKey(key)
	privBlock := &pem.Block{
		Type:  "RSA PRIVATE KEY",
		Bytes: privDER,
	}
	privatePEM = pem.EncodeToMemory(privBlock)

	pubDER, err := x509.MarshalPKIXPublicKey(&key.PublicKey)
	if err != nil {
		t.Fatalf("failed to marshal public key: %v", err)
	}
	pubBlock := &pem.Block{
		Type:  "PUBLIC KEY",
		Bytes: pubDER,
	}
	publicPEM = pem.EncodeToMemory(pubBlock)
	return
}

func TestCreateJwtTokenUsingDuration(t *testing.T) {
	// Generate a test RSA key pair.
	priv, pub := generateTestKeyPair(t)

	// Override security.GetJWTAsymmetric so that our signing functions use our test keys.
	oldGetJWT := security.GetJWTAsymmetric
	security.GetJWTAsymmetric = func() (privateKey string, publicKey string, err error) {
		return string(priv), string(pub), nil
	}
	defer func() { security.GetJWTAsymmetric = oldGetJWT }()

	// Create an empty permissions set.
	perms := UserPermissions{
		SoftwareGateway: []UserSoftwareGatewayAccess{},
		Device:          []UserDeviceAccess{},
	}

	username := "testuser"
	role := int64(1) // Should map to "user" per ValidateRoleInt.
	duration := time.Hour

	token, exp, err := CreateJwtTokenUsingDuration(username, duration, role, perms)
	if err != nil {
		t.Fatalf("CreateJwtTokenUsingDuration failed: %v", err)
	}
	if token == "" {
		t.Fatal("expected a token, got an empty string")
	}
	if exp.Before(time.Now()) {
		t.Fatal("expected expiration to be in the future")
	}

	// Validate the token using our test public key.
	jwtToken, userPerms, err := ValidateJJwtTokenWithPublicKey(token, string(pub))
	if err != nil {
		t.Fatalf("ValidateJJwtTokenWithPublicKey failed: %v", err)
	}
	if jwtToken.AuthId != username {
		t.Errorf("expected AuthId %q, got %q", username, jwtToken.AuthId)
	}
	if jwtToken.Role != "user" {
		t.Errorf("expected Role 'user', got %q", jwtToken.Role)
	}
	// Expect empty permissions.
	if len(userPerms.SoftwareGateway) != 0 || len(userPerms.Device) != 0 {
		t.Errorf("expected empty permissions, got %+v", userPerms)
	}
}

func TestUserPermissionsRoundTrip(t *testing.T) {
	// Create a sample permissions object.
	origPerms := UserPermissions{
		SoftwareGateway: []UserSoftwareGatewayAccess{
			{SoftwareGateway: "gw1"},
			{SoftwareGateway: "gw2"},
		},
		Device: []UserDeviceAccess{
			{Device: "dev1"},
		},
	}

	permStr, err := createUserPermissions(origPerms)
	if err != nil {
		t.Fatalf("CreateUserPermissions failed: %v", err)
	}
	if permStr == "" {
		t.Fatal("expected non-empty permissions string")
	}

	decodedPerms, err := getUserPermissions(permStr)
	if err != nil {
		t.Fatalf("GetUserPermissions failed: %v", err)
	}

	origBytes, err := json.Marshal(origPerms)
	if err != nil {
		t.Fatalf("failed to marshal original permissions: %v", err)
	}
	decodedBytes, err := json.Marshal(decodedPerms)
	if err != nil {
		t.Fatalf("failed to marshal decoded permissions: %v", err)
	}

	if !bytes.Equal(origBytes, decodedBytes) {
		t.Errorf("permissions round-trip mismatch, expected %s, got %s", string(origBytes), string(decodedBytes))
	}
}

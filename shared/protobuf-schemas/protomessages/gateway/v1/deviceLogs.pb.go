// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.30.2
// source: gateway/v1/deviceLogs.proto

package gatewayv1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// DeviceLogs represents the overall body payload.
type DeviceLogs struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Unique identifier for the device.
	DeviceId string `protobuf:"bytes,1,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	// Repeated list of all logs gathered by gateway.
	Logs          []*LogEntry `protobuf:"bytes,2,rep,name=logs,proto3" json:"logs,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeviceLogs) Reset() {
	*x = DeviceLogs{}
	mi := &file_gateway_v1_deviceLogs_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceLogs) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceLogs) ProtoMessage() {}

func (x *DeviceLogs) ProtoReflect() protoreflect.Message {
	mi := &file_gateway_v1_deviceLogs_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceLogs.ProtoReflect.Descriptor instead.
func (*DeviceLogs) Descriptor() ([]byte, []int) {
	return file_gateway_v1_deviceLogs_proto_rawDescGZIP(), []int{0}
}

func (x *DeviceLogs) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *DeviceLogs) GetLogs() []*LogEntry {
	if x != nil {
		return x.Logs
	}
	return nil
}

// LogEntry represents an individual log type.
type LogEntry struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Type of log.
	LogType string `protobuf:"bytes,1,opt,name=log_type,json=logType,proto3" json:"log_type,omitempty"`
	// Log messages for given type of log.
	// May contain multiple messages such as in the case of Fault Singal Sequence Logs.
	Message       [][]byte `protobuf:"bytes,2,rep,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LogEntry) Reset() {
	*x = LogEntry{}
	mi := &file_gateway_v1_deviceLogs_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LogEntry) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogEntry) ProtoMessage() {}

func (x *LogEntry) ProtoReflect() protoreflect.Message {
	mi := &file_gateway_v1_deviceLogs_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogEntry.ProtoReflect.Descriptor instead.
func (*LogEntry) Descriptor() ([]byte, []int) {
	return file_gateway_v1_deviceLogs_proto_rawDescGZIP(), []int{1}
}

func (x *LogEntry) GetLogType() string {
	if x != nil {
		return x.LogType
	}
	return ""
}

func (x *LogEntry) GetMessage() [][]byte {
	if x != nil {
		return x.Message
	}
	return nil
}

var File_gateway_v1_deviceLogs_proto protoreflect.FileDescriptor

const file_gateway_v1_deviceLogs_proto_rawDesc = "" +
	"\n" +
	"\x1bgateway/v1/deviceLogs.proto\x12\n" +
	"gateway.v1\"S\n" +
	"\n" +
	"DeviceLogs\x12\x1b\n" +
	"\tdevice_id\x18\x01 \x01(\tR\bdeviceId\x12(\n" +
	"\x04logs\x18\x02 \x03(\v2\x14.gateway.v1.LogEntryR\x04logs\"?\n" +
	"\bLogEntry\x12\x19\n" +
	"\blog_type\x18\x01 \x01(\tR\alogType\x12\x18\n" +
	"\amessage\x18\x02 \x03(\fR\amessageBOZMbitbucket.org/synapse-its/protobuf-schemas/protomessages/gateway/v1;gatewayv1b\x06proto3"

var (
	file_gateway_v1_deviceLogs_proto_rawDescOnce sync.Once
	file_gateway_v1_deviceLogs_proto_rawDescData []byte
)

func file_gateway_v1_deviceLogs_proto_rawDescGZIP() []byte {
	file_gateway_v1_deviceLogs_proto_rawDescOnce.Do(func() {
		file_gateway_v1_deviceLogs_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_gateway_v1_deviceLogs_proto_rawDesc), len(file_gateway_v1_deviceLogs_proto_rawDesc)))
	})
	return file_gateway_v1_deviceLogs_proto_rawDescData
}

var file_gateway_v1_deviceLogs_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_gateway_v1_deviceLogs_proto_goTypes = []any{
	(*DeviceLogs)(nil), // 0: gateway.v1.DeviceLogs
	(*LogEntry)(nil),   // 1: gateway.v1.LogEntry
}
var file_gateway_v1_deviceLogs_proto_depIdxs = []int32{
	1, // 0: gateway.v1.DeviceLogs.logs:type_name -> gateway.v1.LogEntry
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_gateway_v1_deviceLogs_proto_init() }
func file_gateway_v1_deviceLogs_proto_init() {
	if File_gateway_v1_deviceLogs_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_gateway_v1_deviceLogs_proto_rawDesc), len(file_gateway_v1_deviceLogs_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_gateway_v1_deviceLogs_proto_goTypes,
		DependencyIndexes: file_gateway_v1_deviceLogs_proto_depIdxs,
		MessageInfos:      file_gateway_v1_deviceLogs_proto_msgTypes,
	}.Build()
	File_gateway_v1_deviceLogs_proto = out.File
	file_gateway_v1_deviceLogs_proto_goTypes = nil
	file_gateway_v1_deviceLogs_proto_depIdxs = nil
}

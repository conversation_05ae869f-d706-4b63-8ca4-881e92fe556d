#!/usr/bin/env bash
# Description: List or set the current BigQuery namespace
#   bqe ns
#     Show all namespaces, mark the current one
#   bqe ns <value>
#     Set the current namespace to <value>

set -euo pipefail

# find the existing namespace (empty if unset)
current=$(bqe-get namespace 2>/dev/null || echo "")

# build the list of all namespaces by taking the part before "__" in each table name
namespaces=$(bqe ls --all | tail -n +3 \
  | awk '{print $1}' \
  | awk -F"__" '{print $1}' \
  | sort -u)

if [[ $# -eq 0 ]]; then
  echo "Available namespaces:"
  for ns in $namespaces; do
    if [[ "$ns" == "$current" ]]; then
      echo "  * $ns"
    else
      echo "    $ns"
    fi
  done
  exit 0
fi

# set new namespace
ns=$1
if echo "$namespaces" | grep -xq "$ns"; then
  bqe-set namespace "$ns"
  echo "Namespace set to '$ns'"
  exit 0
else
  echo "Error: '$ns' is not a valid namespace" >&2
  exit 1
fi

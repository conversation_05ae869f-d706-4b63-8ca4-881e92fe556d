package subscriptions

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestDefaultSubscriptions(t *testing.T) {
	t.Parallel()

	subs := DefaultSubscriptions()

	// There should be exactly 18 entries
	assert.Len(t, subs, 18, "DefaultSubscriptions() should return 18 subscriptions")

	// Collect all names and ensure handlers are non-nil
	var names []string
	for _, s := range subs {
		names = append(names, s.Name)
		assert.NotNil(t, s.<PERSON>, "subscription %q has a nil Handler", s.Name)
	}

	// Compare against the exact set of expected names
	expected := []string{
		"etl-processing-gateway-rmsData",
		"etl-processing-gateway-gatewayLog",
		"etl-processing-gateway-faultNotification",
		"etl-processing-gateway-rmsEngine",
		"etl-processing-gateway-monitorName",
		"etl-processing-gateway-macAddress",
		"etl-processing-gateway-faultLogs",
		"etl-processing-gateway-perfStats",
		"etl-raw-gateway-rmsData",
		"etl-raw-gateway-gatewayLog",
		"etl-raw-gateway-faultNotification",
		"etl-raw-gateway-rmsEngine",
		"etl-raw-gateway-monitorName",
		"etl-raw-gateway-macAddress",
		"etl-raw-gateway-faultLogs",
		"etl-raw-gateway-perfStats",
		"etl-processing-dlq-messages",
		"etl-processing-dlq-bqbatch",
	}

	assert.ElementsMatch(t, expected, names, "subscription names mismatch")
}

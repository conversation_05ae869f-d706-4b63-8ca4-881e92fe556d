<nz-layout>
  <nz-header style="background: #fff; padding: 0 16px;">
    <div style="display: flex; justify-content: space-between; align-items: center;">
      <h1 routerLink="/">OEM Dashboard</h1>
      <div style="display: flex; align-items: center;">
        <nz-avatar [nzText]="userName ? userName[0] : 'U'"></nz-avatar>
        <span style="margin: 0 8px;">{{ userName || 'User' }}</span>
        <nav>
          <button nz-button nzType="primary" *ngIf="!userName" (click)="login()">
            Log in
          </button>
          <button nz-button nzType="primary" *ngIf="userName" (click)="logout()">
            Log out
          </button>
        </nav>
      </div>
    </div>
  </nz-header>
</nz-layout>
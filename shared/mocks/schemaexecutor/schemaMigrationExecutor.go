package schemaexecutor

import (
	"database/sql"

	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/mocks/dbexecutor"
)

type FakeSchemaMigrationExecutor struct {
	EnableFailAfter                          bool
	EnsureSchemaMigrationsTableFunc          func() error
	EnsureSchemaMigrationsTableCallCount     int
	EnsureSchemaMigrationsTableCallFailAfter int
	QueryGenericFunc                         func(query string, args ...interface{}) ([]map[string]interface{}, error)
	QueryGenericCallCount                    int
	QueryGenericCallFailAfter                int
	ExecFunc                                 func(query string, args ...interface{}) (sql.Result, error)
	ExecCallCount                            int
	ExecCallFailAfter                        int
	ExecMultipleFunc                         func(queries string) error
	ExecMultipleCallCount                    int
	ExecMultipleCallFailAfter                int
	QueryRowFunc                             func(query string, args ...interface{}) (map[string]interface{}, error)
	QueryRowCallCount                        int
	QueryRowCallFailAfter                    int
	ConfigFunc                               func() connect.DatabaseConfig
	ConfigCallCount                          int
	Client                                   *dbexecutor.FakeDBExecutor
}

func (f *FakeSchemaMigrationExecutor) EnsureSchemaMigrationsTable() error {
	f.EnsureSchemaMigrationsTableCallCount++
	if f.EnsureSchemaMigrationsTableFunc != nil {
		return f.EnsureSchemaMigrationsTableFunc()
	}
	if f.EnableFailAfter && f.EnsureSchemaMigrationsTableCallFailAfter >= 0 && f.EnsureSchemaMigrationsTableCallCount > f.EnsureSchemaMigrationsTableCallFailAfter {
		return ErrFakeSchemaMigrationExecutorEnsureSchemaMigrationsTableCallLimit
	}
	return nil
}

func (f *FakeSchemaMigrationExecutor) QueryGeneric(query string, args ...interface{}) ([]map[string]interface{}, error) {
	f.QueryGenericCallCount++
	if f.QueryGenericFunc != nil {
		return f.QueryGenericFunc(query, args...)
	}
	if f.EnableFailAfter && f.QueryGenericCallFailAfter >= 0 && f.QueryGenericCallCount > f.QueryGenericCallFailAfter {
		return nil, ErrFakeSchemaMigrationExecutorQueryGenericCallLimit
	}
	return nil, nil
}

func (f *FakeSchemaMigrationExecutor) Exec(query string, args ...interface{}) (sql.Result, error) {
	f.ExecCallCount++
	if f.ExecFunc != nil {
		return f.ExecFunc(query, args...)
	}
	if f.EnableFailAfter && f.ExecCallFailAfter >= 0 && f.ExecCallCount > f.ExecCallFailAfter {
		return nil, ErrFakeSchemaMigrationExecutorExecCallLimit
	}
	return nil, nil
}

func (f *FakeSchemaMigrationExecutor) ExecMultiple(query string) error {
	f.ExecMultipleCallCount++
	if f.ExecMultipleFunc != nil {
		return f.ExecMultipleFunc(query)
	}
	if f.EnableFailAfter && f.ExecMultipleCallFailAfter >= 0 && f.ExecMultipleCallCount > f.ExecMultipleCallFailAfter {
		return ErrFakeSchemaMigrationExecutorExecMultipleCallLimit
	}
	return nil
}

func (f *FakeSchemaMigrationExecutor) QueryRow(query string, args ...interface{}) (map[string]interface{}, error) {
	f.QueryRowCallCount++
	if f.QueryRowFunc != nil {
		return f.QueryRowFunc(query, args...)
	}
	if f.EnableFailAfter && f.QueryRowCallFailAfter >= 0 && f.QueryRowCallCount > f.QueryRowCallFailAfter {
		return nil, ErrFakeSchemaMigrationExecutorQueryRowCallLimit
	}
	return nil, nil
}

func (f *FakeSchemaMigrationExecutor) Config() connect.DatabaseConfig {
	f.ConfigCallCount++
	if f.ConfigFunc != nil {
		return f.ConfigFunc()
	}
	return connect.DatabaseConfig{}
}

-- Test Organization: Id=1
INSERT INTO {{Organization}} (Id,Organizationidentifier,APIKey,Description) VALUES (1,'DEV5AC4410CB8F3C8E29CE9210E5913B7B3B872B2BC','US4ZHOzPZ7a2b5ezFldb86jZ1UdUkyIg41hCcqea','<PERSON>''s Organization');
-- Test User: Email=<EMAIL>, Password=puppies1234, Id=1
--   Used in TestAuthenticate
INSERT INTO {{User}} (Id,OrganizationId,RoleId,UserName,Password,FirstName,LastName,Email,Mobile,NotificationSmsEnabled,IANATimezone,Description,FailedLoginAttempts,IsEnabled,LastLoginUTC,TokenDurationHours,WebEnabled,WebTokenDurationSeconds) VALUES (1,1,1,'<EMAIL>','336e98624f7c55062489f60a766ee8eb61f3e3793324f95700ae860987750f29','Test','User','<EMAIL>','19405551234',false,'America/Chicago','na',0,1,'2025-01-13 14:31:18',2160,1,120);
-- Test SoftwareGateway: Id=1, SoftwareGatewayIdentifier=d5c29fc65d7dc9e0798f68e4f4b982853587acfb5686e981c517849973198e9f
INSERT INTO {{SoftwareGateway}} (Id,OrganizationId,SoftwareGatewayIdentifier,APIKey,Token,DateLastCheckedInUTC,PushConfigOnNextCheck,IsEnabled,Description) VALUES (1,1,'d5c29fc65d7dc9e0798f68e4f4b982853587acfb5686e981c517849973198e9f','qiLkND6fYR2zVIQbgifvR2VbyOStT6qt6OgzPZTd','','2025-04-07 15:24:08',true,true,'Cornelius - Gateway');
-- Test Software Gateway Config
INSERT INTO {{SoftwareGatewayConfig}} (SoftwareGatewayId,MaximumDevices,Config) VALUES (1,3,'{"log_level": "error", "log_filename": "log/gateway-app.log", "log_max_backups": 10, "application_version": "deprecated", "log_max_age_in_days": 30, "log_compress_backups": true, "log_file_max_size_mb": 10, "rest_api_device_endpoint": "https://api.dev.synapse-its.app/ingest/v3/state", "send_gateway_logs_to_cloud": false, "edi_device_persist_connection": true, "edi_device_processing_retries": 5, "record_http_requests_to_folder": false, "device_state_send_frequency_seconds": 60, "config_change_check_frequency_seconds": 30, "send_gateway_performance_stats_to_cloud": true, "software_update_check_frequency_seconds": 60, "channel_state_send_frequency_milliseconds": 500, "gateway_performance_stats_output_frequency_seconds": 45, "ws_active": true, "ws_port": "8079", "ws_endpoint": "/gateway", "ws_max_connections": 2, "ws_send_frequency_milliseconds": 5000, "ws_heartbeat_send_frequency_milliseconds": 60000, "threshold_device_error_seconds": 90}');
-- Firebase Config
INSERT INTO {{Config}} (Key,Value,Description) VALUES ('FirebaseAuth','<stubbed>','Service account information for Google Firebase.');
-- Test Device: Id=1
INSERT INTO {{Device}} (Id,DeviceIdentifier,SoftwareGatewayId,Description,Type) VALUES (1,'3fbe60189eef4bb6ab9c03663cc7b3ba',1,'description device 1','Mock');
INSERT INTO {{Device}} (Id,DeviceIdentifier,SoftwareGatewayId,Description,Type) VALUES (2,'426b0919812b490d8b48b0ac788b9712',1,'description device 1','Mock');
INSERT INTO {{Device}} (Id,DeviceIdentifier,SoftwareGatewayId,Description,Type) VALUES (3,'fdf164e648994725bfae514d78887d4',1,'description device 1','Mock');
-- Test Instruction: deviceId=1
INSERT INTO {{SoftwareGatewayInstruction}} (UserId,DeviceId,Instruction,DateQueuedUTC,DateReceivedUTC,Status) VALUES (1,1,'get_device_logs',CURRENT_TIMESTAMP,null,'queued');
-- Test user permissions
INSERT INTO {{UserDevice}} (userid, deviceid, roleid) VALUES (1,1,1);
INSERT INTO {{UserDevice}} (userid, deviceid, roleid) VALUES (1,2,1);
INSERT INTO {{UserDevice}} (userid, deviceid, roleid) VALUES (1,3,1);
-- Test Device communication config
INSERT INTO {{DeviceCommunicationConfig}} (SoftwareGatewayId, DeviceIdentifier, Latitude, Longitude, IPAddress, Port, FlushConnectionMs, EnableRealtime, IsEnabled, Description) VALUES (1, '3fbe60189eef4bb6ab9c03663cc7b3ba', '29.7601', '-95.3701', '127.0.0.1', '8081', '500', 'true', 1, 'Mock Device Local');
INSERT INTO {{DeviceCommunicationConfig}} (SoftwareGatewayId, DeviceIdentifier, Latitude, Longitude, IPAddress, Port, FlushConnectionMs, EnableRealtime, IsEnabled, Description) VALUES (1, '426b0919812b490d8b48b0ac788b9712', '29.7601', '-95.3701', '127.0.0.1', '8083', '500', 'true', 1, 'Mock Device Local');
INSERT INTO {{DeviceCommunicationConfig}} (SoftwareGatewayId, DeviceIdentifier, Latitude, Longitude, IPAddress, Port, FlushConnectionMs, EnableRealtime, IsEnabled, Description) VALUES (1, 'fdf164e648994725bfae514d78887d4', '29.7601', '-95.3701', '127.0.0.1', '8082', '500', 'true', 1, 'Mock Device Local');

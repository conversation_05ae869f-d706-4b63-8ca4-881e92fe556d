package connect

import (
	"context"
	"database/sql"
	"fmt"
	"strings"

	"github.com/redis/go-redis/v9"

	"synapse-its.com/shared/logger"
)

// This type is used as a key in which to store the connection information in
// the context.  It exists simply to make the linters happy.
type contextConnectionsKey string

// This constant is the identifier used in the context to store the connection information.
const ConnectionsKey contextConnectionsKey = "connections"

// Connections holds the vital connections for the microservice (although not
// all connections are necessarily set for all microservices).
type Connections struct {
	Redis     *redis.Client
	Postgres  DatabaseExecutor
	Bigquery  BigQueryExecutorInterface
	Pubsub    PsClient
	Firestore FirestoreClientInterface
}

// Add the Connections to the context.
func WithConnections(ctx context.Context, connections *Connections) context.Context {
	return context.WithValue(ctx, ConnectionsKey, connections)
}

// Get the Connections from the context.
// checkConnections is optional
var GetConnections = func(ctx context.Context, checkConnections ...bool) (*Connections, error) {
	connections, ok := ctx.Value(ConnectionsKey).(*Connections)
	if !ok {
		return nil, fmt.Errorf("connections not found in context")
	}

	// Default to true if no value provided
	shouldValidate := true
	if len(checkConnections) > 0 {
		shouldValidate = checkConnections[0]
	}

	// 	Automatically validates the connections unless a false value is passed into the function.
	// 	Examples of the reasons to pass in false:
	//	1. If you need specific connections to fail for testing
	//	2. If you want to do the checks manually where you are calling this function

	if shouldValidate {
		if connections.Redis == nil {
			return nil, fmt.Errorf("no Redis connection found in context")
		}

		if connections.Postgres == nil {
			return nil, fmt.Errorf("no Postgres connection found in context")
		}

		if connections.Bigquery == nil {
			return nil, fmt.Errorf("no Bigquery connection found in context")
		}

		if connections.Pubsub == nil {
			return nil, fmt.Errorf("no Pubsub connection found in context")
		}

		if connections.Firestore == nil {
			return nil, fmt.Errorf("no Firestore connection found in context")
		}
	}

	return connections, nil
}

// Establish all Connections.
func NewConnections(ctx context.Context) *Connections {
	// Get the release identifier string.
	releaseIdentifier, err := GetReleaseIdentifier()
	if err != nil {
		logger.Fatalf("%v", err)
	}
	logger.Infof("Release Identifier: %s", releaseIdentifier)

	// Connect to Redis.
	redisClient, err := Redis(ctx)
	if err != nil {
		logger.Fatalf("%v", err)
	}

	// Wait until PubSub is set up.
	err = RedisAwait(ctx, redisClient, "ReleaseIdentifier:PubSub", releaseIdentifier)
	if err != nil {
		logger.Fatalf("%v", err)
	}

	// Connect to PubSub.
	pubsubClient, err := PubSub(ctx)
	if err != nil {
		logger.Fatalf("%v", err)
	}

	// Wait until BigQuery is set up.
	err = RedisAwait(ctx, redisClient, "ReleaseIdentifier:BigQuery", releaseIdentifier)
	if err != nil {
		logger.Fatalf("%v", err)
	}

	// Connect to BigQuery.
	bq, err := BigQuery(ctx, nil, nil)
	if err != nil {
		logger.Fatalf("%v", err)
	}

	// Wait until Postgres is set up.
	err = RedisAwait(ctx, redisClient, "ReleaseIdentifier:Postgres", releaseIdentifier)
	if err != nil {
		logger.Fatalf("%v", err)
	}

	// Connect to Postgres.
	pg, err := Postgres(ctx, nil)
	if err != nil {
		logger.Fatalf("%v", err)
	}

	// Connect to Firestore.
	fs, err := Firestore(ctx)
	if err != nil {
		logger.Fatal(err)
	}

	// Put all connections into a single struct.
	return &Connections{
		Redis:     redisClient,
		Pubsub:    pubsubClient,
		Bigquery:  bq,
		Postgres:  pg,
		Firestore: fs,
	}
}

// Closes all Connections (to be used as part of a `defer` statement).
func (c *Connections) Close() {
	if c == nil {
		return
	}
	if c.Redis != nil {
		c.Redis.Close()
	}
	if c.Postgres != nil {
		c.Postgres.Close()
	}
	if c.Bigquery != nil {
		c.Bigquery.Close()
	}
	if c.Pubsub != nil {
		c.Pubsub.Close()
	}
	if c.Firestore != nil {
		c.Firestore.Close()
	}
}

// DatabaseExecutor defines an interface for running queries safely.
type DatabaseExecutor interface {
	// Exec executes a query that does not return rows.
	Exec(query string, args ...interface{}) (sql.Result, error)
	// ExecMultiple executes a string containing multiple SQL statements.
	ExecMultiple(queries string) error
	// QueryGeneric executes a query and returns the results as a slice of maps.
	QueryGeneric(query string, args ...interface{}) ([]map[string]interface{}, error)
	// QueryGenericSlice executes a query and adds results into dest slice
	QueryGenericSlice(dest interface{}, query string, args ...interface{}) error
	// QueryRow executes a query and returns the first row as a map.
	QueryRow(query string, args ...interface{}) (map[string]interface{}, error)
	// QueryRowStruct executes a query and adds the first row into dest struct
	QueryRowStruct(dest interface{}, query string, args ...interface{}) error
	// EscapeIdentifier safely escapes table and column names.
	EscapeIdentifier(identifier string) string
	// Replace the "<T>" placeholder with the namespace of the table.
	ReplaceNamespace(query string) string
	// Close errors
	Close() error
}

// DatabaseConfig holds schema and environment settings.
type DatabaseConfig struct {
	DBName      string
	Environment string
	Namespace   string
}

// Split (potentially) multiple SQL statements into an array of individual SQL statements.
func SplitSQLStatements(sqlStr string) []string {
	var stmts []string
	var sb strings.Builder

	inSingleQuote := false
	inDoubleQuote := false
	inLineComment := false
	inBlockComment := false

	// Loop over each byte (character) in the SQL string.
	for i := 0; i < len(sqlStr); i++ {
		c := sqlStr[i]
		var next byte
		if i+1 < len(sqlStr) {
			next = sqlStr[i+1]
		}

		// Handle line comments (e.g., -- comment)
		if inLineComment {
			sb.WriteByte(c)
			if c == '\n' {
				inLineComment = false
			}
			continue
		}

		// Handle block comments (e.g., /* comment */)
		if inBlockComment {
			sb.WriteByte(c)
			if c == '*' && next == '/' {
				sb.WriteByte(next)
				i++ // skip the '/'
				inBlockComment = false
			}
			continue
		}

		// Handle string literals (single quotes)
		if inSingleQuote {
			sb.WriteByte(c)
			if c == '\'' {
				// Check for escaped single quote (e.g., '')
				if next == '\'' {
					sb.WriteByte(next)
					i++ // skip escaped quote
				} else {
					inSingleQuote = false
				}
			}
			continue
		}

		// Handle double-quoted identifiers or literals.
		if inDoubleQuote {
			sb.WriteByte(c)
			if c == '"' {
				// Check for escaped double quotes ("")
				if next == '"' {
					sb.WriteByte(next)
					i++
				} else {
					inDoubleQuote = false
				}
			}
			continue
		}

		// Not in any string or comment.
		// Check if starting a line comment.
		if c == '-' && next == '-' {
			inLineComment = true
			sb.WriteByte(c)
			sb.WriteByte(next)
			i++ // skip next character
			continue
		}
		// Check if starting a block comment.
		if c == '/' && next == '*' {
			inBlockComment = true
			sb.WriteByte(c)
			sb.WriteByte(next)
			i++
			continue
		}
		// Check for start of single-quoted string.
		if c == '\'' {
			inSingleQuote = true
			sb.WriteByte(c)
			continue
		}
		// Check for start of double-quoted string.
		if c == '"' {
			inDoubleQuote = true
			sb.WriteByte(c)
			continue
		}

		// Check for statement delimiter.
		if c == ';' {
			// End of a statement.
			stmt := strings.TrimSpace(sb.String())
			if stmt != "" {
				stmts = append(stmts, stmt)
			}
			sb.Reset()
			continue
		}

		// Append the current character.
		sb.WriteByte(c)
	}

	// Add any remaining statement.
	if stmt := strings.TrimSpace(sb.String()); stmt != "" {
		stmts = append(stmts, stmt)
	}

	return stmts
}

// getColumn is a generic helper to extract a value of type T from a map.
func GetColumn[T any](m map[string]interface{}, key string) (T, error) {
	var zero T
	value, ok := m[key]
	if !ok {
		return zero, fmt.Errorf("expected column %q to be present", key)
	}
	typed, ok := value.(T)
	if !ok {
		return zero, fmt.Errorf("expected column %q to be of type %T, got %T", key, zero, value)
	}
	return typed, nil
}

// This is a helper function which combines the namespace and the table name.
// It exists so that there is a single place in which this pattern is defined.
func CombineTableNamespace(namespace string, tableName string) string {
	return namespace + "__" + tableName
}

package etl

import (
	"bytes"
	"encoding/base64"
	"fmt"
	"io"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/schemas"
	"synapse-its.com/testing/utils"
)

// This tests the gateway/raw endpoint.
func TestGatewayIngestEndpoint_rmsData(t *testing.T) {
	var (
		gatewayDeviceID = "d5c29fc65d7dc9e0798f68e4f4b982853587acfb5686e981c517849973198e9f"
		messageType     = "rmsData"
		apiKey          = "qiLkND6fYR2zVIQbgifvR2VbyOStT6qt6OgzPZTd"
		tz              = "America/Chicago"
	)

	assert := assert.New(t)

	const messageVersion = "v1"

	// The base64 encoded protobuf
	const base64payload = `CosBCiBmZGYxNjRlNjQ4OTk0NzI1YmZhZTUxNDFkNzg4ODdkNBJnkzcDAVkWAQB1PAAAAAAAIgAAAABf3RdZFBMJI4mBAQABdHV1AHIAc3R0dHRycwlzcnIJc3V1AAAAAAAABwcHBwcHAAAHB3V1BwcHBwAABwcHB3MHAAB0BwAAAAAAAAAAAAAAAMEAFQqOAQogM2ZiZTYwMTg5ZWVmNGJiNmFiOWMwMzY2M2NjN2IzYmESapM4CwF1AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAP//AACCPBB4iSlVGQMEJAB4CQkJCAkJCQkJCQkICQoJCQkICQgEBAQFCQkJCAgJCQl4d3d4d3d3d3h3d3h3eHd3AAAAAP4AAAAAADEKwwIKIDQyNmIwOTE5ODEyYjQ5MGQ4YjQ4YjBhYzc4OGI5NzEyEp4Ck0EzUiIAAAAAAAAAM/UAAAAAAACICgAAAAAAAAAAAAAAAAAA+wB5eXh4eXp5eXoAAAAAAAAAAHl5AQF4eAEBeQF5AXl5eXoAAAAAAAAAAAAAAAAAAAAAAgIBAQICAQEAAAAAAgICAQAAAAAAAAAAAAAAAAAAAAACAgF4AgIBeQF5AXkCAQIBAAAAAAAAAAAAAAAAAgICAgEBAAABAQAAAQABAAEBAQEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABAAAAAQABAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAgxIAIEJF5ffgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAP8AegAAeg==`

	payload, _ := base64.StdEncoding.DecodeString(base64payload)

	// Wait for everything to be up and running.
	connections := connect.NewConnections(t.Context())
	bq := connections.Bigquery
	assert.NoError(utils.AwaitBroker(t.Context(), 30*time.Second))

	// Verify that there are no existing records in the database.
	query := fmt.Sprintf(`
		SELECT *
		FROM {{%s}}
		WHERE (Data = CAST($1 AS BYTES) OR Data = FROM_BASE64($2)) AND topic LIKE $3`, schemas.T_EdiRawMessages)
	rows := []schemas.EdiRawMessages{}
	err := bq.QueryGenericSlice(&rows, query, base64payload, base64payload, fmt.Sprintf("%%%s%%", messageType))
	assert.NoError(err, "Error querying BigQuery")
	assert.Equal(0, len(rows), "Expected 0 rows in BigQuery")

	// The URL under test
	url := "http://broker:8080/api/v3/gateway/ingest"

	reqBody := bytes.NewReader(payload)

	// Build the request
	req, err := http.NewRequest("POST", url, reqBody)
	if err != nil {
		t.Fatalf("creating request: %v", err)
	}

	// Set headers exactly as in your curl
	req.Header.Set("Content-Type", "application/x-protobuf")
	req.Header.Set("gateway-device-id", gatewayDeviceID)
	req.Header.Set("message-version", messageVersion)
	req.Header.Set("message-type", messageType)
	req.Header.Set("x-api-key", apiKey)
	req.Header.Set("tz", tz)

	// Issue the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		t.Fatalf("making POST to %s: %v", url, err)
	}
	defer resp.Body.Close()

	// Read body for debugging if needed
	bodyBytes, _ := io.ReadAll(resp.Body)

	// Verify status code
	if resp.StatusCode != http.StatusOK {
		t.Fatalf("expected 200 OK, got %d: %s", resp.StatusCode, string(bodyBytes))
	}

	// Wait 5 seconds for the message to be processed.
	time.Sleep(10 * time.Second)

	// Verify that the database record was inserted.
	rows = []schemas.EdiRawMessages{}
	err = bq.QueryGenericSlice(&rows, query, base64payload, base64payload, fmt.Sprintf("%%%s%%", messageType))
	assert.NoError(err, "Error querying BigQuery")
	assert.Equal("broker-gateway-rmsData", rows[0].Topic, "Topic should be correct")
	assert.NotEmpty(rows[0].Data, "Data should not be empty")
	assert.NotEmpty(rows[0].PubsubTimestamp, "PubsubTimestamp should not be empty")
	assert.NotEmpty(rows[0].PubsubID, "PubsubID should not be empty")
}

// This tests the gateway/raw endpoint for faultNotifications
func TestGatewayIngestEndpoint_faultNotification(t *testing.T) {
	var (
		gatewayDeviceID = "d5c29fc65d7dc9e0798f68e4f4b982853587acfb5686e981c517849973198e9f"
		messageType     = "faultNotification"
		apiKey          = "qiLkND6fYR2zVIQbgifvR2VbyOStT6qt6OgzPZTd"
		tz              = "America/Chicago"
	)
	assert := assert.New(t)

	const messageVersion = "v1"

	// The base64 encoded protobuf
	const base64payload = `CosBCiBmZGYxNjRlNjQ4OTk0NzI1YmZhZTUxNDFkNzg4ODdkNBJnkzcDAVkWAQB1PAAAAAAAIgAAAABf3RdZFBMJI4mBAQABdHV1AHIAc3R0dHRycwlzcnIJc3V1AAAAAAAABwcHBwcHAAAHB3V1BwcHBwAABwcHB3MHAAB0BwAAAAAAAAAAAAAAAMEAFQqOAQogM2ZiZTYwMTg5ZWVmNGJiNmFiOWMwMzY2M2NjN2IzYmESapM4CwF1AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAP//AACCPBB4iSlVGQMEJAB4CQkJCAkJCQkJCQkICQoJCQkICQgEBAQFCQkJCAgJCQl4d3d4d3d3d3h3d3h3eHd3AAAAAP4AAAAAADEKwwIKIDQyNmIwOTE5ODEyYjQ5MGQ4YjQ4YjBhYzc4OGI5NzEyEp4Ck0EzUiIAAAAAAAAAM/UAAAAAAACICgAAAAAAAAAAAAAAAAAA+wB5eXh4eXp5eXoAAAAAAAAAAHl5AQF4eAEBeQF5AXl5eXoAAAAAAAAAAAAAAAAAAAAAAgIBAQICAQEAAAAAAgICAQAAAAAAAAAAAAAAAAAAAAACAgF4AgIBeQF5AXkCAQIBAAAAAAAAAAAAAAAAAgICAgEBAAABAQAAAQABAAEBAQEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABAAAAAQABAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAgxIAIEJF5ffgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAP8AegAAeg==`

	payload, _ := base64.StdEncoding.DecodeString(base64payload)

	// Wait for everything to be up and running.
	connections := connect.NewConnections(t.Context())
	bq := connections.Bigquery
	assert.NoError(utils.AwaitBroker(t.Context(), 30*time.Second))

	// Verify that there are no existing records in the database.
	query := fmt.Sprintf(`
		SELECT *
		FROM {{%s}}
		WHERE (Data = CAST($1 AS BYTES) OR Data = FROM_BASE64($2)) AND topic LIKE $3`, schemas.T_EdiRawMessages)
	rows := []schemas.EdiRawMessages{}
	err := bq.QueryGenericSlice(&rows, query, base64payload, base64payload, fmt.Sprintf("%%%s%%", messageType))
	assert.NoError(err, "Error querying BigQuery")
	assert.Equal(0, len(rows), "Expected 0 rows in BigQuery")
	// The URL under test
	url := "http://broker:8080/api/v3/gateway/ingest"

	reqBody := bytes.NewReader(payload)

	// Build the request
	req, err := http.NewRequest("POST", url, reqBody)
	if err != nil {
		t.Fatalf("creating request: %v", err)
	}

	// Set headers exactly as in your curl
	req.Header.Set("Content-Type", "application/x-protobuf")
	req.Header.Set("gateway-device-id", gatewayDeviceID)
	req.Header.Set("message-version", messageVersion)
	req.Header.Set("message-type", messageType)
	req.Header.Set("x-api-key", apiKey)
	req.Header.Set("tz", tz)

	// Issue the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		t.Fatalf("making POST to %s: %v", url, err)
	}
	defer resp.Body.Close()

	// Read body for debugging if needed
	bodyBytes, _ := io.ReadAll(resp.Body)

	// Verify status code
	if resp.StatusCode != http.StatusOK {
		t.Fatalf("expected 200 OK, got %d: %s", resp.StatusCode, string(bodyBytes))
	}

	// Wait 5 seconds for the message to be processed.
	time.Sleep(10 * time.Second)

	// Verify that the database record was inserted.
	rows = []schemas.EdiRawMessages{}
	err = bq.QueryGenericSlice(&rows, query, base64payload, base64payload, fmt.Sprintf("%%%s%%", messageType))
	assert.NoError(err, "Error querying BigQuery")
	assert.Equal(1, len(rows), "Expected 1 row in BigQuery")

	// Validate that the data is populated/correct.
	assert.NotEmpty(rows[0].OrganizationIdentifier, "OrganizationIdentifier should not be empty")
	assert.Equal(gatewayDeviceID, rows[0].SoftwareGatewayIdentifier, "SoftwareGatewayIdentifier should match the header")
	assert.Equal("broker-gateway-faultNotification", rows[0].Topic, "Topic should be correct")
	assert.NotEmpty(rows[0].Data, "Data should not be empty")
	assert.NotEmpty(rows[0].PubsubTimestamp, "PubsubTimestamp should not be empty")
	assert.NotEmpty(rows[0].PubsubID, "PubsubID should not be empty")
}

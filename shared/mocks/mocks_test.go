package mocks

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/mocks/bqexecutor"
	"synapse-its.com/shared/mocks/dbexecutor"
	"synapse-its.com/shared/mocks/pubsub"
)

func TestFakeConns(t *testing.T) {
	// Call FakeConns and verify each field is set correctly.
	conns := FakeConns()

	// 1) Redis client should satisfy Ping returning "PONG".
	redisClient := conns.Redis
	val, err := redisClient.Ping(context.Background()).Result()
	assert.NoError(t, err, "expected no error from <PERSON>()")
	assert.Equal(t, "PONG", val, "expected Ping() to return PONG")

	// 2) Pubsub should be a *pubsub.FakePubsubClient
	psClient := conns.Pubsub
	fc, ok := psClient.(*pubsub.FakePubsubClient)
	assert.True(t, ok, "expected Pubsub to be *FakePubsubClient")
	assert.NotNil(t, fc, "expected FakePubsubClient to be non-nil")

	// 3) Postgres should be *dbexecutor.FakeDBExecutor
	pg := conns.Postgres
	_, ok = pg.(*dbexecutor.FakeDBExecutor)
	assert.True(t, ok, "expected Postgres to be *FakeDBExecutor")

	// 4) Bigquery should be *bqexecutor.FakeBigQueryExecutor
	bq := conns.Bigquery
	_, ok = bq.(*bqexecutor.FakeBigQueryExecutor)
	assert.True(t, ok, "expected Bigquery to be *FakeBigQueryExecutor")
}

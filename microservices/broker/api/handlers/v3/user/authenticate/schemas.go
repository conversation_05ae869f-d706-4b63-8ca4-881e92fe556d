// Package v3 encapsulates logic that is shared between v3 functionalities
package authenticate

// API Response Payloads

type dataTokenPayload struct {
	Token string `json:"token"`
}

// dataUserResponsePayload is the payload that gets hydrated and added to the lambda output data attribute
type dataUserResponsePayload struct {
	User  userDetailRecord `json:"user"`
	Token string           `json:"token"`
}

// userDetail defines the structure of the user record returned to the API Gateway on a successful authentication.
type userDetailRecord struct {
	UserID         int64  `json:"user_id"`
	Username       string `json:"username"`
	OrganizationID string `json:"organization_id"`
	APIKey         string `json:"api_key"`
	Role           string `json:"role"`
	// TODO: Find out if this can be removed (was from legacy code)
	// Firstname   string `json:"first_name"`
	// Lastname    string `json:"last_name"`
	// Email       string `json:"email"`
	// Mobile      string `json:"mobile"`
	// LastLogin   string `json:"last_login"`
	// Description string `json:"description"`
}

// Database Structs

// dbUser represents a user record from the database
type dbUser struct {
	Id                      int64  `db:"id"`
	UserName                string `db:"username"`
	TokenDurationHours      int64  `db:"tokendurationhours"`
	OrganizationIdentifier  string `db:"organizationidentifier"`
	RoleId                  int64  `db:"roleid"`
	APIKey                  string `db:"apikey"`
	WebEnabled              int64  `db:"webenabled"`
	WebTokenDurationSeconds int64  `db:"webtokendurationseconds"`
	ExpirationUTC           string `db:"expirationutc"`
	Password                string `db:"password"`
	IsEnabled               int64  `db:"isenabled"`
	FailedLoginAttempts     int64  `db:"failedloginattempts"`
}

// dbSoftwareGateway represents a software gateway record from the database
type dbSoftwareGateway struct {
	SoftwareGatewayIdentifier string `db:"softwaregatewayidentifier"`
}

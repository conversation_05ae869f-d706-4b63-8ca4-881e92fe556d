syntax = "proto3";

package gateway.v1;

option go_package = "bitbucket.org/synapse-its/protobuf-schemas/protomessages/gateway/v1;gatewayv1";

// DeviceLogs represents the overall body payload.
message DeviceLogs {

  // Unique identifier for the device.
  string device_id = 1;

  // Repeated list of all logs gathered by gateway.
  repeated LogEntry logs = 2;
}

// LogEntry represents an individual log type.
message LogEntry {

  // Type of log.
  string log_type = 1;

  // Log messages for given type of log.
  // May contain multiple messages such as in the case of Fault Singal Sequence Logs.
  repeated bytes message = 2;
}

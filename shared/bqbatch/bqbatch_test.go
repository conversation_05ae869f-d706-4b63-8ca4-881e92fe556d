package bqbatch

import (
	"context"
	"reflect"
	"testing"

	"cloud.google.com/go/bigquery"
	"github.com/stretchr/testify/assert"

	"synapse-its.com/shared/mocks/bqexecutor"
)

// Validate that the default table/struct pairs are valid.
func TestDefaultTableStructPairs(t *testing.T) {
	t.<PERSON>()
	assert := assert.New(t)

	// Check that the default table/struct pairs are valid.
	for _, q := range queues {
		assert.NotEmpty(q.table, "Table name should not be empty")
		_, _, err := getValidType(q.rowExample)
		assert.NoError(err, "Default table/struct pairs should be valid")
	}
}

// Verify that the creation of the default batcher succeeds.
func TestDefaultBatcher(t *testing.T) {
	t.<PERSON>llel()
	_, err := newDefault(&bqexecutor.FakeBigQueryExecutor{
		Ctx: context.Background(),
	}, nil)
	assert.NoError(t, err, "newDefault should not return an error")
}

func TestNewCreatesExpectedQueues(t *testing.T) {
	t.<PERSON>()
	exec := &bqexecutor.FakeBigQueryExecutor{Ctx: context.Background()}

	b := New(exec, nil)
	// Ensure returned type is our concrete *batcher
	bb, ok := b.(*batcher)
	if !ok {
		t.Fatalf("New() returned %T, want *batcher", b)
	}

	// Verify the number of queues matches the configs
	if got, want := len(bb.queues), len(bb.cfgs); got != want {
		t.Errorf("len(queues) = %d; want %d", got, want)
	}

	// Check each queue's table name and config
	for name, cfg := range bb.cfgs {
		q, exists := bb.queues[name]
		if !exists {
			t.Errorf("queue for %q missing", name)
			continue
		}
		if q.table != name {
			t.Errorf("q.table = %q; want %q", q.table, name)
		}
		if !reflect.DeepEqual(q.cfg, cfg) {
			t.Errorf("q.cfg = %+v; want %+v", q.cfg, cfg)
		}
	}
}

func TestExtraShutdown(t *testing.T) {
	t.Parallel()
	exec := &bqexecutor.FakeBigQueryExecutor{Ctx: context.Background()}

	b := New(exec, nil)
	// Ensure returned type is our concrete *batcher
	bb, ok := b.(*batcher)
	if !ok {
		t.Fatalf("New() returned %T, want *batcher", b)
	}

	assert.NoError(t, bb.Shutdown(), "Shutdown should not return an error on first call")
	assert.ErrorIs(t, ErrBatcherIsShutDown, bb.Shutdown(), "Shutdown should return an error on second call")
}

// fakeBatcher just implements the interface; methods never actually run.
type fakeBatcher struct{}

// LoadBatch implements Batcher.
func (f *fakeBatcher) LoadBatch(batch FailedBatch) error {
	panic("unimplemented")
}

func (f *fakeBatcher) Register(_ any, _ string, _ QueueConfig) error {
	return nil
}
func (f *fakeBatcher) Add(_ any) error           { return nil }
func (f *fakeBatcher) Shutdown() error           { return nil }
func (f *fakeBatcher) AddTest(_ BatchTest) error { return nil }

func TestWithBatch_GetBatch_Success(t *testing.T) {
	t.Parallel()
	assert := assert.New(t)

	// No batch was ever stored --> ErrBatchContext
	_, err := GetBatch(context.Background())
	assert.ErrorIs(err, ErrBatchContext)

	// Create a fake Batch and stick it in a context
	fb := &fakeBatcher{}
	ctx := context.Background()
	ctx = WithBatch(ctx, fb)

	// Retrieving it should succeed and give the same pointer back
	got, err := GetBatch(ctx)
	assert.NoError(err)
	assert.Same(fb, got)
}

// getValidType tests

// Example struct that should produce a valid schema.
type ValidStruct struct {
	A int
	B string
}

// Pointer to a struct with an unsupported field type.
type BadStruct struct {
	F func()
}

func TestGetValidType_TableDriven(t *testing.T) {
	tests := []struct {
		name         string
		input        any
		expectType   reflect.Type
		expectSchema bool  // whether a non-nil schema is expected
		expectErr    error // either bqbatch.ErrNotStruct or non-nil from InferSchema
	}{
		{
			name:       "nil input",
			input:      nil,
			expectType: nil,
			expectErr:  ErrNotStruct,
		},
		{
			name:       "non-struct input (int)",
			input:      42,
			expectType: nil,
			expectErr:  ErrNotStruct,
		},
		{
			name:         "valid struct value",
			input:        ValidStruct{A: 1, B: "x"},
			expectType:   reflect.TypeOf(ValidStruct{}),
			expectSchema: true,
			expectErr:    nil,
		},
		{
			name:         "pointer to valid struct",
			input:        &ValidStruct{A: 2, B: "y"},
			expectType:   reflect.TypeOf(ValidStruct{}),
			expectSchema: true,
			expectErr:    nil,
		},
		{
			name:       "struct with unsupported field",
			input:      BadStruct{F: func() {}},
			expectType: nil,
			// The error will come from bigquery.InferSchema, not ErrNotStruct
			expectErr: func() error {
				_, err := bigquery.InferSchema(BadStruct{})
				return err
			}(),
		},
	}

	for _, tc := range tests {
		tc := tc // capture range variable
		t.Run(tc.name, func(t *testing.T) {
			gotType, gotSchema, gotErr := getValidType(tc.input)

			// Check error expectation
			if tc.expectErr != nil {
				assert.Error(t, gotErr)
				// For ErrNotStruct cases, exactly match
				if tc.expectErr == ErrNotStruct {
					assert.Equal(t, ErrNotStruct, gotErr)
				} else {
					// Otherwise just ensure the error strings match
					assert.EqualError(t, gotErr, tc.expectErr.Error())
				}
				assert.Nil(t, gotType)
				assert.Nil(t, gotSchema)
				return
			}
			assert.NoError(t, gotErr)

			// Type should match expected reflect.Type
			assert.Equal(t, tc.expectType, gotType)

			// Schema should be non-nil if expected
			if tc.expectSchema {
				assert.NotNil(t, gotSchema)
				// Verify schema has fields "A" and "B"
				fieldNames := []string{}
				for _, f := range gotSchema {
					fieldNames = append(fieldNames, f.Name)
				}
				assert.Contains(t, fieldNames, "A")
				assert.Contains(t, fieldNames, "B")
			} else {
				assert.Nil(t, gotSchema)
			}
		})
	}
}

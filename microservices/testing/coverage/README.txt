This file exists just to ensure that the directory is tracked by git.

This directory serves to hold all of the code coverage tests that are created
and later combined by the unit, integration, and end-to-end tests.  A quick
explanation of the tests are as follows:

Unit tests are tests that verify the functionality of a single function.  They
are self-contained and do not rely on any other microservices to exist.  They
use mocks to test functionality when needed.

Integration tests are tests that require the existence of another microservice
or service in order to function.  An example of this is the `schema_mgmt`
tests, because they require an actual database to exist in order to test the
migration logic.

End-to-end tests are tests that require the entire infrastructure to be set up
in order to run.  Examples of this type of testing are workflows such as
login -> data ingest -> data fetch, which relies on many different services
and microservices to exist and be properly functioning.

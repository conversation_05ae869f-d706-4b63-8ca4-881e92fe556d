package integration

import (
	"encoding/json"
	"testing"
	"time"

	edidevice "synapse-its.com/shared/devices"
	edihelper "synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/logger"
	"synapse-its.com/shared/pubsubdata"
)

// TODO: Chnage the way we populate msg from raw hex to from a file.
// File directory is edi_device_files
func TestGetDeviceModel_RMSStatusFromFile(t *testing.T) {
	// This is the exact 106-byte RMSStatus payload from
	msg := []byte{
		0x93, 0x38, 0x0b, 0x01, 0x75, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x11, 0x00, 0x00, 0x00, 0xee, 0xff,
		0x00, 0x00, 0x82, 0x3c, 0x10, 0x77, 0x89, 0x57, 0x55, 0x19, 0x03, 0x04, 0x24, 0x00, 0x78, 0x09,
		0x08, 0x09, 0x08, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x77, 0x09, 0x0a, 0x09, 0x77, 0x09,
		0x08, 0x09, 0x08, 0x04, 0x04, 0x04, 0x04, 0x09, 0x09, 0x09, 0x08, 0x08, 0x09, 0x09, 0x09, 0x78,
		0x77, 0x77, 0x78, 0x77, 0x77, 0x77, 0x76, 0x77, 0x77, 0x77, 0x08, 0x77, 0x77, 0x77, 0x08, 0x00,
		0x00, 0x00, 0x00, 0xfe, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0b,
	}

	// We'll pretend we got this from the adapter's header:
	httpHdr := &pubsubdata.HeaderDetails{
		GatewayTimezone: "America/Chicago",
	}

	headerBytes, _ := edihelper.GetHeaderBytesFromByteMsg(msg)
	headerDetails, _ := edihelper.ParseHeaderBytesIntoStruct(headerBytes)
	// Get the device implementation:
	dev, err := edidevice.GetDeviceModel(*headerDetails)
	if err != nil {
		t.Fatal("edidevice.GetDeviceModel returned nil; expected an EDIMMU216LE device")
	}

	// Call its RMSStatus()
	rec, err := dev.RMSStatus(httpHdr, msg, headerDetails)
	if err != nil {
		t.Fatalf("RMSStatus returned error: %v", err)
	}

	// We know the BCD timestamp in the capture is:
	//   month=0x04, day=0x03, year=0x24 -> 2024-04-03
	//   hour=0x19 -> 19:00, minute=0x55, second=0x57
	// in America/Chicago this local time corresponds to UTC+5 -> 2024-04-04T00:55:57Z.
	want := time.Date(2024, 4, 4, 0, 55, 57, 0, time.UTC)
	if !rec.MonitorTime.Equal(want) {
		t.Errorf("MonitorTime = %v; want %v", rec.MonitorTime, want)
	}

	// And since byte 7 of the payload is 0x00, we expect no fault:
	if rec.IsFaulted {
		t.Error("IsFaulted = true; want false")
	}
	if rec.Fault != "No Fault" {
		t.Errorf("Fault = %q; want %q", rec.Fault, "No Fault")
	}
	b, _ := json.MarshalIndent(rec, "", "  ")
	logger.Debugf("response data : %v", string(b))
}

func TestGetDeviceModel_RMSEngineDataFromFile(t *testing.T) {
	msg := []byte{
		0x91, 0x38, 0x0b, 0x01, 0x75, 0x00, 0x00, 0x05, 0x31, 0x7F, // from *-request2*
		0x93, 0x38, 0x0b, 0x01, 0x75, 0x00, 0x00, 0x04, 0x01, 0xAE, // from *-request1*
	}

	httpHdr := &pubsubdata.HeaderDetails{GatewayTimezone: "UTC"}

	headerBytes, _ := edihelper.GetHeaderBytesFromByteMsg(msg)
	headerDetails, _ := edihelper.ParseHeaderBytesIntoStruct(headerBytes)
	// Get the device implementation:
	dev, err := edidevice.GetDeviceModel(*headerDetails)
	if err != nil {
		t.Fatal("edidevice.GetDeviceModel returned nil; expected an EDIMMU216LE device")
	}

	rec, err := dev.RMSEngineData(httpHdr, msg, headerDetails)
	if err != nil {
		t.Fatalf("expected no errors, got %v", err)
	}
	if rec.EngineVersion != 49 && rec.EngineRevision != 1 {
		t.Fatalf("expected EngineVersion = 49 got(%v) and EngineRevision = 41 got (%v)", rec.EngineVersion, rec.EngineRevision)
	}
	b, _ := json.MarshalIndent(rec, "", "  ")
	logger.Debugf("response data : %v", string(b))
}

func TestGetDeviceModel_MonitorIDandNameFromFile(t *testing.T) {
	msg := []byte{
		0x97, 0x38, 0x0B, 0x01, 0x75, 0xE8, 0x03,
		0x57, 0x65, 0x73, 0x74, 0x68, 0x65, 0x69, 0x6D, 0x65, 0x72,
		0x20, 0x52, 0x64, 0x20, 0x40, 0x20, 0x53, 0x61, 0x67, 0x65, 0x20, 0x52, 0x64,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFB,
	}

	httpHdr := &pubsubdata.HeaderDetails{GatewayTimezone: "UTC"}

	headerBytes, _ := edihelper.GetHeaderBytesFromByteMsg(msg)
	headerDetails, _ := edihelper.ParseHeaderBytesIntoStruct(headerBytes)
	// Get the device implementation:
	dev, err := edidevice.GetDeviceModel(*headerDetails)
	if err != nil {
		t.Fatal("edidevice.GetDeviceModel returned nil; expected an EDIMMU216LE device")
	}

	rec, err := dev.MonitorIDandName(httpHdr, msg, headerDetails)
	if err != nil {
		t.Fatalf("expected no errors, got %v", err)
	}
	if rec.MonitorId != 1000 {
		t.Fatalf("expected MonitorId = 1000 got(%v) ", rec.MonitorId)
	}
	if rec.MonitorName != "Westheimer Rd @ Sage Rd" {
		t.Fatalf("expected MonitorName = 1000 got(%v) ", rec.MonitorName)
	}
	if rec.DeviceModel != "EDIMMU216LE" {
		t.Fatalf("expected DeviceModel = 1000 got(%v) ", rec.DeviceModel)
	}

	// temp
	b, _ := json.MarshalIndent(rec, "", "  ")
	logger.Debugf("response data : %v", string(b))
}

func TestGetDeviceModel_LogConfigurationFromFile(t *testing.T) {
	msg := []byte{
		0x95, 0x38, 0x0B, 0x01, 0x75, 0x00, 0x00, 0x0A, 0xCE, 0xEB, 0xCC,
		0xAA, 0x38, 0x77, 0x30, 0x55, 0xE0, 0xEE, 0xC0, 0xAA, 0x80, 0xDD,
		0x00, 0x55, 0x00, 0xAA, 0x00, 0x54, 0x00, 0xA8, 0x00, 0x50, 0x00,
		0xA0, 0x00, 0x40, 0x00, 0x80, 0x00, 0x0F, 0x00, 0x00, 0xFF, 0xFF,
		0xFF, 0xF0, 0xFF, 0xFF, 0xFF, 0xF0, 0xFF, 0xFF, 0xFF, 0xF0, 0xFF,
		0xFF, 0x4C, 0x00, 0x00, 0x00, 0x00, 0x01, 0x10, 0x32, 0x22, 0x15,
		0x10, 0x02, 0x24, 0x01, 0x62, 0x1C, 0xCE, 0xEB, 0xCC, 0xAA, 0x38,
		0x77, 0x30, 0x55, 0xE0, 0xEE, 0xC0, 0xAA, 0x80, 0xDD, 0x00, 0x55,
		0x00, 0xAA, 0x00, 0x54, 0x00, 0xA8, 0x00, 0x50, 0x00, 0xA0, 0x00,
		0x40, 0x00, 0x80, 0x00, 0x0F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0x4C,
		0x00, 0x00, 0x00, 0x00, 0x01, 0x10, 0x02, 0x22, 0x15, 0x10, 0x02,
		0x24, 0x04, 0x35, 0x1E, 0xCE, 0xEB, 0xCC, 0xAA, 0x38, 0x77, 0x30,
		0x55, 0xE0, 0xEE, 0xC0, 0xAA, 0x80, 0xDD, 0x00, 0x55, 0x00, 0xAA,
		0x00, 0x54, 0x00, 0xA8, 0x00, 0x50, 0x00, 0xA0, 0x00, 0x40, 0x00,
		0x80, 0x00, 0x0F, 0x00, 0x04, 0xBB, 0xF4, 0xAB, 0x04, 0xAB, 0xF4,
		0xAB, 0x04, 0xAB, 0xF4, 0xAB, 0x04, 0xAB, 0xF4, 0x4C, 0x00, 0x00,
		0x04, 0x30, 0x01, 0x10, 0x51, 0x18, 0x15, 0x10, 0x02, 0x24, 0x01,
		0xA1, 0x03, 0xCE, 0xEB, 0xCC, 0xAA, 0x38, 0x77, 0x30, 0x55, 0xE0,
		0xEE, 0xC0, 0xAA, 0x80, 0xDD, 0x00, 0x55, 0x00, 0xAA, 0x00, 0x54,
		0x00, 0xA8, 0x00, 0x50, 0x00, 0xA0, 0x00, 0x40, 0x00, 0x80, 0x00,
		0x0F, 0x00, 0x04, 0xBB, 0xF4, 0xAB, 0x04, 0xAB, 0xF4, 0xAB, 0x00,
		0xAB, 0xF4, 0xAB, 0x00, 0xAB, 0xF4, 0x4C, 0x00, 0x00, 0x04, 0x30,
		0x01, 0x10, 0x04, 0x07, 0x15, 0x10, 0x02, 0x24, 0x01, 0xB0, 0x30,
		0xCE, 0xEB, 0xCC, 0xAA, 0x38, 0x77, 0x30, 0x55, 0xE0, 0xEE, 0xC0,
		0xAA, 0x80, 0xDD, 0x00, 0x55, 0x00, 0xAA, 0x00, 0x54, 0x00, 0xA8,
		0x00, 0x50, 0x00, 0xA0, 0x00, 0x40, 0x00, 0x80, 0x00, 0x0F, 0x00,
		0x04, 0xBB, 0xF4, 0xAB, 0x04, 0xAB, 0xF4, 0xAB, 0x04, 0xAB, 0xF4,
		0xAB, 0x04, 0xAB, 0xF4, 0x4C, 0x00, 0x00, 0x04, 0x30, 0x01, 0x10,
		0x18, 0x06, 0x08, 0x30, 0x01, 0x24, 0x00, 0xA1, 0x03, 0xCE, 0xEB,
		0xCC, 0xAA, 0x38, 0x77, 0x30, 0x55, 0xE0, 0xEE, 0xC0, 0xAA, 0x80,
		0xDD, 0x00, 0x55, 0x00, 0xAA, 0x00, 0x54, 0x00, 0xA8, 0x00, 0x50,
		0x00, 0xA0, 0x00, 0x40, 0x00, 0x80, 0x00, 0x0F, 0x00, 0x04, 0xBB,
		0xF4, 0xAB, 0x04, 0xAB, 0xF4, 0xAB, 0x04, 0xAB, 0xF4, 0xAB, 0x04,
		0xAB, 0xF4, 0x4C, 0x00, 0x00, 0x04, 0x30, 0x00, 0x10, 0x36, 0x05,
		0x08, 0x30, 0x01, 0x24, 0x00, 0xA0, 0xFF, 0xFE, 0xFF, 0xDC, 0xAF,
		0xF8, 0xFF, 0x70, 0x5C, 0xE0, 0xFF, 0xC0, 0xAF, 0x80, 0xFF, 0x00,
		0x5C, 0x00, 0x50, 0x00, 0x50, 0x00, 0xF0, 0x00, 0xF0, 0x00, 0xA0,
		0x00, 0x40, 0x00, 0x80, 0x00, 0xF0, 0x00, 0x04, 0xBB, 0xF4, 0xAB,
		0x04, 0xAB, 0xF4, 0xAB, 0x04, 0xAB, 0xF4, 0xAB, 0x04, 0xAB, 0xF4,
		0x45, 0x00, 0x00, 0x04, 0x30, 0x07, 0x10, 0x37, 0x44, 0x00, 0x17,
		0x01, 0x24, 0x01, 0x4E, 0x08, 0xFE, 0xFF, 0xDC, 0xAF, 0xF8, 0xFF,
		0x70, 0x5C, 0xE0, 0xFF, 0xC0, 0xAF, 0x80, 0xFF, 0x00, 0x5C, 0x00,
		0x50, 0x00, 0x50, 0x00, 0xF0, 0x00, 0xF0, 0x00, 0xA0, 0x00, 0x40,
		0x00, 0x80, 0x00, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0x45, 0x00,
		0x00, 0x04, 0x30, 0x07, 0x10, 0x16, 0x36, 0x00, 0x17, 0x01, 0x24,
		0x01, 0x9B, 0x95, 0xFE, 0xFF, 0xDC, 0xAF, 0xF8, 0xFF, 0x70, 0x5C,
		0xE0, 0xFF, 0xC0, 0xAF, 0x80, 0xFF, 0x00, 0x5C, 0x00, 0x50, 0x00,
		0x50, 0x00, 0xF0, 0x00, 0xF0, 0x00, 0xA0, 0x00, 0x40, 0x00, 0x80,
		0x00, 0xF0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0x45, 0x00, 0x00, 0x04,
		0x00, 0x07, 0x10, 0x09, 0x30, 0x00, 0x17, 0x01, 0x24, 0x01, 0xDB,
		0x91, 0xFE, 0xFF, 0xDC, 0xAF, 0xF8, 0xFF, 0x70, 0x5C, 0xE0, 0xFF,
		0xC0, 0xAF, 0x80, 0xFF, 0x00, 0x5C, 0x00, 0x50, 0x00, 0x50, 0x00,
		0xF0, 0x00, 0xF0, 0x00, 0xA0, 0x00, 0x40, 0x00, 0x80, 0x00, 0xF0,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0xFF, 0xFF, 0x45, 0x00, 0x00, 0x00, 0x00, 0x07,
		0x10, 0x06, 0x25, 0x00, 0x17, 0x01, 0x24, 0x04, 0xDA, 0x15, 0xFF,
	}

	httpHdr := &pubsubdata.HeaderDetails{GatewayTimezone: "UTC"}

	headerBytes, _ := edihelper.GetHeaderBytesFromByteMsg(msg)
	headerDetails, _ := edihelper.ParseHeaderBytesIntoStruct(headerBytes)
	// Get the device implementation:
	dev, err := edidevice.GetDeviceModel(*headerDetails)
	if err != nil {
		t.Fatal("edidevice.GetDeviceModel returned nil; expected an EDIMMU216LE device")
	}

	rec, err := dev.LogConfiguration(httpHdr, msg, headerDetails)
	if err != nil {
		t.Fatalf("expected no errors, got %v", err)
	}
	if rec.Record[0].Ch01Permissives[0] != "5" {
		t.Fatalf("expected Record[0].Ch01Permissives[0] = 5 got(%v) ", rec.Record[0].Ch01Permissives[0])
	}
	if rec.Record[0].FieldCheckEnableRed[3] != true {
		t.Fatalf("expected Record[0].FieldCheckEnableRed[3] to be true got(%v) ", rec.Record[0].FieldCheckEnableRed[3])
	}
	if rec.Record[0].Pplt5Suppression != "PPLT5 Arrow Suppression = <none>" {
		t.Fatalf("expected rec.Record[0].Pplt5Suppression = 'PPLT5 Arrow Suppression = <none>' got(%v) ", rec.Record[0].Pplt5Suppression)
	}
	if rec.Record[0].X24VLatchEnable != false {
		t.Fatalf("expected Record[0].X24VLatchEnable to be false got(%v) ", rec.Record[0].X24VLatchEnable)
	}

	// temp
	b, _ := json.MarshalIndent(rec, "", "  ")
	logger.Debugf("response data : %v", string(b))
}

func TestGetDeviceModel_LogFaultSignalSequenceFromFile(t *testing.T) {
	msg := []byte{
		0x9C, 0x38, 0x0B, 0x01, 0x75, 0x00, 0x00, 0x05, 0x48, 0x3C, 0x4C, 0x50, 0x22, 0x00, 0x00, 0x00,
		0xDD, 0xFF, 0x00, 0x00, 0x80, 0x79, 0x48, 0xF8, 0x22, 0x00, 0x00, 0x00, 0xDD, 0xFF, 0x00, 0x00,
		0x80, 0x79, 0x48, 0xEE, 0x22, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0x00, 0x00, 0x80, 0x7A, 0x48, 0x30,
		0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0x00, 0x00, 0x80, 0x79, 0x48, 0x26, 0x00, 0x00, 0x11, 0x00,
		0xFF, 0xFF, 0x00, 0x00, 0x80, 0x7A, 0x45, 0xD8, 0x00, 0x00, 0x11, 0x00, 0xEE, 0xFF, 0x00, 0x00,
		0x80, 0x79, 0x45, 0xCE, 0x11, 0x00, 0x11, 0x00, 0xEE, 0xFF, 0x00, 0x00, 0x80, 0x79, 0x3E, 0x08,
		0x11, 0x00, 0x00, 0x00, 0xEE, 0xFF, 0x00, 0x00, 0x80, 0x7A, 0x3D, 0xFE, 0x11, 0x00, 0x00, 0x00,
		0xFF, 0xFF, 0x00, 0x00, 0x80, 0x7A, 0x3D, 0x40, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0x00, 0x00,
		0x80, 0x7A, 0x3D, 0x36, 0x00, 0x00, 0x88, 0x00, 0xFF, 0xFF, 0x00, 0x00, 0x80, 0x79, 0x3A, 0xE8,
		0x00, 0x00, 0x88, 0x00, 0x77, 0xFF, 0x00, 0x00, 0x80, 0x79, 0x3A, 0xDE, 0x88, 0x00, 0x88, 0x00,
		0x77, 0xFF, 0x00, 0x00, 0x80, 0x79, 0x2B, 0x48, 0x88, 0x00, 0x00, 0x00, 0x77, 0xFF, 0x00, 0x00,
		0x80, 0x79, 0x2B, 0x3E, 0x88, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0x00, 0x00, 0x80, 0x79, 0x2A, 0x80,
		0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0x00, 0x00, 0x80, 0x79, 0x2A, 0x76, 0x00, 0x00, 0x44, 0x00,
		0xFF, 0xFF, 0x00, 0x00, 0x80, 0x79, 0x28, 0x28, 0x00, 0x00, 0x44, 0x00, 0xBB, 0xFF, 0x00, 0x00,
		0x80, 0x78, 0x28, 0x1E, 0x44, 0x00, 0x44, 0x00, 0xBB, 0xFF, 0x00, 0x00, 0x80, 0x79, 0x20, 0x58,
		0x44, 0x00, 0x00, 0x00, 0xBB, 0xFF, 0x00, 0x00, 0x80, 0x79, 0x20, 0x4E, 0x44, 0x00, 0x00, 0x00,
		0xFF, 0xFF, 0x00, 0x00, 0x80, 0x7A, 0x1F, 0x90, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0x00, 0x00,
		0x80, 0x79, 0x1F, 0x86, 0x00, 0x00, 0x22, 0x00, 0xFF, 0xFF, 0x00, 0x00, 0x80, 0x7A, 0x1D, 0x38,
		0x00, 0x00, 0x22, 0x00, 0xDD, 0xFF, 0x00, 0x00, 0x80, 0x79, 0x1D, 0x2E, 0x22, 0x00, 0x22, 0x00,
		0xDD, 0xFF, 0x00, 0x00, 0x80, 0x7A, 0x0D, 0x98, 0x22, 0x00, 0x00, 0x00, 0xDD, 0xFF, 0x00, 0x00,
		0x80, 0x7A, 0x0D, 0x8E, 0x22, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0x00, 0x00, 0x80, 0x7A, 0x0C, 0xD0,
		0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0x00, 0x00, 0x80, 0x78, 0x0C, 0xC6, 0x00, 0x00, 0x11, 0x00,
		0xFF, 0xFF, 0x00, 0x00, 0x80, 0x7A, 0x0A, 0x78, 0x00, 0x00, 0x11, 0x00, 0xEE, 0xFF, 0x00, 0x00,
		0x80, 0x78, 0x0A, 0x6E, 0x11, 0x00, 0x11, 0x00, 0xEE, 0xFF, 0x00, 0x00, 0x80, 0x79, 0x02, 0xA8,
		0x11, 0x00, 0x00, 0x00, 0xEE, 0xFF, 0x00, 0x00, 0x80, 0x7A, 0x02, 0x9E, 0x11, 0x00, 0x00, 0x00,
		0xFF, 0xFF, 0x00, 0x00, 0x80, 0x79, 0x01, 0xE0, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0x00, 0x00,
		0x80, 0x7A, 0x01, 0xD6, 0x00, 0x00, 0x88, 0x00, 0xFF, 0xFF, 0x00, 0x00, 0x80, 0x79, 0x00, 0x00,
		0x00, 0x00, 0x88, 0x00, 0x77, 0xFF, 0x00, 0x00, 0x80, 0x79, 0xFF, 0x82, 0x00, 0x00, 0x88, 0x00,
		0x77, 0xFF, 0x00, 0x00, 0x80, 0x7A, 0xEF, 0xE2, 0x88, 0x00, 0x00, 0x00, 0x77, 0xFF, 0x00, 0x00,
		0x80, 0x79, 0xEF, 0x1A, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0x00, 0x00, 0x80, 0x79, 0xEC, 0xC2,
		0x00, 0x00, 0x44, 0x00, 0xBB, 0xFF, 0x00, 0x00, 0x80, 0x7A, 0xE4, 0xF2, 0x44, 0x00, 0x00, 0x00,
		0xBB, 0xFF, 0x00, 0x00, 0x80, 0x78, 0xE4, 0x2A, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0x00, 0x00,
		0x80, 0x79, 0xE1, 0xD2, 0x00, 0x00, 0x22, 0x00, 0xDD, 0xFF, 0x00, 0x00, 0x80, 0x79, 0xD2, 0x32,
		0x22, 0x00, 0x00, 0x00, 0xDD, 0xFF, 0x00, 0x00, 0x80, 0x78, 0xD1, 0x6A, 0x00, 0x00, 0x00, 0x00,
		0xFF, 0xFF, 0x00, 0x00, 0x80, 0x79, 0xCF, 0x12, 0x00, 0x00, 0x11, 0x00, 0xEE, 0xFF, 0x00, 0x00,
		0x80, 0x78, 0xC7, 0x42, 0x11, 0x00, 0x00, 0x00, 0xEE, 0xFF, 0x00, 0x00, 0x80, 0x78, 0xC6, 0x7A,
		0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0x00, 0x00, 0x80, 0x79, 0xC4, 0x22, 0x00, 0x00, 0x88, 0x00,
		0x77, 0xFF, 0x00, 0x00, 0x80, 0x79, 0xB4, 0x82, 0x88, 0x00, 0x00, 0x00, 0x77, 0xFF, 0x00, 0x00,
		0x80, 0x7A, 0xB3, 0xBA, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0x00, 0x00, 0x80, 0x79, 0xB1, 0x62,
		0x00, 0x00, 0x44, 0x00, 0xBB, 0xFF, 0x00, 0x00, 0x80, 0x79, 0xA9, 0x92, 0x44, 0x00, 0x00, 0x00,
		0xBB, 0xFF, 0x00, 0x00, 0x80, 0x79, 0xA8, 0xCA, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0x00, 0x00,
		0x80, 0x7A, 0xA6, 0x7C, 0x00, 0x00, 0x22, 0x00, 0xDD, 0xFF, 0x00, 0x00, 0x80, 0x7A, 0xA6, 0x72,
		0x22, 0x00, 0x22, 0x00, 0xDD, 0xFF, 0x00, 0x00, 0x80, 0x7A, 0x96, 0xDC, 0x22, 0x00, 0x00, 0x00,
		0xDD, 0xFF, 0x00, 0x00, 0x80, 0x7A, 0x96, 0xD2, 0x22, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0x00, 0x00,
		0x80, 0x7A, 0x96, 0x14, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0x00, 0x00, 0x80, 0x79, 0x96, 0x0A,
		0x00, 0x00, 0x11, 0x00, 0xFF, 0xFF, 0x00, 0x00, 0x80, 0x7A, 0xFC,
	}

	httpHdr := &pubsubdata.HeaderDetails{GatewayTimezone: "UTC"}

	headerBytes, _ := edihelper.GetHeaderBytesFromByteMsg(msg)
	headerDetails, _ := edihelper.ParseHeaderBytesIntoStruct(headerBytes)
	// Get the device implementation:
	dev, err := edidevice.GetDeviceModel(*headerDetails)
	if err != nil {
		t.Fatal("edidevice.GetDeviceModel returned nil; expected an EDIMMU216LE device")
	}

	rec, err := dev.LogFaultSignalSequence(httpHdr, msg, headerDetails)
	if err != nil {
		t.Fatalf("expected no errors, got %v", err)
	}

	if rec.FaultType != "Port 1 Fault" {
		t.Fatalf("expected FaultType = Port 1 Fault got(%v) ", rec.FaultType)
	}
	if rec.Records[1].Timestamp != 435 {
		t.Fatalf("expected Records[0].Timestamp = 435 got(%v) ", rec.Records[0].Timestamp)
	}
	if rec.Records[3].Yellows[0] != true {
		t.Fatalf("expected Records[3].Yellows[0] is true got(%v) ", rec.Records[3].Yellows[0])
	}
	if rec.Records[3].Yellows[1] != false {
		t.Fatalf("expected Records[3].Yellows[1] is false got(%v) ", rec.Records[3].Yellows[1])
	}

	// temp
	b, _ := json.MarshalIndent(rec, "", "  ")
	logger.Debugf("response data : %v", string(b))
}

func TestGetDeviceModel_LogACLineEventFromFile(t *testing.T) {
	msg := []byte{
		0x88, 0x38, 0x0B, 0x01, 0x75, 0x00, 0x00, 0x0A, 0x05, 0x7A, 0x53, 0x10,
		0x22, 0x15, 0x03, 0x24, 0x81, 0x04, 0x28, 0x13, 0x07, 0x12, 0x03, 0x24,
		0x05, 0x79, 0x13, 0x39, 0x14, 0x26, 0x02, 0x24, 0x81, 0x04, 0x48, 0x34,
		0x16, 0x24, 0x02, 0x24, 0x05, 0x76, 0x07, 0x18, 0x15, 0x10, 0x02, 0x24,
		0x81, 0x05, 0x22, 0x08, 0x15, 0x10, 0x02, 0x24, 0x05, 0x76, 0x32, 0x05,
		0x08, 0x30, 0x01, 0x24, 0x81, 0x05, 0x35, 0x04, 0x08, 0x30, 0x01, 0x24,
		0x05, 0x75, 0x40, 0x00, 0x08, 0x30, 0x01, 0x24, 0x81, 0x04, 0x26, 0x47,
		0x01, 0x17, 0x01, 0x24, 0x98,
	}

	httpHdr := &pubsubdata.HeaderDetails{GatewayTimezone: "UTC"}

	headerBytes, _ := edihelper.GetHeaderBytesFromByteMsg(msg)
	headerDetails, _ := edihelper.ParseHeaderBytesIntoStruct(headerBytes)
	// Get the device implementation:
	dev, err := edidevice.GetDeviceModel(*headerDetails)
	if err != nil {
		t.Fatal("edidevice.GetDeviceModel returned nil; expected an EDIMMU216LE device")
	}

	rec, err := dev.LogACLineEvent(httpHdr, msg, headerDetails)
	if err != nil {
		t.Fatalf("expected no errors, got %v", err)
	}
	if rec.VoltageType != 1 {
		t.Fatalf("expected VoltageType = 1 got(%v) ", rec.VoltageType)
	}
	if rec.Records[1].EventType != "Power Down" {
		t.Fatalf("expected Records[1].EventType = Power Down got(%v) ", rec.Records[1].EventType)
	}
	if rec.Records[4].LineVoltageRms != 118 {
		t.Fatalf("expected Records[4].LineVoltageRms = 118 got(%v) ", rec.Records[4].LineVoltageRms)
	}
	if rec.DeviceModel != "EDIMMU216LE" {
		t.Fatalf("expected DeviceModel = EDIMMU216LE got(%v) ", rec.DeviceModel)
	}

	// temp
	b, _ := json.MarshalIndent(rec, "", "  ")
	logger.Debugf("response data : %v", string(b))
}

func TestGetDeviceModel_LogPreviousFailFromFile(t *testing.T) {
	msg := []byte{
		0x94, 0x38, 0x0B, 0x01, 0x75, 0x00, 0x00, 0x11, 0x48, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x22, 0x00, 0x00, 0x00, 0xDD,
		0xFF, 0x00, 0x00, 0x82, 0x3C, 0x10, 0x79, 0x88, 0x03, 0x13, 0x07, 0x12, 0x03, 0x24, 0x00, 0x7A,
		0x09, 0x09, 0x09, 0x08, 0x09, 0x09, 0x09, 0x0A, 0x09, 0x09, 0x7A, 0x09, 0x09, 0x0A, 0x79, 0x09,
		0x09, 0x09, 0x09, 0x08, 0x04, 0x04, 0x04, 0x04, 0x09, 0x09, 0x09, 0x08, 0x08, 0x09, 0x09, 0x09,
		0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x79, 0x78, 0x79, 0x79, 0x09, 0x79, 0x79, 0x79, 0x09, 0x79,
		0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xF0, 0xFF, 0xFF, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0xA5, 0x30, 0x00, 0x00, 0x42, 0x3C, 0x10, 0x78, 0x7C, 0x28, 0x39, 0x14, 0x26, 0x02, 0x24,
		0x00, 0x3C, 0x09, 0x09, 0x09, 0x08, 0x09, 0x09, 0x09, 0x0A, 0x09, 0x09, 0x09, 0x08, 0x09, 0x0A,
		0x09, 0x09, 0x09, 0x08, 0x09, 0x08, 0x04, 0x04, 0x04, 0x05, 0x09, 0x0A, 0x09, 0x08, 0x09, 0x09,
		0x09, 0x09, 0x03, 0x05, 0x74, 0x74, 0x09, 0x09, 0x09, 0x09, 0x79, 0x01, 0x74, 0x08, 0x02, 0x76,
		0x03, 0x76, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xF0, 0xFF, 0xFF, 0x01, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x5A, 0xC0, 0x00, 0x00, 0x02, 0x3C, 0x10, 0x78, 0x7C, 0x22, 0x39, 0x14, 0x26,
		0x02, 0x24, 0x00, 0x00, 0x09, 0x09, 0x09, 0x08, 0x09, 0x09, 0x09, 0x0A, 0x09, 0x09, 0x09, 0x09,
		0x09, 0x0A, 0x09, 0x09, 0x09, 0x08, 0x09, 0x08, 0x04, 0x04, 0x04, 0x04, 0x09, 0x09, 0x09, 0x08,
		0x08, 0x09, 0x09, 0x09, 0x79, 0x79, 0x02, 0x02, 0x09, 0x09, 0x09, 0x09, 0x02, 0x78, 0x01, 0x79,
		0x78, 0x02, 0x79, 0x01, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xF0, 0xFF, 0xFF, 0x48, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x11, 0x00, 0xEE, 0xFF, 0x00, 0x00, 0x82, 0x3C, 0x10, 0x76, 0x85, 0x44, 0x34,
		0x16, 0x24, 0x02, 0x24, 0x00, 0x76, 0x09, 0x09, 0x09, 0x08, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09,
		0x09, 0x08, 0x09, 0x0A, 0x09, 0x09, 0x09, 0x08, 0x09, 0x08, 0x04, 0x04, 0x04, 0x04, 0x09, 0x09,
		0x09, 0x76, 0x08, 0x09, 0x09, 0x76, 0x76, 0x76, 0x75, 0x76, 0x74, 0x75, 0x76, 0x74, 0x76, 0x75,
		0x74, 0x08, 0x75, 0x76, 0x76, 0x08, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xFF, 0xFF, 0xF0, 0xFF, 0xFF,
		0x58, 0x10, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x11, 0x00, 0x00, 0x00, 0xEE, 0xFF, 0x00, 0x00, 0x82, 0x3C, 0x10, 0x75, 0x7F,
		0x39, 0x21, 0x15, 0x10, 0x02, 0x24, 0x00, 0x76, 0x08, 0x08, 0x08, 0x08, 0x08, 0x08, 0x08, 0x09,
		0x09, 0x09, 0x09, 0x75, 0x09, 0x09, 0x09, 0x74, 0x09, 0x08, 0x09, 0x08, 0x04, 0x04, 0x04, 0x04,
		0x09, 0x09, 0x09, 0x00, 0x08, 0x09, 0x09, 0x09, 0x76, 0x75, 0x75, 0x76, 0x74, 0x75, 0x76, 0x74,
		0x76, 0x74, 0x74, 0x00, 0x74, 0x76, 0x75, 0x07, 0x00, 0x00, 0x00, 0x00, 0xBB, 0xF4, 0xAB, 0x04,
		0xAB, 0xF4, 0x58, 0x10, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x11, 0x00, 0x00, 0x00, 0xEE, 0xFF, 0x00, 0x00, 0x82, 0x3C, 0x10,
		0x77, 0x7E, 0x28, 0x20, 0x15, 0x10, 0x02, 0x24, 0x00, 0x77, 0x09, 0x08, 0x08, 0x08, 0x09, 0x09,
		0x09, 0x09, 0x09, 0x09, 0x09, 0x76, 0x09, 0x09, 0x09, 0x76, 0x09, 0x08, 0x08, 0x08, 0x04, 0x04,
		0x04, 0x04, 0x08, 0x09, 0x09, 0x00, 0x08, 0x09, 0x09, 0x09, 0x77, 0x76, 0x76, 0x77, 0x76, 0x76,
		0x76, 0x75, 0x77, 0x76, 0x76, 0x00, 0x76, 0x76, 0x76, 0x08, 0x00, 0x00, 0x00, 0x00, 0xBB, 0xF4,
		0xAB, 0x04, 0xAB, 0xF4, 0x58, 0x10, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x11, 0x00, 0x00, 0x00, 0xEE, 0xFF, 0x00, 0x00, 0x82,
		0x3C, 0x10, 0x75, 0x7D, 0x17, 0x19, 0x15, 0x10, 0x02, 0x24, 0x00, 0x76, 0x09, 0x08, 0x09, 0x08,
		0x08, 0x09, 0x09, 0x09, 0x09, 0x08, 0x09, 0x75, 0x09, 0x09, 0x09, 0x75, 0x09, 0x08, 0x08, 0x08,
		0x04, 0x04, 0x04, 0x04, 0x08, 0x09, 0x08, 0x00, 0x08, 0x09, 0x09, 0x09, 0x76, 0x76, 0x75, 0x76,
		0x74, 0x75, 0x76, 0x74, 0x76, 0x74, 0x74, 0x00, 0x74, 0x76, 0x75, 0x08, 0x00, 0x00, 0x00, 0x00,
		0xBB, 0xF4, 0xAB, 0x04, 0xAB, 0xF4, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x82, 0x3C, 0x10, 0x76, 0x7B, 0x15, 0x18, 0x15, 0x10, 0x02, 0x24, 0x00, 0x77, 0x09, 0x08,
		0x09, 0x08, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x08, 0x09, 0x09, 0x09, 0x09, 0x09, 0x08,
		0x08, 0x08, 0x04, 0x04, 0x04, 0x04, 0x08, 0x09, 0x08, 0x00, 0x08, 0x09, 0x09, 0x09, 0x08, 0x08,
		0x08, 0x08, 0x08, 0x08, 0x08, 0x08, 0x09, 0x08, 0x08, 0x00, 0x08, 0x09, 0x09, 0x08, 0x00, 0x00,
		0x00, 0x00, 0xBB, 0xF4, 0xAB, 0x04, 0xAB, 0xF4, 0x58, 0x10, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x11, 0x00, 0x00, 0x00, 0xEE,
		0xFF, 0x00, 0x00, 0x82, 0x3C, 0x10, 0x76, 0x7F, 0x16, 0x08, 0x15, 0x10, 0x02, 0x24, 0x00, 0x76,
		0x09, 0x08, 0x09, 0x08, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x75, 0x09, 0x0A, 0x09, 0x75,
		0x09, 0x09, 0x09, 0x08, 0x04, 0x04, 0x04, 0x04, 0x08, 0x09, 0x08, 0x00, 0x08, 0x09, 0x09, 0x09,
		0x76, 0x76, 0x75, 0x76, 0x74, 0x75, 0x76, 0x74, 0x76, 0x75, 0x75, 0x00, 0x75, 0x76, 0x76, 0x08,
		0x00, 0x00, 0x00, 0x00, 0xBB, 0xF4, 0xAB, 0x04, 0xAB, 0xF4, 0x58, 0x10, 0x04, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x11, 0x00, 0x00,
		0x00, 0xEE, 0xFF, 0x00, 0x00, 0x82, 0x3C, 0x10, 0x75, 0x7E, 0x12, 0x07, 0x15, 0x10, 0x02, 0x24,
		0x00, 0x76, 0x09, 0x09, 0x09, 0x08, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09, 0x75, 0x09, 0x0A,
		0x09, 0x75, 0x09, 0x09, 0x09, 0x08, 0x04, 0x04, 0x04, 0x05, 0x09, 0x09, 0x08, 0x00, 0x08, 0x09,
		0x09, 0x08, 0x76, 0x76, 0x75, 0x76, 0x74, 0x75, 0x76, 0x74, 0x76, 0x75, 0x74, 0x00, 0x75, 0x76,
		0x76, 0x08, 0x00, 0x00, 0x00, 0x00, 0xBB, 0xF4, 0xAB, 0x04, 0xAB, 0xF4, 0x58, 0x10, 0x04, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x11,
		0x00, 0x00, 0x00, 0xEE, 0xFF, 0x00, 0x00, 0x82, 0x3C, 0x10, 0x76, 0x7D, 0x32, 0x05, 0x15, 0x10,
		0x02, 0x24, 0x00, 0x76, 0x09, 0x09, 0x09, 0x08, 0x09, 0x09, 0x09, 0x0A, 0x09, 0x09, 0x09, 0x75,
		0x09, 0x0A, 0x09, 0x75, 0x09, 0x09, 0x09, 0x08, 0x04, 0x04, 0x04, 0x04, 0x09, 0x09, 0x08, 0x00,
		0x08, 0x09, 0x09, 0x08, 0x76, 0x75, 0x75, 0x76, 0x74, 0x75, 0x76, 0x74, 0x76, 0x75, 0x74, 0x00,
		0x74, 0x76, 0x75, 0x08, 0x00, 0x00, 0x00, 0x00, 0xBB, 0xF4, 0xAB, 0x04, 0xAB, 0xF4, 0x58, 0x10,
		0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x11, 0x00, 0x00, 0x00, 0xEE, 0xFF, 0x00, 0x00, 0x82, 0x3C, 0x10, 0x75, 0x7B, 0x07, 0x58,
		0x14, 0x10, 0x02, 0x24, 0x00, 0x76, 0x09, 0x08, 0x09, 0x08, 0x09, 0x09, 0x09, 0x09, 0x09, 0x09,
		0x09, 0x75, 0x09, 0x0A, 0x09, 0x75, 0x09, 0x09, 0x09, 0x08, 0x04, 0x04, 0x04, 0x04, 0x08, 0x09,
		0x08, 0x00, 0x08, 0x09, 0x09, 0x09, 0x76, 0x75, 0x75, 0x76, 0x74, 0x75, 0x76, 0x74, 0x76, 0x74,
		0x74, 0x00, 0x74, 0x76, 0x75, 0x08, 0x00, 0x00, 0x00, 0x00, 0xBB, 0xF4, 0xAB, 0x04, 0xAB, 0xF4,
		0x58, 0x10, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x11, 0x00, 0x00, 0x00, 0xEE, 0xFF, 0x00, 0x00, 0x82, 0x3C, 0x10, 0x75, 0x78,
		0x47, 0x52, 0x14, 0x10, 0x02, 0x24, 0x00, 0x76, 0x09, 0x08, 0x09, 0x08, 0x09, 0x09, 0x09, 0x09,
		0x09, 0x09, 0x09, 0x75, 0x09, 0x0A, 0x09, 0x75, 0x09, 0x09, 0x09, 0x08, 0x05, 0x04, 0x04, 0x05,
		0x09, 0x09, 0x08, 0x00, 0x08, 0x09, 0x09, 0x08, 0x76, 0x76, 0x75, 0x76, 0x74, 0x74, 0x76, 0x74,
		0x76, 0x74, 0x74, 0x00, 0x74, 0x76, 0x75, 0x08, 0x00, 0x00, 0x00, 0x00, 0xBB, 0xF4, 0xAB, 0x04,
		0xAB, 0xF4, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x88, 0x00, 0x00, 0x0A, 0x67, 0xF5, 0x00, 0x00, 0x82, 0x3C, 0x10,
		0x75, 0x75, 0x18, 0x06, 0x08, 0x30, 0x01, 0x24, 0x00, 0x76, 0x09, 0x09, 0x09, 0x08, 0x09, 0x09,
		0x09, 0x0A, 0x74, 0x09, 0x09, 0x09, 0x75, 0x0A, 0x09, 0x09, 0x09, 0x08, 0x09, 0x08, 0x74, 0x04,
		0x74, 0x04, 0x08, 0x09, 0x08, 0x00, 0x08, 0x09, 0x09, 0x09, 0x76, 0x75, 0x75, 0x76, 0x08, 0x74,
		0x08, 0x74, 0x09, 0x74, 0x74, 0x00, 0x09, 0x76, 0x75, 0x74, 0x00, 0x00, 0x00, 0x00, 0xBB, 0xF4,
		0xAB, 0x04, 0xAB, 0xF4, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x3C, 0x10, 0x75, 0x73, 0x50, 0x00, 0x08, 0x30, 0x01, 0x24, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0xBB, 0xF4, 0xAB, 0x04, 0xAB, 0xF4, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x02, 0x3C, 0x10, 0x75, 0x72, 0x40, 0x00, 0x08, 0x30, 0x01, 0x24, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0xBB, 0xF4, 0xAB, 0x04, 0xAB, 0xF4, 0x48, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x02, 0x3C, 0x10, 0x7A, 0x7B, 0x13, 0x57, 0x00, 0x17, 0x01, 0x24, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
		0x00, 0x00, 0x00, 0x00, 0xBB, 0xF4, 0xAB, 0x04, 0xAB, 0xF4, 0xDC,
	}

	httpHdr := &pubsubdata.HeaderDetails{GatewayTimezone: "UTC"}

	headerBytes, _ := edihelper.GetHeaderBytesFromByteMsg(msg)
	headerDetails, _ := edihelper.ParseHeaderBytesIntoStruct(headerBytes)
	// Get the device implementation:
	dev, err := edidevice.GetDeviceModel(*headerDetails)
	if err != nil {
		t.Fatal("edidevice.GetDeviceModel returned nil; expected an EDIMMU216LE device")
	}

	rec, err := dev.LogPreviousFail(httpHdr, msg, headerDetails)
	if err != nil {
		t.Fatalf("expected no errors, got %v", err)
	}
	if rec.Records[1].Fault != "Program Card Ajar Fault" {
		t.Fatalf("expected Records[1].Fault = Program Card Ajar Fault got(%v) ", rec.Records[1].Fault)
	}
	if rec.Records[2].ChannelRedStatus[0] != false {
		t.Fatalf("expected Records[2].ChannelRedStatus[0] = false got(%v) ", rec.Records[2].ChannelRedStatus[0])
	}
	if rec.Records[2].ChannelRedStatus[1] != true {
		t.Fatalf("expected Records[2].ChannelRedStatus[1] = true got(%v) ", rec.Records[2].ChannelRedStatus[1])
	}
	if rec.DeviceModel != "EDIMMU216LE" {
		t.Fatalf("expected DeviceModel = EDIMMU216LE got(%v) ", rec.DeviceModel)
	}

	// temp
	b, _ := json.MarshalIndent(rec, "", "  ")
	logger.Debugf("response data : %v", string(b))
}

func TestGetDeviceModel_LogMonitorResetFromFile(t *testing.T) {
	msg := []byte{
		0x8A, 0x38, 0x0B, 0x01, 0x75, 0x00, 0x00, 0x11, 0x05, 0x11, 0x22, 0x15, 0x03, 0x24, 0x02, 0x32,
		0x39, 0x14, 0x26, 0x02, 0x24, 0x01, 0x27, 0x39, 0x14, 0x26, 0x02, 0x24, 0x01, 0x22, 0x39, 0x14,
		0x26, 0x02, 0x24, 0x02, 0x31, 0x22, 0x15, 0x10, 0x02, 0x24, 0x01, 0x50, 0x20, 0x15, 0x10, 0x02,
		0x24, 0x01, 0x38, 0x19, 0x15, 0x10, 0x02, 0x24, 0x01, 0x16, 0x18, 0x15, 0x10, 0x02, 0x24, 0x02,
		0x14, 0x18, 0x15, 0x10, 0x02, 0x24, 0x01, 0x26, 0x07, 0x15, 0x10, 0x02, 0x24, 0x01, 0x23, 0x06,
		0x15, 0x10, 0x02, 0x24, 0x01, 0x42, 0x04, 0x15, 0x10, 0x02, 0x24, 0x01, 0x17, 0x57, 0x14, 0x10,
		0x02, 0x24, 0x01, 0x19, 0x06, 0x08, 0x30, 0x01, 0x24, 0x02, 0x17, 0x06, 0x08, 0x30, 0x01, 0x24,
		0x01, 0x49, 0x00, 0x08, 0x30, 0x01, 0x24, 0x01, 0x03, 0x44, 0x01, 0x17, 0x01, 0x24, 0x01, 0xA2,
	}

	httpHdr := &pubsubdata.HeaderDetails{GatewayTimezone: "UTC"}

	headerBytes, _ := edihelper.GetHeaderBytesFromByteMsg(msg)
	headerDetails, _ := edihelper.ParseHeaderBytesIntoStruct(headerBytes)
	// Get the device implementation:
	dev, err := edidevice.GetDeviceModel(*headerDetails)
	if err != nil {
		t.Fatal("edidevice.GetDeviceModel returned nil; expected an EDIMMU216LE device")
	}

	rec, err := dev.LogMonitorReset(httpHdr, msg, headerDetails)
	if err != nil {
		t.Fatalf("expected no errors, got %v", err)
	}
	if rec.Records[0].ResetType != "MONITOR NON-LATCHED FAULT RESET EVENT #" {
		t.Fatalf("expected Records[1].EventType = MONITOR NON-LATCHED FAULT RESET EVENT # got(%v) ", rec.Records[0].ResetType)
	}
	if rec.DeviceModel != "EDIMMU216LE" {
		t.Fatalf("expected DeviceModel = EDIMMU216LE got(%v) ", rec.DeviceModel)
	}

	// temp
	b, _ := json.MarshalIndent(rec, "", "  ")
	logger.Debugf("response data : %v", string(b))
}

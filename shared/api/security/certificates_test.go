package security

import (
	"crypto/ecdsa"
	"crypto/elliptic"
	"crypto/rand"
	"crypto/rsa"
	"crypto/x509"
	"encoding/pem"
	"testing"
)

// Helpers to generate test PEMs:

func generateRSAPrivateKeyPEM(t *testing.T) (*rsa.<PERSON>Key, string) {
	priv, err := rsa.GenerateKey(rand.Reader, 2048)
	if err != nil {
		t.Fatalf("generate RSA key: %v", err)
	}
	der := x509.MarshalPKCS1PrivateKey(priv)
	block := &pem.Block{Type: "RSA PRIVATE KEY", Bytes: der}
	return priv, string(pem.EncodeToMemory(block))
}

func generateRSAPublicKeyPEM(t *testing.T, priv *rsa.PrivateKey) (*rsa.PublicKey, string) {
	der, err := x509.MarshalPKIXPublicKey(&priv.PublicKey)
	if err != nil {
		t.Fatalf("marshal public key: %v", err)
	}
	block := &pem.Block{Type: "PUBLIC KEY", Bytes: der}
	return &priv.<PERSON>Key, string(pem.EncodeToMemory(block))
}

func generateNonRSAPublicKeyPEM(t *testing.T) string {
	ecdsaKey, err := ecdsa.GenerateKey(elliptic.P256(), rand.Reader)
	if err != nil {
		t.Fatalf("generate ECDSA key: %v", err)
	}
	der, err := x509.MarshalPKIXPublicKey(&ecdsaKey.PublicKey)
	if err != nil {
		t.Fatalf("marshal ECDSA public key: %v", err)
	}
	block := &pem.Block{Type: "PUBLIC KEY", Bytes: der}
	return string(pem.EncodeToMemory(block))
}

// Tests for the private‐key parser:

func TestParseRSAPrivateKeyFromPEM(t *testing.T) {
	origKey, origPEM := generateRSAPrivateKeyPEM(t)

	// wrong type and corrupted‐DER variants:
	wrongTypePEM := string(pem.EncodeToMemory(&pem.Block{Type: "PUBLIC KEY", Bytes: []byte("dummy")}))
	corruptDERPEM := string(pem.EncodeToMemory(&pem.Block{Type: "RSA PRIVATE KEY", Bytes: []byte("notDER")}))

	cases := []struct {
		name    string
		input   string
		want    *rsa.PrivateKey
		wantErr bool
	}{
		{"valid PEM", origPEM, origKey, false},
		{"empty input", "", nil, true},
		{"wrong type", wrongTypePEM, nil, true},
		{"corrupted DER", corruptDERPEM, nil, true},
	}

	for _, tc := range cases {
		t.Run(tc.name, func(t *testing.T) {
			got, err := ParseRSAPrivateKeyFromPEM(tc.input)
			if tc.wantErr {
				if err == nil {
					t.Errorf("expected error, got none")
				}
				return
			}
			if err != nil {
				t.Fatalf("unexpected error: %v", err)
			}
			// compare public components to avoid deep reflect on private internals
			if got.PublicKey.N.Cmp(tc.want.PublicKey.N) != 0 || got.PublicKey.E != tc.want.PublicKey.E {
				t.Error("parsed private key does not match original")
			}
		})
	}
}

// Tests for the public‐key parser:

func TestParseRSAPublicKeyFromPEM(t *testing.T) {
	privKey, _ := generateRSAPrivateKeyPEM(t)
	wantPub, pubPEM := generateRSAPublicKeyPEM(t, privKey)

	wrongTypePEM := string(pem.EncodeToMemory(&pem.Block{Type: "RSA PRIVATE KEY", Bytes: []byte("dummy")}))
	corruptDERPEM := string(pem.EncodeToMemory(&pem.Block{Type: "PUBLIC KEY", Bytes: []byte("notDER")}))
	nonRSAPEM := generateNonRSAPublicKeyPEM(t)

	cases := []struct {
		name    string
		input   string
		want    *rsa.PublicKey
		wantErr bool
	}{
		{"valid PEM", pubPEM, wantPub, false},
		{"empty input", "", nil, true},
		{"wrong type", wrongTypePEM, nil, true},
		{"corrupted DER", corruptDERPEM, nil, true},
		{"non-RSA key", nonRSAPEM, nil, true},
	}

	for _, tc := range cases {
		t.Run(tc.name, func(t *testing.T) {
			got, err := ParseRSAPublicKeyFromPEM(tc.input)
			if tc.wantErr {
				if err == nil {
					t.Errorf("expected error, got none")
				}
				return
			}
			if err != nil {
				t.Fatalf("unexpected error: %v", err)
			}
			if got.N.Cmp(tc.want.N) != 0 || got.E != tc.want.E {
				t.Error("parsed public key does not match original")
			}
		})
	}
}

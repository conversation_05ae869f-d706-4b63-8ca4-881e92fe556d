package jwttokens

import (
	"testing"
)

func TestValidateRoleInt(t *testing.T) {
	tests := []struct {
		role      int64
		expected  string
		expectErr bool
	}{
		{1, "user", false},
		{2, "integrator", false},
		{3, "unknown", true},
	}

	for _, tc := range tests {
		roleName, err := ValidateRoleInt(tc.role)
		if tc.expectErr && err == nil {
			t.<PERSON>("expected error for role %d, got nil", tc.role)
		}
		if !tc.expectErr && err != nil {
			t.<PERSON><PERSON><PERSON>("expected no error for role %d, got error: %v", tc.role, err)
		}
		if roleName != tc.expected {
			t.<PERSON><PERSON>rf("expected role %q for role %d, got %q", tc.expected, tc.role, roleName)
		}
	}
}

func TestValidateRoleString(t *testing.T) {
	tests := []struct {
		role      string
		expected  string
		expectErr bool
	}{
		{"user", "user", false},
		{"integrator", "integrator", false},
		{"admin", "unknown", true},
	}

	for _, tc := range tests {
		roleName, err := ValidateRoleString(tc.role)
		if tc.expectErr && err == nil {
			t.Errorf("expected error for role %q, got nil", tc.role)
		}
		if !tc.expectErr && err != nil {
			t.Errorf("expected no error for role %q, got error: %v", tc.role, err)
		}
		if roleName != tc.expected {
			t.Errorf("expected role %q for role %q, got %q", tc.expected, tc.role, roleName)
		}
	}
}

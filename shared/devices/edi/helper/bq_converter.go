package helper

import (
	"cloud.google.com/go/pubsub"
	"synapse-its.com/shared/pubsubdata"
	"synapse-its.com/shared/schemas"
)

type BQConverter func(byteMsg []byte) (any, error)

func (r *LogMonitorResetRecord) ToBigQuerySchema() schemas.LogMonitorResetRecord {
	return schemas.LogMonitorResetRecord{
		EventTimestamp: r.DateTime,
		ResetType:      r.ResetType,
	}
}

type LogMonitorResetTranslater func(*pubsubdata.HeaderDetails, []byte) (*LogMonitorResetRecords, *HeaderRecord, error)

func NewLogMonitorResetBQConverter(httpHeader *pubsubdata.HeaderDetails, commonAttrs *pubsubdata.CommonAttributes, pubsubMessage *pubsub.Message, translater LogMonitorResetTranslater, logUUID string, deviceID string) BQConverter {
	return func(byteMsg []byte) (any, error) {
		parsedRecords, parsedHeader, err := translater(httpHeader, byteMsg)
		if err != nil {
			return nil, err
		}
		return schemas.LogMonitorReset{
			OrganizationIdentifier: commonAttrs.OrganizationIdentifier,
			SoftwareGatewayID:      httpHeader.GatewayDeviceID,
			TZ:                     httpHeader.GatewayTimezone,
			Topic:                  commonAttrs.Topic,
			PubsubTimestamp:        pubsubMessage.PublishTime.UTC(),
			PubsubID:               pubsubMessage.ID,
			DeviceID:               deviceID,
			Header:                 *parsedHeader.ToBigQuerySchema(),
			Records: func() []schemas.LogMonitorResetRecord {
				records := make([]schemas.LogMonitorResetRecord, len(parsedRecords.Records))
				for i, record := range parsedRecords.Records {
					records[i] = record.ToBigQuerySchema()
				}
				return records
			}(),
			DeviceModel: parsedRecords.DeviceModel,
			RawMessage:  parsedRecords.RawMessage,
			LogUUID:     logUUID,
		}, nil
	}
}

func (r *LogACLineEventRecord) ToBigQuerySchema() schemas.LogACLineEventRecord {
	var freqHz int32
	if r.LineFrequencyHz != nil {
		freqHz = *r.LineFrequencyHz
	}
	return schemas.LogACLineEventRecord{
		EventType:       r.EventType,
		DateTime:        r.DateTime,
		LineVoltageRms:  r.LineVoltageRms,
		LineFrequencyHz: int64(freqHz),
	}
}

func (r *LogPreviousFailRecord) ToBigQuerySchema() schemas.LogPreviousFailRecord {
	return schemas.LogPreviousFailRecord{
		DateTime:                          r.DateTime,
		Fault:                             r.Fault,
		ACLine:                            r.AcLine,
		T48VDCSignalBus:                   r.T48VDCSignalBus,
		RedEnable:                         r.RedEnable,
		MCCoilEE:                          r.MCCoilEE,
		SpecialFunction1:                  r.SpecialFunction1,
		SpecialFunction2:                  r.SpecialFunction2,
		WDTMonitor:                        r.WDTMonitor,
		T24VDCInput:                       r.T24VDCInput,
		Temperature:                       r.Temperature,
		LsFlashBit:                        r.LsFlashBit,
		FaultStatus:                       r.FaultStatus,
		ChannelGreenStatus:                r.ChannelGreenStatus,
		ChannelYellowStatus:               r.ChannelYellowStatus,
		ChannelRedStatus:                  r.ChannelRedStatus,
		ChannelWalkStatus:                 r.ChannelWalkStatus,
		ChannelGreenFieldCheckStatus:      r.ChannelGreenFieldCheckStatus,
		ChannelYellowFieldCheckStatus:     r.ChannelYellowFieldCheckStatus,
		ChannelRedFieldCheckStatus:        r.ChannelRedFieldCheckStatus,
		ChannelWalkFieldCheckStatus:       r.ChannelWalkFieldCheckStatus,
		ChannelGreenRecurrentPulseStatus:  r.ChannelGreenRecurrentPulseStatus,
		ChannelYellowRecurrentPulseStatus: r.ChannelYellowRecurrentPulseStatus,
		ChannelRedRecurrentPulseStatus:    r.ChannelRedRecurrentPulseStatus,
		ChannelWalkRecurrentPulseStatus:   r.ChannelWalkRecurrentPulseStatus,
		ChannelGreenRmsVoltage:            convertInt32SliceToInt64(r.ChannelGreenRmsVoltage),
		ChannelYellowRmsVoltage:           convertInt32SliceToInt64(r.ChannelYellowRmsVoltage),
		ChannelRedRmsVoltage:              convertInt32SliceToInt64(r.ChannelRedRmsVoltage),
		ChannelWalkRmsVoltage:             convertInt32SliceToInt64(r.ChannelWalkRmsVoltage),
		NextConflictingChannels:           r.NextConflictingChannels,
		ChannelRedCurrentStatus:           r.ChannelRedCurrentStatus,
		ChannelYellowCurrentStatus:        r.ChannelYellowCurrentStatus,
		ChannelGreenCurrentStatus:         r.ChannelGreenCurrentStatus,
		ChannelRedRmsCurrent:              convertInt32SliceToInt64(r.ChannelRedRmsCurrent),
		ChannelYellowRmsCurrent:           convertInt32SliceToInt64(r.ChannelYellowRmsCurrent),
		ChannelGreenRmsCurrent:            convertInt32SliceToInt64(r.ChannelGreenRmsCurrent),
	}
}

func (r *TraceBuffer) ToBigQuerySchema() schemas.TraceBuffer {
	return schemas.TraceBuffer{
		BufferRawBytes: r.BufferRawBytes,
		Timestamp:      r.Timestamp,
		Reds:           r.Reds,
		Yellows:        r.Yellows,
		Greens:         r.Greens,
		Walks:          r.Walks,
		EE_SF_RE:       r.EE_SF_RE,
		AcVoltage:      int64(r.AcVoltage),
	}
}

// Helper function to convert []int32 to []int64
func convertInt32SliceToInt64(slice []int32) []int64 {
	if slice == nil {
		return nil
	}
	result := make([]int64, len(slice))
	for i, v := range slice {
		result[i] = int64(v)
	}
	return result
}

func (r *ConfigurationChangeLogRecord) ToBigQuerySchema() schemas.ConfigurationChangeLogRecord {
	return schemas.ConfigurationChangeLogRecord{
		DateTime:                        r.DateTime,
		Ch01Permissives:                 r.Ch01Permissives,
		Ch02Permissives:                 r.Ch02Permissives,
		Ch03Permissives:                 r.Ch03Permissives,
		Ch04Permissives:                 r.Ch04Permissives,
		Ch05Permissives:                 r.Ch05Permissives,
		Ch06Permissives:                 r.Ch06Permissives,
		Ch07Permissives:                 r.Ch07Permissives,
		Ch08Permissives:                 r.Ch08Permissives,
		Ch09Permissives:                 r.Ch09Permissives,
		Ch10Permissives:                 r.Ch10Permissives,
		Ch11Permissives:                 r.Ch11Permissives,
		Ch12Permissives:                 r.Ch12Permissives,
		Ch13Permissives:                 r.Ch13Permissives,
		Ch14Permissives:                 r.Ch14Permissives,
		Ch15Permissives:                 r.Ch15Permissives,
		Ch16Permissives:                 r.Ch16Permissives,
		Ch17Permissives:                 r.Ch17Permissives,
		Ch18Permissives:                 r.Ch18Permissives,
		Ch19Permissives:                 r.Ch19Permissives,
		Ch20Permissives:                 r.Ch20Permissives,
		Ch21Permissives:                 r.Ch21Permissives,
		Ch22Permissives:                 r.Ch22Permissives,
		Ch23Permissives:                 r.Ch23Permissives,
		Ch24Permissives:                 r.Ch24Permissives,
		Ch25Permissives:                 r.Ch25Permissives,
		Ch26Permissives:                 r.Ch26Permissives,
		Ch27Permissives:                 r.Ch27Permissives,
		Ch28Permissives:                 r.Ch28Permissives,
		Ch29Permissives:                 r.Ch29Permissives,
		Ch30Permissives:                 r.Ch30Permissives,
		Ch31Permissives:                 r.Ch31Permissives,
		RedFailEnable:                   r.RedFailEnable,
		GreenYellowDualEnable:           r.GreenYellowDualEnable,
		YellowRedDualEnable:             r.YellowRedDualEnable,
		GreenRedDualEnable:              r.GreenRedDualEnable,
		MinimumYellowClearanceEnable:    r.MinimumYellowClearanceEnable,
		MinimumYellowRedClearanceEnable: r.MinimumYellowRedClearanceEnable,
		FieldCheckEnableGreen:           r.FieldCheckEnableGreen,
		FieldCheckEnableYellow:          r.FieldCheckEnableYellow,
		FieldCheckEnableRed:             r.FieldCheckEnableRed,
		YellowEnable:                    r.YellowEnable,
		WalkEnableTs1:                   r.WalkEnableTs1,
		RedFaultTiming:                  r.RedFaultTiming,
		RecurrentPulse:                  r.RecurrentPulse,
		WatchdogTiming:                  r.WatchdogTiming,
		WatchdogEnableSwitch:            r.WatchdogEnableSwitch,
		ProgramCardMemory:               r.ProgramCardMemory,
		GYEnable:                        r.GYEnable,
		MinimumFlashTime:                r.MinimumFlashTime,
		CvmLatchEnable:                  r.CvmLatchEnable,
		LogCvmFaults:                    r.LogCvmFaults,
		X24VIiInputThreshold:            r.X24VIiInputThreshold,
		X24VLatchEnable:                 r.X24VLatchEnable,
		X24VoltInhibit:                  r.X24VoltInhibit,
		Port1Disable:                    r.Port_1Disable,
		TypeMode:                        r.TypeMode,
		LEDGuardThresholds:              r.LEDguardThresholds,
		ForceType16Mode:                 r.ForceType_16Mode,
		Type12WithSdlcMode:              r.Type_12WithSdlcMode,
		VmCvm24V3XDayLatch:              r.VmCvm_24V_3XdayLatch,
		RedFailEnabledBySSM:             r.RedFailEnabledbySSM,
		DualIndicationFaultTiming:       r.DualIndicationFaultTiming,
		WDTErrorClearOnPU:               r.WDTErrorClearonPU,
		MinimumFlash:                    r.MinimumFlash,
		ConfigChangeFault:               r.ConfigChangeFault,
		RedCableFault:                   r.RedCableFault,
		AcLineBrownout:                  r.AcLineBrownout,
		PinEEPolarity:                   r.PinEEPolarity,
		FlashingYellowArrows:            r.FlashingYellowArrows,
		FyaRedAndYellowEnable:           r.FyaRedAndYellowEnable,
		FyaRedAndGreenDisable:           r.FyaRedAndGreenDisable,
		FyaYellowTrapDetection:          r.FyaYellowTrapDetection,
		FYAFlashRateFault:               r.FYAFlashRateFault,
		FyaFlashRateDetection:           r.FyaFlashRateDetection,
		Pplt5Suppression:                r.Pplt5Suppression,
		CheckValue:                      r.CheckValue,
		ChangeSource:                    r.ChangeSource,
		RedVirtualChannel:               convertVirtualSettings(r.RedVirtualChannel),
		YellowVirtualChannel:            convertVirtualSettings(r.YellowVirtualChannel),
		GreenVirtualChannel:             convertVirtualSettings(r.GreenVirtualChannel),
		CurrentSenseRedEnabled:          r.CurrentSenseRedEnabled,
		CurrentSenseYellowEnabled:       r.CurrentSenseYellowEnabled,
		CurrentSenseGreenEnabled:        r.CurrentSenseGreenEnabled,
		CurrentSenseRedThreshold:        r.CurrentSenseRedThreshold,
		CurrentSenseYellowThreshold:     r.CurrentSenseYellowThreshold,
		CurrentSenseGreenThreshold:      r.CurrentSenseGreenThreshold,
		DarkChannelX01:                  r.DarkChannelX01,
		DarkChannelX02:                  r.DarkChannelX02,
		DarkChannelX03:                  r.DarkChannelX03,
		DarkChannelX04:                  r.DarkChannelX04,
	}
}

// Helper function to convert []VirtualSetting to []schemas.VirtualSetting
func convertVirtualSettings(settings []VirtualSetting) []schemas.VirtualSetting {
	if settings == nil {
		return nil
	}
	result := make([]schemas.VirtualSetting, len(settings))
	for i, v := range settings {
		result[i] = schemas.VirtualSetting{
			Color:         v.Color,
			Enabled:       v.Enabled,
			SourceChannel: v.SourceChannel,
			SourceColor:   v.SourceColor,
		}
	}
	return result
}

type LogACLineEventTranslater func(*pubsubdata.HeaderDetails, []byte) (*LogACLineEventRecords, *HeaderRecord, error)

func NewLogACLineEventBQConverter(httpHeader *pubsubdata.HeaderDetails, commonAttrs *pubsubdata.CommonAttributes, pubsubMessage *pubsub.Message, translater LogACLineEventTranslater, logUUID string, deviceID string) BQConverter {
	return func(byteMsg []byte) (any, error) {
		parsedRecords, parsedHeader, err := translater(httpHeader, byteMsg)
		if err != nil {
			return nil, err
		}
		return schemas.LogACLineEvent{
			OrganizationIdentifier: commonAttrs.OrganizationIdentifier,
			SoftwareGatewayID:      httpHeader.GatewayDeviceID,
			TZ:                     httpHeader.GatewayTimezone,
			Topic:                  commonAttrs.Topic,
			PubsubTimestamp:        pubsubMessage.PublishTime.UTC(),
			PubsubID:               pubsubMessage.ID,
			DeviceID:               deviceID,
			Header:                 *parsedHeader.ToBigQuerySchema(),
			Record: func() []schemas.LogACLineEventRecord {
				records := make([]schemas.LogACLineEventRecord, len(parsedRecords.Records))
				for i, record := range parsedRecords.Records {
					records[i] = record.ToBigQuerySchema()
				}
				return records
			}(),
			DeviceModel: parsedRecords.DeviceModel,
			RawMessage:  parsedRecords.RawMessage,
			LogUUID:     logUUID,
		}, nil
	}
}

type LogPreviousFailTranslater func(*pubsubdata.HeaderDetails, []byte) (*LogPreviousFailRecords, *HeaderRecord, error)

func NewLogPreviousFailBQConverter(httpHeader *pubsubdata.HeaderDetails, commonAttrs *pubsubdata.CommonAttributes, pubsubMessage *pubsub.Message, translater LogPreviousFailTranslater, logUUID string, deviceID string) BQConverter {
	return func(byteMsg []byte) (any, error) {
		parsedRecords, parsedHeader, err := translater(httpHeader, byteMsg)
		if err != nil {
			return nil, err
		}
		return schemas.LogPreviousFail{
			OrganizationIdentifier: commonAttrs.OrganizationIdentifier,
			SoftwareGatewayID:      httpHeader.GatewayDeviceID,
			TZ:                     httpHeader.GatewayTimezone,
			Topic:                  commonAttrs.Topic,
			PubsubTimestamp:        pubsubMessage.PublishTime.UTC(),
			PubsubID:               pubsubMessage.ID,
			DeviceID:               deviceID,
			Header:                 *parsedHeader.ToBigQuerySchema(),
			Records: func() []schemas.LogPreviousFailRecord {
				records := make([]schemas.LogPreviousFailRecord, len(parsedRecords.Records))
				for i, record := range parsedRecords.Records {
					records[i] = record.ToBigQuerySchema()
				}
				return records
			}(),
			DeviceModel: parsedRecords.DeviceModel,
			RawMessage:  parsedRecords.RawMessage,
			LogUUID:     logUUID,
		}, nil
	}
}

type FaultSignalSequenceTranslater func(*pubsubdata.HeaderDetails, []byte) (*FaultSignalSequenceRecords, *HeaderRecord, error)

func NewFaultSignalSequenceBQConverter(
	httpHeader *pubsubdata.HeaderDetails,
	commonAttrs *pubsubdata.CommonAttributes,
	pubsubMessage *pubsub.Message,
	translater FaultSignalSequenceTranslater,
	existingSeq *schemas.LogFaultSignalSequence,
	logUUID string,
	deviceID string,
) BQConverter {
	return func(byteMsg []byte) (any, error) {
		// Run your translator:
		parsedRecords, parsedHeader, err := translater(httpHeader, byteMsg)
		if err != nil {
			return nil, err
		}

		// If existingSeq is “empty” (we check OrganizationIdentifier here; you can pick any
		// other field that’s guaranteed to be non‐empty once initialized), populate all
		// top‐level fields except Records:
		if existingSeq.OrganizationIdentifier == "" {
			existingSeq.OrganizationIdentifier = commonAttrs.OrganizationIdentifier
			existingSeq.SoftwareGatewayID = httpHeader.GatewayDeviceID
			existingSeq.TZ = httpHeader.GatewayTimezone
			existingSeq.Topic = commonAttrs.Topic
			existingSeq.PubsubTimestamp = pubsubMessage.PublishTime.UTC()
			existingSeq.PubsubID = pubsubMessage.ID
			existingSeq.DeviceID = deviceID
			existingSeq.Header = parsedHeader.ToBigQuerySchema()
			existingSeq.DeviceModel = parsedRecords.DeviceModel
			existingSeq.LogUUID = logUUID
		}

		// Build a single LogFaultSignalSequenceRecords out of parsedRecords
		newBlock := schemas.LogFaultSignalSequenceRecords{
			TraceRawBytes: parsedRecords.RawMessage, // <— assume this is a byte
			FaultType:     parsedRecords.FaultType,
			Records: func() []schemas.TraceBuffer {
				out := make([]schemas.TraceBuffer, len(parsedRecords.Records))
				for i, r := range parsedRecords.Records {
					out[i] = r.ToBigQuerySchema()
				}
				return out
			}(),
		}

		// Append to the slice every time this converter is called:
		existingSeq.Records = append(existingSeq.Records, newBlock)

		return *existingSeq, nil
	}
}

type ConfigurationChangeLogTranslater func(*pubsubdata.HeaderDetails, []byte) (*ConfigurationChangeLogRecords, *HeaderRecord, error)

func NewConfigurationChangeLogBQConverter(httpHeader *pubsubdata.HeaderDetails, commonAttrs *pubsubdata.CommonAttributes, pubsubMessage *pubsub.Message, translater ConfigurationChangeLogTranslater, logUUID string, deviceID string) BQConverter {
	return func(byteMsg []byte) (any, error) {
		parsedRecords, parsedHeader, err := translater(httpHeader, byteMsg)
		if err != nil {
			return nil, err
		}
		return schemas.LogConfiguration{
			OrganizationIdentifier: commonAttrs.OrganizationIdentifier,
			SoftwareGatewayID:      httpHeader.GatewayDeviceID,
			TZ:                     httpHeader.GatewayTimezone,
			Topic:                  commonAttrs.Topic,
			PubsubTimestamp:        pubsubMessage.PublishTime.UTC(),
			PubsubID:               pubsubMessage.ID,
			DeviceID:               deviceID,
			Header:                 parsedHeader.ToBigQuerySchema(),
			Record: func() []schemas.ConfigurationChangeLogRecord {
				records := make([]schemas.ConfigurationChangeLogRecord, len(parsedRecords.Record))
				for i, record := range parsedRecords.Record {
					records[i] = record.ToBigQuerySchema()
				}
				return records
			}(),
			DeviceModel: parsedRecords.DeviceModel,
			RawMessage:  parsedRecords.RawMessage,
			LogUUID:     logUUID,
		}, nil
	}
}

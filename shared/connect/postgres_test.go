package connect

import (
	"context"
	"database/sql"
	"database/sql/driver"
	"errors"
	"fmt"
	"reflect"
	"regexp"
	"strings"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/stretchr/testify/assert"
	unexported "synapse-its.com/shared/mocks/unexported"
)

func savePostgresVars() func() {
	origOsGetEnv := osGetenv
	origTimeSleep := timeSleepPostgres
	origsqlOpen := sqlOpen

	// Don't sleep for test cases (too slow)
	timeSleepPostgres = func(d time.Duration) {}

	// Restore env vars after test
	return func() {
		osGetenv = origOsGetEnv
		sqlOpen = origsqlOpen
		timeSleepPostgres = origTimeSleep
	}
}

// Person is a sample struct used for testing.
// It uses db tags that correspond to the query's column names.
type Person struct {
	ID   int    `db:"id"`
	Name string `db:"name"`
}

// Animal is a struct without any db tags.
type Animal struct {
	Name string // fallback: "name"
	Age  int    // fallback: "age"
}

// Book is a struct where Title has an empty db tag.
type Book struct {
	Title  string `db:""` // fallback: "title"
	Author string // fallback: "author"
}

// Define our test cases.
type testCase struct {
	name           string
	query          string
	destSetup      func() interface{}
	rows           *sqlmock.Rows
	expectedResult interface{}
	expectedErr    string
}

// Define env vars
type PostgresConfig struct {
	User     string
	Password string
	DB       string
	Host     string
	Port     string
}

func MockGetEnvFromConfig(cfg PostgresConfig) func(string) string {
	return func(key string) string {
		switch key {
		case "POSTGRES_USER":
			return cfg.User
		case "POSTGRES_PASSWORD":
			return cfg.Password
		case "POSTGRES_DB":
			return cfg.DB
		case "POSTGRES_HOST":
			return cfg.Host
		case "POSTGRES_PORT":
			return cfg.Port
		default:
			return ""
		}
	}
}

// --- Fake sql.Result implementation for testing Exec ---
type fakeResult struct{}

func (fr fakeResult) LastInsertId() (int64, error) { return 0, nil }
func (fr fakeResult) RowsAffected() (int64, error) { return 0, nil }

// helper function to convert []interface{} to []driver.Value.
func toDriverValues(args []interface{}) []driver.Value {
	vals := make([]driver.Value, len(args))
	for i, arg := range args {
		vals[i] = arg
	}
	return vals
}

func TestExec(t *testing.T) {
	// Define test cases.
	tests := []struct {
		name         string
		namespace    string
		query        string
		args         []interface{}
		mockResult   sql.Result
		mockErr      error
		expectedStmt string // expected statement after ReplaceNamespace.
	}{
		{
			name:         "Exec with namespace and parameters succeeds",
			namespace:    "NS_",
			query:        "INSERT INTO {{mytable}} (col) VALUES (?)",
			args:         []interface{}{123},
			mockResult:   sqlmock.NewResult(1, 1),
			mockErr:      nil,
			expectedStmt: `INSERT INTO "NS_mytable" (col) VALUES (?)`,
		},
		{
			name:         "Exec with no parameters succeeds",
			namespace:    "",
			query:        "DELETE FROM {{usertable}} WHERE id = 10",
			args:         []interface{}{},
			mockResult:   sqlmock.NewResult(0, 1),
			mockErr:      nil,
			expectedStmt: `DELETE FROM "usertable" WHERE id = 10`,
		},
		{
			name:         "Exec returns error from DB",
			namespace:    "",
			query:        "DELETE FROM {{usertable}} WHERE id = 10",
			args:         []interface{}{},
			mockResult:   nil,
			mockErr:      fmt.Errorf("db error"),
			expectedStmt: `DELETE FROM "usertable" WHERE id = 10`,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			db, mock, err := sqlmock.New()
			if err != nil {
				t.Fatalf("failed to create sqlmock: %v", err)
			}
			defer db.Close()

			executor := &PostgresExecutor{
				DB:     db,
				Ctx:    context.Background(),
				Config: DatabaseConfig{Namespace: tt.namespace},
			}

			// Convert tt.args (of type []interface{}) to []driver.Value.
			driverArgs := toDriverValues(tt.args)

			mock.ExpectExec(regexp.QuoteMeta(tt.expectedStmt)).
				WithArgs(driverArgs...).
				WillReturnResult(tt.mockResult).
				WillReturnError(tt.mockErr)

			result, err := executor.Exec(tt.query, tt.args...)
			// Check for expected error if any.
			if tt.mockErr != nil {
				if err == nil || err.Error() != tt.mockErr.Error() {
					t.Errorf("expected error %v, got %v", tt.mockErr, err)
				}
				return
			}
			if err != nil {
				t.Errorf("unexpected error: %v", err)
			}
			if result == nil {
				t.Errorf("expected non-nil result")
			}

			if err := mock.ExpectationsWereMet(); err != nil {
				t.Errorf("there were unfulfilled expectations: %v", err)
			}
		})
	}
}

func TestExecMultiple(t *testing.T) {
	// Define test cases.
	tests := []struct {
		name          string
		namespace     string
		queries       string
		expectedStmts []string // expected individual statements after replacement & trimming.
		errorOnStmt   int      // index of statement to return an error; -1 means no error.
		errorMessage  string   // error message that Exec should return for that statement.
		expectedError string   // expected error from ExecMultiple.
	}{
		{
			name:      "Multiple valid queries executed sequentially",
			namespace: "DEV_",
			queries:   "UPDATE {{table1}} SET col = 1; DELETE FROM {{table2}}",
			expectedStmts: []string{
				`UPDATE "DEV_table1" SET col = 1`,
				`DELETE FROM "DEV_table2"`,
			},
			errorOnStmt:   -1,
			expectedError: "",
		},
		{
			name:      "Extra semicolons and empty statements are ignored",
			namespace: "",
			queries:   "INSERT INTO {{foo}} (col) VALUES (10); ;  ;UPDATE {{bar}} SET col = 20;",
			expectedStmts: []string{
				`INSERT INTO "foo" (col) VALUES (10)`,
				`UPDATE "bar" SET col = 20`,
			},
			errorOnStmt:   -1,
			expectedError: "",
		},
		{
			name:      "Error on one statement aborts ExecMultiple",
			namespace: "NS_",
			queries:   "SELECT * FROM {{alpha}}; DELETE FROM {{beta}}",
			expectedStmts: []string{
				`SELECT * FROM "NS_alpha"`,
				`DELETE FROM "NS_beta"`,
			},
			errorOnStmt:   1,
			errorMessage:  "simulated DB error",
			expectedError: `error executing statement "DELETE FROM \"NS_beta\""`,
		},
		{
			name:      "No queries provided",
			namespace: "NS_",
			queries:   "   ",
			// No statements expected.
			expectedStmts: []string{},
			errorOnStmt:   -1,
			expectedError: "",
		},
		{
			name:      "Empty statements and one valid statement (empties skipped)",
			namespace: "",
			queries:   ";\t;\r;  SELECT 1; \n  ;",
			// No statements should be executed
			expectedStmts: []string{
				`SELECT 1`,
			},
			errorOnStmt:   -1,
			expectedError: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			db, mock, err := sqlmock.New()
			if err != nil {
				t.Fatalf("failed to create sqlmock: %v", err)
			}
			defer db.Close()

			executor := &PostgresExecutor{
				DB:     db,
				Ctx:    context.Background(),
				Config: DatabaseConfig{Namespace: tt.namespace},
			}

			// Set expectations for each expected individual statement.
			for i, stmt := range tt.expectedStmts {
				exp := mock.ExpectExec(regexp.QuoteMeta(stmt)).
					WithArgs() // no parameters supported
				if i == tt.errorOnStmt {
					exp.WillReturnError(fmt.Errorf("%s", tt.errorMessage))
				} else {
					exp.WillReturnResult(sqlmock.NewResult(1, 1))
				}
			}

			err = executor.ExecMultiple(tt.queries)
			if tt.expectedError != "" {
				if err == nil {
					t.Errorf("expected error %q but got none", tt.expectedError)
				} else if !strings.Contains(err.Error(), tt.expectedError) {
					t.Errorf("expected error to contain %q but got %q", tt.expectedError, err.Error())
				}
			} else if err != nil {
				t.Errorf("unexpected error: %v", err)
			}

			if err := mock.ExpectationsWereMet(); err != nil {
				t.Errorf("there were unfulfilled expectations: %v", err)
			}
		})
	}
}

func TestQueryRowStruct(t *testing.T) {
	tests := []testCase{
		{
			name:  "Valid: returns a row and maps fields",
			query: "SELECT id, name FROM people",
			destSetup: func() interface{} {
				var p Person
				return &p
			},
			rows: sqlmock.NewRows([]string{"id", "name"}).
				AddRow(1, "Alice"),
			expectedResult: Person{ID: 1, Name: "Alice"},
			expectedErr:    "",
		},
		{
			name:  "Valid: Animal struct with no db tags uses lowercase field names",
			query: "SELECT name, age FROM animals",
			destSetup: func() interface{} {
				var a Animal
				return &a
			},
			rows: sqlmock.NewRows([]string{"name", "age"}).
				AddRow("Lion", 5),
			expectedResult: Animal{Name: "Lion", Age: 5},
			expectedErr:    "",
		},
		{
			name:  "Valid: Book struct with empty db tag falls back to lowercase field name",
			query: "SELECT title, author FROM books",
			destSetup: func() interface{} {
				var b Book
				return &b
			},
			rows: sqlmock.NewRows([]string{"title", "author"}).
				AddRow("The Hobbit", "J.R.R. Tolkien"),
			expectedResult: Book{Title: "The Hobbit", Author: "J.R.R. Tolkien"},
			expectedErr:    "",
		},
		{
			name:  "sql.ErrNoRows: No rows returned returns sql.ErrNoRows",
			query: "SELECT id, name FROM people",
			destSetup: func() interface{} {
				var p Person
				return &p
			},
			rows:           sqlmock.NewRows([]string{"id", "name"}),
			expectedResult: nil,
			expectedErr:    sql.ErrNoRows.Error(),
		},
		{
			name:  "Unmapped column returns an error",
			query: "SELECT id, name, age FROM people",
			destSetup: func() interface{} {
				var p Person
				return &p
			},
			rows: sqlmock.NewRows([]string{"id", "name", "age"}).
				AddRow(1, "Alice", 30),
			expectedResult: nil,
			expectedErr:    `no matching struct field found for column "age"`,
		},
		{
			name:  "Invalid dest: nil pointer",
			query: "SELECT id, name FROM people",
			destSetup: func() interface{} {
				var p *Person = nil
				return p
			},
			rows:           nil, // Query is not executed because the error is detected immediately.
			expectedResult: nil,
			expectedErr:    "dest must be a non-nil pointer",
		},
		{
			name:  "Invalid dest: not pointer to struct",
			query: "SELECT id, name FROM people",
			destSetup: func() interface{} {
				var p Person
				return p
			},
			rows:           nil,
			expectedResult: nil,
			expectedErr:    "dest must be a non-nil pointer",
		},
		{
			name:  "Invalid dest: pointer to a slice (not pointer to a struct)",
			query: "SELECT id, name FROM people",
			destSetup: func() interface{} {
				// This returns a pointer to a slice of Person.
				var people []Person
				return &people
			},
			rows:           nil,
			expectedResult: nil,
			expectedErr:    "dest must point to a struct",
		},
		{
			name:  "Valid: Extra struct field remains unchanged",
			query: "SELECT id FROM people", // Query returns only 'id'
			destSetup: func() interface{} {
				// Prepopulate Name with a non-zero value.
				p := Person{Name: "Initial"}
				return &p
			},
			rows: sqlmock.NewRows([]string{"id"}).
				AddRow(2),
			// Expect that only ID is updated while Name remains "Initial"
			expectedResult: Person{ID: 2, Name: "Initial"},
			expectedErr:    "",
		},
		{
			name:  "Valid: Type mismatch between struct and query result: query string (`1`) not type int",
			query: "SELECT id, name FROM people",
			destSetup: func() interface{} {
				var p Person
				return &p
			},
			rows: sqlmock.NewRows([]string{"id", "name"}).
				AddRow("1", "Alice"),
			expectedResult: Person{ID: 1, Name: "Alice"},
			expectedErr:    "",
		},
		{
			name:  "Error: Type mismatch between struct and query result: Scan error",
			query: "SELECT id, name FROM people",
			destSetup: func() interface{} {
				var p Person
				return &p
			},
			rows: sqlmock.NewRows([]string{"id", "name"}).
				AddRow("not-an-integer", "Alice"),
			expectedResult: nil,
			expectedErr:    "sql: Scan error",
		},
		{
			name:  "Valid: Scan int into string field (via coercion)",
			query: "SELECT id, name FROM people",
			destSetup: func() interface{} {
				var p Person
				return &p
			},
			rows: sqlmock.NewRows([]string{"id", "name"}).
				AddRow(1, 272), // int for name
			expectedResult: Person{ID: 1, Name: "272"},
			expectedErr:    "",
		},
		{
			name:  "Error: cannot scan into unexported field",
			query: "SELECT secret FROM secrets",
			destSetup: func() interface{} {
				var h unexported.Hidden
				return &h
			},
			rows: sqlmock.NewRows([]string{"secret"}).
				AddRow("top-secret"),
			expectedResult: nil,
			expectedErr:    `cannot scan into unexported field "secret"`,
		},
	}

	// Run each test case.
	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			// Create a new sqlmock database for this test.
			db, mock, err := sqlmock.New()
			if err != nil {
				t.Fatalf("failed to create sqlmock: %v", err)
			}
			defer db.Close()

			executor := &PostgresExecutor{
				DB:  db,
				Ctx: context.Background(),
			}

			dest := tc.destSetup()

			if tc.rows != nil {
				queryRegex := "^" + regexp.QuoteMeta(tc.query) + "$"
				mock.ExpectQuery(queryRegex).WillReturnRows(tc.rows)
			}

			err = executor.QueryRowStruct(dest, tc.query)
			// Check for expected error.
			if tc.expectedErr != "" {
				if err == nil {
					t.Errorf("expected error %q but got none", tc.expectedErr)
				} else if !strings.Contains(err.Error(), tc.expectedErr) {
					t.Errorf("expected error to contain %q but got %q", tc.expectedErr, err.Error())
				}
				return
			}
			// Error not expected.
			if err != nil {
				t.Errorf("unexpected error: %v", err)
			}

			// If an expectedResult is provided, verify that the destination value matches.
			if tc.expectedResult != nil {
				resultVal := reflect.ValueOf(dest).Elem().Interface()
				if !reflect.DeepEqual(resultVal, tc.expectedResult) {
					t.Errorf("expected result %#v, got %#v", tc.expectedResult, resultVal)
				}
			}

			// Ensure all expectations were met.
			if err := mock.ExpectationsWereMet(); err != nil {
				t.Errorf("there were unfulfilled expectations: %v", err)
			}
		})
	}
}

func TestQueryGenericSlice(t *testing.T) {
	tests := []testCase{
		{
			name:  "Valid: returns multiple people",
			query: "SELECT id, name FROM people",
			destSetup: func() interface{} {
				var people []Person
				return &people
			},
			rows: sqlmock.NewRows([]string{"id", "name"}).
				AddRow(1, "Alice").
				AddRow(2, "Bob"),
			expectedResult: []Person{{ID: 1, Name: "Alice"}, {ID: 2, Name: "Bob"}},
			expectedErr:    "",
		},
		{
			name:  "Valid: Animal struct fallback field names",
			query: "SELECT name, age FROM animals",
			destSetup: func() interface{} {
				var animals []Animal
				return &animals
			},
			rows: sqlmock.NewRows([]string{"name", "age"}).
				AddRow("Tiger", 3).
				AddRow("Panther", 5),
			expectedResult: []Animal{{Name: "Tiger", Age: 3}, {Name: "Panther", Age: 5}},
			expectedErr:    "",
		},
		{
			name:  "Valid: Book with empty db tag",
			query: "SELECT title, author FROM books",
			destSetup: func() interface{} {
				var books []Book
				return &books
			},
			rows: sqlmock.NewRows([]string{"title", "author"}).
				AddRow("1984", "George Orwell").
				AddRow("Dune", "Frank Herbert"),
			expectedResult: []Book{{Title: "1984", Author: "George Orwell"}, {Title: "Dune", Author: "Frank Herbert"}},
			expectedErr:    "",
		},
		{
			name:  "Empty result set",
			query: "SELECT id, name FROM people",
			destSetup: func() interface{} {
				var people []Person
				return &people
			},
			rows:           sqlmock.NewRows([]string{"id", "name"}),
			expectedResult: []Person{},
			expectedErr:    "",
		},
		{
			name:  "Unmapped column returns error",
			query: "SELECT id, name, age FROM people",
			destSetup: func() interface{} {
				var people []Person
				return &people
			},
			rows: sqlmock.NewRows([]string{"id", "name", "age"}).
				AddRow(1, "Alice", 30),
			expectedResult: nil,
			expectedErr:    `no matching struct field found for column "age"`,
		},
		{
			name:  "Valid: Type mismatch string as int field (coercible)",
			query: "SELECT id, name FROM people",
			destSetup: func() interface{} {
				var people []Person
				return &people
			},
			rows: sqlmock.NewRows([]string{"id", "name"}).
				AddRow("1", "Alice"),
			expectedResult: []Person{{ID: 1, Name: "Alice"}},
			expectedErr:    "",
		},
		{
			name:  "Error: Type mismatch string not parseable to int",
			query: "SELECT id, name FROM people",
			destSetup: func() interface{} {
				var people []Person
				return &people
			},
			rows: sqlmock.NewRows([]string{"id", "name"}).
				AddRow("not-an-integer", "Alice"),
			expectedResult: nil,
			expectedErr:    "sql: Scan error",
		},
		{
			name:  "Valid: Scan int into string field (via coercion)",
			query: "SELECT id, name FROM people",
			destSetup: func() interface{} {
				var people []Person
				return &people
			},
			rows: sqlmock.NewRows([]string{"id", "name"}).
				AddRow(1, 272), // int for name
			expectedResult: []Person{{ID: 1, Name: "272"}},
			expectedErr:    "",
		},
		{
			name:  "Invalid dest: not a pointer",
			query: "SELECT id, name FROM people",
			destSetup: func() interface{} {
				var people []Person
				return people
			},
			rows:           nil,
			expectedResult: nil,
			expectedErr:    "dest must be a non-nil pointer",
		},
		{
			name:  "Invalid dest: not pointer to slice",
			query: "SELECT id, name FROM people",
			destSetup: func() interface{} {
				var person Person
				return &person
			},
			rows:           nil,
			expectedResult: nil,
			expectedErr:    "dest must point to a slice",
		},
		{
			name:  "Invalid dest: slice of non-struct",
			query: "SELECT id FROM people",
			destSetup: func() interface{} {
				var ints []int
				return &ints
			},
			rows:           nil,
			expectedResult: nil,
			expectedErr:    "dest slice element must be a struct",
		},
		{
			name:  "Error: cannot scan into unexported field",
			query: "SELECT secret FROM secrets",
			destSetup: func() interface{} {
				var h []unexported.Hidden
				return &h
			},
			rows: sqlmock.NewRows([]string{"secret"}).
				AddRow("top-secret"),
			expectedResult: nil,
			expectedErr:    `cannot scan into unexported field "secret"`,
		},
	}

	// Run each test case.
	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			// Create a new sqlmock database for this test.
			db, mock, err := sqlmock.New()
			if err != nil {
				t.Fatalf("failed to create sqlmock: %v", err)
			}
			defer db.Close()

			// Create a new PostgresExecutor instance.
			executor := &PostgresExecutor{
				DB:  db,
				Ctx: context.Background(),
			}

			// Use the destSetup to obtain a destination for scanning.
			dest := tc.destSetup()

			// If rows are provided, set an expectation on the query.
			if tc.rows != nil {
				// Use regexp.QuoteMeta to safely match the query exactly.
				queryRegex := "^" + regexp.QuoteMeta(tc.query) + "$"
				mock.ExpectQuery(queryRegex).WillReturnRows(tc.rows)
			}

			// Call the function under test.
			err = executor.QueryGenericSlice(dest, tc.query)
			if tc.expectedErr != "" {
				if err == nil {
					t.Errorf("expected error %q but got none", tc.expectedErr)
				} else if !strings.Contains(err.Error(), tc.expectedErr) {
					t.Errorf("expected error to contain %q but got %q", tc.expectedErr, err.Error())
				}
				// For error cases, we can return early.
				return
			}
			if err != nil {
				t.Errorf("unexpected error: %v", err)
			}

			// Check that the resulting slice matches our expected result.
			// Because dest is a pointer to a slice, we need to dereference it.
			resultVal := reflect.ValueOf(dest).Elem().Interface()
			if !reflect.DeepEqual(resultVal, tc.expectedResult) {
				t.Errorf("expected result %#v, got %#v", tc.expectedResult, resultVal)
			}

			// Ensure all expectations were met.
			if err := mock.ExpectationsWereMet(); err != nil {
				t.Errorf("there were unfulfilled expectations: %v", err)
			}
		})
	}
}

func TestQueryGeneric(t *testing.T) {
	tests := []struct {
		name           string
		query          string
		args           []interface{}
		rows           *sqlmock.Rows
		expectedResult []map[string]interface{}
		expectedErr    string
	}{
		{
			name:  "Valid: returns multiple rows",
			query: "SELECT id, name FROM people",
			args:  []interface{}{},
			rows: sqlmock.NewRows([]string{"id", "name"}).
				AddRow(1, "Alice").
				AddRow(2, "Bob"),
			expectedResult: []map[string]interface{}{
				{"id": int64(1), "name": "Alice"},
				{"id": int64(2), "name": "Bob"},
			},
			expectedErr: "",
		},
		{
			name:  "Valid: returns single row",
			query: "SELECT name, age FROM animals WHERE id = $1",
			args:  []interface{}{1},
			rows: sqlmock.NewRows([]string{"name", "age"}).
				AddRow("Tiger", 3),
			expectedResult: []map[string]interface{}{
				{"name": "Tiger", "age": int64(3)},
			},
			expectedErr: "",
		},
		{
			name:           "Empty result set returns nil",
			query:          "SELECT id, name FROM people",
			args:           []interface{}{},
			rows:           sqlmock.NewRows([]string{"id", "name"}),
			expectedResult: nil,
			expectedErr:    "",
		},
		{
			name:           "Database error is propagated",
			query:          "SELECT * FROM invalid_table",
			args:           []interface{}{},
			rows:           nil,
			expectedResult: nil,
			expectedErr:    "database error",
		},
		{
			name:  "Valid: with query parameters",
			query: "SELECT id, name FROM people WHERE id = $1",
			args:  []interface{}{42},
			rows: sqlmock.NewRows([]string{"id", "name"}).
				AddRow(42, "Douglas"),
			expectedResult: []map[string]interface{}{
				{"id": int64(42), "name": "Douglas"},
			},
			expectedErr: "",
		},
		{
			name:  "Valid: different column types",
			query: "SELECT id, name, active, salary FROM employees",
			args:  []interface{}{},
			rows: sqlmock.NewRows([]string{"id", "name", "active", "salary"}).
				AddRow(1, "Alice", true, 75000.50).
				AddRow(2, "Bob", false, 85000.75),
			expectedResult: []map[string]interface{}{
				{"id": int64(1), "name": "Alice", "active": true, "salary": 75000.50},
				{"id": int64(2), "name": "Bob", "active": false, "salary": 85000.75},
			},
			expectedErr: "",
		},
		{
			name:  "Error: Columns() returns error",
			query: "SELECT id FROM people",
			args:  []interface{}{},
			rows: sqlmock.NewRows([]string{"id"}).
				CloseError(fmt.Errorf("columns error")),
			expectedResult: nil,
			expectedErr:    "columns error",
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			// Create a new sqlmock database for this test.
			db, mock, err := sqlmock.New()
			if err != nil {
				t.Fatalf("failed to create sqlmock: %v", err)
			}
			defer db.Close()

			// Create a new PostgresExecutor instance.
			executor := &PostgresExecutor{
				DB:  db,
				Ctx: context.Background(),
			}

			// If rows are provided, set an expectation on the query.
			if tc.rows != nil {
				// Use regexp.QuoteMeta to safely match the query exactly.
				queryRegex := "^" + regexp.QuoteMeta(tc.query) + "$"

				// Convert tc.args to driver.Value for the mock
				driverArgs := toDriverValues(tc.args)

				mock.ExpectQuery(queryRegex).
					WithArgs(driverArgs...).
					WillReturnRows(tc.rows)
			} else if tc.expectedErr != "" {
				// For error cases with no rows
				queryRegex := "^" + regexp.QuoteMeta(tc.query) + "$"

				// Convert tc.args to driver.Value for the mock
				driverArgs := toDriverValues(tc.args)

				mock.ExpectQuery(queryRegex).
					WithArgs(driverArgs...).
					WillReturnError(fmt.Errorf("%s", tc.expectedErr))
			}

			// Call the function under test.
			result, err := executor.QueryGeneric(tc.query, tc.args...)

			// Check for expected error.
			if tc.expectedErr != "" {
				if err == nil {
					t.Errorf("expected error %q but got none", tc.expectedErr)
				} else if !strings.Contains(err.Error(), tc.expectedErr) {
					t.Errorf("expected error to contain %q but got %q", tc.expectedErr, err.Error())
				}
				return
			}

			// Error not expected.
			if err != nil {
				t.Errorf("unexpected error: %v", err)
			}

			// Check that the resulting slice matches our expected result.
			if !reflect.DeepEqual(result, tc.expectedResult) {
				t.Errorf("expected result %#v, got %#v", tc.expectedResult, result)
			}

			// Ensure all expectations were met.
			if err := mock.ExpectationsWereMet(); err != nil {
				t.Errorf("there were unfulfilled expectations: %v", err)
			}
		})
	}
}

func TestQueryRow(t *testing.T) {
	tests := []struct {
		name           string
		query          string
		args           []interface{}
		rows           *sqlmock.Rows
		expectedResult map[string]interface{}
		expectedErr    string
	}{
		{
			name:  "Valid: returns a single row as map",
			query: "SELECT id, name FROM people WHERE id = $1",
			args:  []interface{}{1},
			rows: sqlmock.NewRows([]string{"id", "name"}).
				AddRow(1, "Alice"),
			expectedResult: map[string]interface{}{
				"id":   int64(1),
				"name": "Alice",
			},
			expectedErr: "",
		},
		{
			name:  "Valid: different column types",
			query: "SELECT id, name, active, salary FROM employees WHERE id = $1",
			args:  []interface{}{1},
			rows: sqlmock.NewRows([]string{"id", "name", "active", "salary"}).
				AddRow(1, "Alice", true, 75000.50),
			expectedResult: map[string]interface{}{
				"id":     int64(1),
				"name":   "Alice",
				"active": true,
				"salary": 75000.50,
			},
			expectedErr: "",
		},
		{
			name:  "Valid: byte array converted to string",
			query: "SELECT id, data FROM blobs WHERE id = $1",
			args:  []interface{}{1},
			rows: sqlmock.NewRows([]string{"id", "data"}).
				AddRow(1, []byte("binary data")),
			expectedResult: map[string]interface{}{
				"id":   int64(1),
				"data": "binary data",
			},
			expectedErr: "",
		},
		{
			name:           "No rows returns sql.ErrNoRows",
			query:          "SELECT id, name FROM people WHERE id = $1",
			args:           []interface{}{999},
			rows:           sqlmock.NewRows([]string{"id", "name"}),
			expectedResult: nil,
			expectedErr:    sql.ErrNoRows.Error(),
		},
		{
			name:           "Database error is propagated",
			query:          "SELECT * FROM invalid_table",
			args:           []interface{}{},
			rows:           nil,
			expectedResult: nil,
			expectedErr:    "database error",
		},
		{
			name:  "Multiple rows returns only first row",
			query: "SELECT id, name FROM people",
			args:  []interface{}{},
			rows: sqlmock.NewRows([]string{"id", "name"}).
				AddRow(1, "Alice").
				AddRow(2, "Bob"),
			expectedResult: map[string]interface{}{
				"id":   int64(1),
				"name": "Alice",
			},
			expectedErr: "",
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			// Create a new sqlmock database for this test.
			db, mock, err := sqlmock.New()
			if err != nil {
				t.Fatalf("failed to create sqlmock: %v", err)
			}
			defer db.Close()

			// Create a new PostgresExecutor instance.
			executor := &PostgresExecutor{
				DB:  db,
				Ctx: context.Background(),
			}

			// If rows are provided, set an expectation on the query.
			if tc.rows != nil {
				// Use regexp.QuoteMeta to safely match the query exactly.
				queryRegex := "^" + regexp.QuoteMeta(tc.query) + "$"

				// Convert tc.args to driver.Value for the mock
				driverArgs := toDriverValues(tc.args)

				mock.ExpectQuery(queryRegex).
					WithArgs(driverArgs...).
					WillReturnRows(tc.rows)
			} else if tc.expectedErr != "" {
				// For error cases with no rows
				queryRegex := "^" + regexp.QuoteMeta(tc.query) + "$"

				// Convert tc.args to driver.Value for the mock
				driverArgs := toDriverValues(tc.args)

				mock.ExpectQuery(queryRegex).
					WithArgs(driverArgs...).
					WillReturnError(fmt.Errorf("%s", tc.expectedErr))
			}

			// Call the function under test.
			result, err := executor.QueryRow(tc.query, tc.args...)

			// Check for expected error.
			if tc.expectedErr != "" {
				if err == nil {
					t.Errorf("expected error %q but got none", tc.expectedErr)
				} else if !strings.Contains(err.Error(), tc.expectedErr) {
					t.Errorf("expected error to contain %q but got %q", tc.expectedErr, err.Error())
				}
				return
			}

			// Error not expected.
			if err != nil {
				t.Errorf("unexpected error: %v", err)
			}

			// Check that the resulting map matches our expected result.
			if !reflect.DeepEqual(result, tc.expectedResult) {
				t.Errorf("expected result %#v, got %#v", tc.expectedResult, result)
			}

			// Ensure all expectations were met.
			if err := mock.ExpectationsWereMet(); err != nil {
				t.Errorf("there were unfulfilled expectations: %v", err)
			}
		})
	}
}

func TestEscapeIdentifier(t *testing.T) {
	tests := []struct {
		name       string
		identifier string
		want       string
	}{
		{
			name:       "Simple identifier",
			identifier: "mytable",
			want:       `"mytable"`,
		},
		{
			name:       "Identifier with embedded quotes",
			identifier: `col"name`,
			want:       `"col""name"`,
		},
		{
			name:       "Identifier with multiple quotes",
			identifier: `a"b"c`,
			want:       `"a""b""c"`,
		},
		{
			name:       "Empty identifier",
			identifier: "",
			want:       `""`,
		},
	}

	executor := &PostgresExecutor{}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := executor.EscapeIdentifier(tt.identifier)
			if got != tt.want {
				t.Errorf("EscapeIdentifier(%q) = %q; want %q", tt.identifier, got, tt.want)
			}
		})
	}
}

func TestReplaceNamespace(t *testing.T) {
	tests := []struct {
		name      string
		query     string
		namespace string
		want      string
	}{
		{
			name:      "Empty query returns empty string",
			namespace: "NS_",
			query:     "",
			want:      "",
		},
		{
			name:      "Query without markers remains unchanged",
			namespace: "NS_",
			query:     "SELECT * FROM sometable WHERE id = 1",
			want:      "SELECT * FROM sometable WHERE id = 1",
		},
		{
			name:      "No namespace: simple marker replacement",
			namespace: "",
			query:     "SELECT * FROM {{results_table}} WHERE col = 1",
			// With no namespace, the marker is replaced with the raw table name properly escaped.
			want: `SELECT * FROM "results_table" WHERE col = 1`,
		},
		{
			name:      "With namespace: prepend namespace to table name",
			namespace: "DEV",
			query:     "SELECT * FROM {{results_table}}",
			// With namespace, the full table name becomes DEVresults_table and is quoted.
			want: `SELECT * FROM "DEVresults_table"`,
		},
		{
			name:      "Consecutive markers without space",
			namespace: "NS_",
			query:     "INSERT INTO {{table1}}{{table2}} VALUES (1,2)",
			// Each marker is replaced independently.
			want: `INSERT INTO "NS_table1""NS_table2" VALUES (1,2)`,
		},
		{
			name:      "Marker with extra whitespace",
			namespace: "PRE_",
			query:     "SELECT * FROM {{   table_name   }}",
			// Extra whitespace is trimmed.
			want: `SELECT * FROM "PRE_table_name"`,
		},
		{
			name:      "Marker with empty content",
			namespace: "PRE_",
			query:     "SELECT * FROM {{    }}",
			// When the marker is empty, namespace is prepended to an empty string.
			want: `SELECT * FROM "PRE_"`,
		},
		{
			name:      "Multiple markers in one query with mixed whitespace",
			namespace: "NS_",
			query:     "UPDATE {{ table1 }} SET col = 2; SELECT * FROM {{table2}}",
			want:      `UPDATE "NS_table1" SET col = 2; SELECT * FROM "NS_table2"`,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			executor := &PostgresExecutor{Config: DatabaseConfig{Namespace: tt.namespace}}
			got := executor.ReplaceNamespace(tt.query)
			if got != tt.want {
				t.Errorf("ReplaceNamespace(%q) with namespace %q = %q; want %q", tt.query, tt.namespace, got, tt.want)
			}
		})
	}
}

func TestGetFieldMap(t *testing.T) {
	// Define test structs
	type BasicStruct struct {
		ID   int
		Name string
	}

	type WithDBTags struct {
		ID       int    `db:"user_id"`
		Name     string `db:"user_name"`
		Email    string `db:"email_address"`
		Internal string // No db tag
	}

	type WithMixedTags struct {
		ID    int    `db:"user_id"`
		Name  string // No db tag
		Email string `db:"email_address"`
	}

	type WithEmptyTag struct {
		ID    int    `db:"user_id"`
		Title string `db:""`
	}

	type WithUnexportedFields struct {
		ID      int    `db:"user_id"`
		Name    string `db:"user_name"`
		email   string `db:"email"` // Unexported
		private int    // Unexported, no db tag
	}

	tests := []struct {
		name                 string
		structType           reflect.Type
		expectedFieldMap     map[string]int
		expectedExportedMap  map[int]bool
		expectedFieldCount   int
		expectedExportedOnly bool
	}{
		{
			name:       "Basic struct with no tags",
			structType: reflect.TypeOf(BasicStruct{}),
			expectedFieldMap: map[string]int{
				"id":   0,
				"name": 1,
			},
			expectedExportedMap: map[int]bool{
				0: true,
				1: true,
			},
			expectedFieldCount:   2,
			expectedExportedOnly: true,
		},
		{
			name:       "Struct with DB tags",
			structType: reflect.TypeOf(WithDBTags{}),
			expectedFieldMap: map[string]int{
				"user_id":       0,
				"user_name":     1,
				"email_address": 2,
				"internal":      3,
			},
			expectedExportedMap: map[int]bool{
				0: true,
				1: true,
				2: true,
				3: true,
			},
			expectedFieldCount:   4,
			expectedExportedOnly: true,
		},
		{
			name:       "Struct with mixed tags",
			structType: reflect.TypeOf(WithMixedTags{}),
			expectedFieldMap: map[string]int{
				"user_id":       0,
				"name":          1,
				"email_address": 2,
			},
			expectedExportedMap: map[int]bool{
				0: true,
				1: true,
				2: true,
			},
			expectedFieldCount:   3,
			expectedExportedOnly: true,
		},
		{
			name:       "Struct with empty tag",
			structType: reflect.TypeOf(WithEmptyTag{}),
			expectedFieldMap: map[string]int{
				"user_id": 0,
				"title":   1, // Should use lowercase field name
			},
			expectedExportedMap: map[int]bool{
				0: true,
				1: true,
			},
			expectedFieldCount:   2,
			expectedExportedOnly: true,
		},
		{
			name:       "Struct with unexported fields",
			structType: reflect.TypeOf(WithUnexportedFields{}),
			expectedFieldMap: map[string]int{
				"user_id":   0,
				"user_name": 1,
				"email":     2,
				"private":   3,
			},
			expectedExportedMap: map[int]bool{
				0: true,
				1: true,
				2: false, // Unexported
				3: false, // Unexported
			},
			expectedFieldCount:   4,
			expectedExportedOnly: false,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			fieldMap, exportedFields := getFieldMap(tc.structType)

			// Check field map size
			if len(fieldMap) != tc.expectedFieldCount {
				t.Errorf("expected field map to have %d entries, got %d", tc.expectedFieldCount, len(fieldMap))
			}

			// Check field map entries
			for key, expectedIdx := range tc.expectedFieldMap {
				idx, ok := fieldMap[key]
				if !ok {
					t.Errorf("expected field map to contain key %q but it didn't", key)
					continue
				}
				if idx != expectedIdx {
					t.Errorf("for key %q expected index %d, got %d", key, expectedIdx, idx)
				}
			}

			// Check exported fields map
			for idx, expectedExported := range tc.expectedExportedMap {
				exported, ok := exportedFields[idx]
				if !ok {
					t.Errorf("expected exported map to contain index %d but it didn't", idx)
					continue
				}
				if exported != expectedExported {
					t.Errorf("for index %d expected exported=%v, got %v", idx, expectedExported, exported)
				}
			}

			// Check if all fields are exported when expected
			if tc.expectedExportedOnly {
				for idx, exported := range exportedFields {
					if !exported {
						t.Errorf("expected all fields to be exported but field at index %d is not exported", idx)
					}
				}
			}
		})
	}
}

func TestQueryRowStruct_RowsError(t *testing.T) {
	// Create a new sqlmock database for this test
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("failed to create sqlmock: %v", err)
	}
	defer db.Close()

	executor := &PostgresExecutor{
		DB:  db,
		Ctx: context.Background(),
	}

	// Create a destination struct
	var p Person

	// Set up mock to return a rows.Err() error
	mock.ExpectQuery("^SELECT").
		WillReturnRows(sqlmock.NewRows([]string{"id", "name"}).
			AddRow(1, "Alice").
			RowError(0, fmt.Errorf("row error")))

	// Execute the query
	err = executor.QueryRowStruct(&p, "SELECT id, name FROM people")

	// Verify error is returned
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "row error")

	// Verify all expectations were met
	if err := mock.ExpectationsWereMet(); err != nil {
		t.Errorf("there were unfulfilled expectations: %v", err)
	}
}

func TestPgScanRowsIntoStruct_EdgeCases(t *testing.T) {
	tests := []struct {
		name        string
		columns     []string
		setupStruct func() reflect.Value
		fieldMap    map[string]int
		exportedMap map[int]bool
		mockRow     func() *sqlmock.Rows
		expectedErr string
	}{
		{
			name:    "Column count mismatch",
			columns: []string{"id", "name", "extra"},
			setupStruct: func() reflect.Value {
				return reflect.ValueOf(&Person{}).Elem()
			},
			fieldMap: map[string]int{
				"id":   0,
				"name": 1,
			},
			exportedMap: map[int]bool{
				0: true,
				1: true,
			},
			mockRow: func() *sqlmock.Rows {
				return sqlmock.NewRows([]string{"id", "name", "extra"}).
					AddRow(1, "Alice", "extra")
			},
			expectedErr: "no matching struct field found for column \"extra\"",
		},
		{
			name:    "Scan error due to type mismatch",
			columns: []string{"id", "name"},
			setupStruct: func() reflect.Value {
				return reflect.ValueOf(&Person{}).Elem()
			},
			fieldMap: map[string]int{
				"id":   0,
				"name": 1,
			},
			exportedMap: map[int]bool{
				0: true,
				1: true,
			},
			mockRow: func() *sqlmock.Rows {
				return sqlmock.NewRows([]string{"id", "name"}).
					AddRow("not-an-int", "Alice")
			},
			expectedErr: "sql: Scan error",
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			// Create mock database
			db, mock, err := sqlmock.New()
			if err != nil {
				t.Fatalf("failed to create sqlmock: %v", err)
			}
			defer db.Close()

			// Set up mock rows
			rows := tc.mockRow()
			mock.ExpectQuery("^SELECT").WillReturnRows(rows)

			// Execute query to get *sql.Rows
			sqlRows, err := db.Query("SELECT")
			if err != nil {
				t.Fatalf("failed to execute query: %v", err)
			}
			defer sqlRows.Close()

			// Move to first row
			sqlRows.Next()

			// Test pgScanRowsIntoStruct
			err = pgScanRowsIntoStruct(
				sqlRows,
				tc.setupStruct(),
				tc.fieldMap,
				tc.exportedMap,
				tc.columns,
			)

			if tc.expectedErr != "" {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tc.expectedErr)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestQueryGeneric_RowsError(t *testing.T) {
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("failed to create sqlmock: %v", err)
	}
	defer db.Close()

	executor := &PostgresExecutor{
		DB:  db,
		Ctx: context.Background(),
	}

	// Set up mock to return a rows.Err() error
	rows := sqlmock.NewRows([]string{"id", "name"}).
		AddRow(1, "Alice").
		RowError(0, fmt.Errorf("row error"))
	mock.ExpectQuery("SELECT").WillReturnRows(rows)

	// Execute query that will fail during row iteration
	_, err = executor.QueryGeneric("SELECT id, name FROM people")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "row error")
}

func TestQueryGeneric_ScanError(t *testing.T) {
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("failed to create sqlmock: %v", err)
	}
	defer db.Close()

	executor := &PostgresExecutor{
		DB:  db,
		Ctx: context.Background(),
	}

	// Set up mock to return a row that will cause a scan error
	// Using a string value for a column that will be scanned as int
	columns := []string{"id"}
	rows := sqlmock.NewRows(columns).
		AddRow("not-an-int").
		RowError(0, fmt.Errorf("sql: Scan error on column index 0: converting driver.Value type string (\"not-an-int\") to a int64: invalid syntax"))

	mock.ExpectQuery(regexp.QuoteMeta("SELECT id FROM people")).
		WillReturnRows(rows)

	// Execute query that will fail during scan
	result, err := executor.QueryGeneric("SELECT id FROM people")

	// Verify error is returned and result is nil
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "sql: Scan error")
	assert.Nil(t, result)

	// Verify all expectations were met
	if err := mock.ExpectationsWereMet(); err != nil {
		t.Errorf("there were unfulfilled expectations: %v", err)
	}
}

func TestQueryGeneric_QueryError(t *testing.T) {
	// Create a new sqlmock database
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("failed to create sqlmock: %v", err)
	}
	defer db.Close()

	executor := &PostgresExecutor{
		DB:  db,
		Ctx: context.Background(),
	}

	// Set up mock to return an error for the query execution
	expectedErr := fmt.Errorf("query execution failed")
	mock.ExpectQuery(regexp.QuoteMeta("SELECT * FROM test_table")).
		WillReturnError(expectedErr)

	// Execute query that should fail
	result, err := executor.QueryGeneric("SELECT * FROM test_table")

	// Verify that the error is returned and result is nil
	if err != expectedErr {
		t.Errorf("expected error %v, got %v", expectedErr, err)
	}
	if result != nil {
		t.Errorf("expected nil result, got %v", result)
	}

	// Verify all expectations were met
	if err := mock.ExpectationsWereMet(); err != nil {
		t.Errorf("there were unfulfilled expectations: %v", err)
	}
}

func TestQueryGenericSlice_ScanError(t *testing.T) {
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("failed to create sqlmock: %v", err)
	}
	defer db.Close()

	executor := &PostgresExecutor{
		DB:  db,
		Ctx: context.Background(),
	}

	// Set up mock to return data that will cause a scan error
	rows := sqlmock.NewRows([]string{"id", "name"}).
		AddRow("not-an-int", "Alice") // This will cause a scan error for ID field
	mock.ExpectQuery("SELECT").WillReturnRows(rows)

	var people []Person
	err = executor.QueryGenericSlice(&people, "SELECT id, name FROM people")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "sql: Scan error")
}

func TestExec_Error(t *testing.T) {
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("failed to create sqlmock: %v", err)
	}
	defer db.Close()

	executor := &PostgresExecutor{
		DB:  db,
		Ctx: context.Background(),
	}

	// Set up mock to return an error
	mock.ExpectExec("INSERT").WillReturnError(fmt.Errorf("exec error"))

	_, err = executor.Exec("INSERT INTO people (name) VALUES (?)", "Alice")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "exec error")
}

func TestQueryRow_NoRows(t *testing.T) {
	// Create a new sqlmock database
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("failed to create sqlmock: %v", err)
	}
	defer db.Close()

	executor := &PostgresExecutor{
		DB:  db,
		Ctx: context.Background(),
	}

	// Set up mock to return no rows
	mock.ExpectQuery("SELECT").WillReturnRows(sqlmock.NewRows([]string{"id", "name"}))

	// Execute query that will return no rows
	result, err := executor.QueryRow("SELECT id, name FROM people WHERE id = $1", 999)

	// Verify that sql.ErrNoRows is returned
	assert.Error(t, err)
	assert.Equal(t, sql.ErrNoRows, err)
	assert.Nil(t, result)

	// Verify all expectations were met
	if err := mock.ExpectationsWereMet(); err != nil {
		t.Errorf("there were unfulfilled expectations: %v", err)
	}
}

func TestQueryRow_QueryError(t *testing.T) {
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("failed to create sqlmock: %v", err)
	}
	defer db.Close()

	executor := &PostgresExecutor{
		DB:  db,
		Ctx: context.Background(),
	}

	// Set up mock to return an error
	expectedErr := fmt.Errorf("database error")
	mock.ExpectQuery("SELECT").WillReturnError(expectedErr)

	// Execute query that will fail
	_, err = executor.QueryRow("SELECT id, name FROM people WHERE id = $1", 1)

	// Verify error is returned
	assert.Error(t, err)
	assert.Equal(t, expectedErr, err)
}

func TestQueryRow_RowsError(t *testing.T) {
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("failed to create sqlmock: %v", err)
	}
	defer db.Close()

	executor := &PostgresExecutor{
		DB:  db,
		Ctx: context.Background(),
	}

	// Create rows that will return an error during iteration
	expectedErr := fmt.Errorf("rows error")
	rows := sqlmock.NewRows([]string{"id", "name"}).
		AddRow(1, "Alice").
		RowError(0, expectedErr)

	mock.ExpectQuery(regexp.QuoteMeta("SELECT id, name FROM people")).
		WillReturnRows(rows)

	// Execute query that will fail during row iteration
	result, err := executor.QueryRow("SELECT id, name FROM people")

	// Verify error is returned and result is nil
	assert.Error(t, err)
	assert.Equal(t, expectedErr, err)
	assert.Nil(t, result)

	// Verify all expectations were met
	if err := mock.ExpectationsWereMet(); err != nil {
		t.Errorf("there were unfulfilled expectations: %v", err)
	}
}

func TestQueryRow_ColumnsError(t *testing.T) {
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("failed to create sqlmock: %v", err)
	}
	defer db.Close()

	executor := &PostgresExecutor{
		DB:  db,
		Ctx: context.Background(),
	}

	// Create rows that will return an error on Columns() call
	rows := sqlmock.NewRows([]string{"id"}).CloseError(fmt.Errorf("columns error"))
	mock.ExpectQuery("SELECT").WillReturnRows(rows)

	// Execute query that will fail during columns retrieval
	_, err = executor.QueryRow("SELECT id FROM people")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "columns error")
}

func TestQueryRow_ScanError(t *testing.T) {
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("failed to create sqlmock: %v", err)
	}
	defer db.Close()

	executor := &PostgresExecutor{
		DB:  db,
		Ctx: context.Background(),
	}

	// Set up mock to return a row that will cause a scan error
	rows := sqlmock.NewRows([]string{"id", "name"}).
		AddRow("not-an-int", "Alice")
	mock.ExpectQuery(regexp.QuoteMeta("SELECT id, name FROM people")).
		WillReturnRows(rows)

	// Execute query that will fail during scan
	var dest struct {
		ID   int    `db:"id"`
		Name string `db:"name"`
	}
	err = executor.QueryRowStruct(&dest, "SELECT id, name FROM people")

	// Verify error is returned
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "sql: Scan error")

	// Verify all expectations were met
	if err := mock.ExpectationsWereMet(); err != nil {
		t.Errorf("there were unfulfilled expectations: %v", err)
	}
}

func TestQueryRow_MultipleRows(t *testing.T) {
	// Create a new sqlmock database
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("failed to create sqlmock: %v", err)
	}
	defer db.Close()

	executor := &PostgresExecutor{
		DB:  db,
		Ctx: context.Background(),
	}

	// Set up mock to return multiple rows
	rows := sqlmock.NewRows([]string{"id", "name"}).
		AddRow(1, "Alice").
		AddRow(2, "Bob")
	mock.ExpectQuery("SELECT").WillReturnRows(rows)

	// Execute query that will return multiple rows
	result, err := executor.QueryRow("SELECT id, name FROM people")

	// Verify only first row is returned
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, int64(1), result["id"])
	assert.Equal(t, "Alice", result["name"])

	// Verify all expectations were met
	if err := mock.ExpectationsWereMet(); err != nil {
		t.Errorf("there were unfulfilled expectations: %v", err)
	}
}

func TestClose(t *testing.T) {
	tests := []struct {
		name        string
		mockSetup   func(mock sqlmock.Sqlmock)
		expectedErr error
	}{
		{
			name: "Close succeeds",
			mockSetup: func(mock sqlmock.Sqlmock) {
				mock.ExpectClose().WillReturnError(nil)
			},
			expectedErr: nil,
		},
		{
			name: "Close returns error",
			mockSetup: func(mock sqlmock.Sqlmock) {
				mock.ExpectClose().WillReturnError(fmt.Errorf("connection already closed"))
			},
			expectedErr: fmt.Errorf("connection already closed"),
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			// Create a new sqlmock database for this test
			db, mock, err := sqlmock.New()
			if err != nil {
				t.Fatalf("failed to create sqlmock: %v", err)
			}

			// Set up the mock expectations
			tc.mockSetup(mock)

			// Create a new PostgresExecutor instance
			executor := &PostgresExecutor{
				DB:  db,
				Ctx: context.Background(),
			}

			// Call the Close function
			err = executor.Close()

			// Check if the error matches the expected error
			if tc.expectedErr == nil {
				if err != nil {
					t.Errorf("expected no error, got %v", err)
				}
			} else {
				if err == nil {
					t.Errorf("expected error %v, got nil", tc.expectedErr)
				} else if err.Error() != tc.expectedErr.Error() {
					t.Errorf("expected error %v, got %v", tc.expectedErr, err)
				}
			}

			// Verify that all expectations were met
			if err := mock.ExpectationsWereMet(); err != nil {
				t.Errorf("there were unfulfilled expectations: %v", err)
			}
		})
	}
}

func TestQueryRowStruct_Error(t *testing.T) {
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("failed to create mock: %v", err)
	}
	defer db.Close()

	executor := &PostgresExecutor{
		DB:  db,
		Ctx: context.Background(),
	}

	mock.ExpectQuery("SELECT").WillReturnError(fmt.Errorf("database error"))

	var dest struct {
		ID   int    `db:"id"`
		Name string `db:"name"`
	}

	err = executor.QueryRowStruct(&dest, "SELECT 1")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "database error")
}

func TestQueryGenericSlice_Error(t *testing.T) {
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("failed to create mock: %v", err)
	}
	defer db.Close()

	executor := &PostgresExecutor{
		DB:  db,
		Ctx: context.Background(),
	}

	mock.ExpectQuery("SELECT").WillReturnError(fmt.Errorf("database error"))

	var dest []struct {
		ID   int    `db:"id"`
		Name string `db:"name"`
	}

	err = executor.QueryGenericSlice(&dest, "SELECT 1")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "database error")
}

func TestQueryGenericSlice_ColumnsError(t *testing.T) {
	db, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("failed to create sqlmock: %v", err)
	}
	defer db.Close()

	executor := &PostgresExecutor{
		DB:  db,
		Ctx: context.Background(),
	}

	// Create rows that will return an error on Columns() call
	rows := sqlmock.NewRows([]string{"id"}).CloseError(fmt.Errorf("columns error"))
	mock.ExpectQuery("SELECT").WillReturnRows(rows)

	var dest []Person
	err = executor.QueryGenericSlice(&dest, "SELECT id FROM people")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "columns error")
}

func TestPostgres_ConfigErrors(t *testing.T) {
	// Save original osGetenv and restore it after the test
	restore := savePostgresVars()
	defer restore()

	tests := []struct {
		name        string
		config      PostgresConfig
		expectedErr error
		errContains string
	}{
		{
			name: "Missing user",
			config: PostgresConfig{
				User:     "",
				Password: "pass",
				DB:       "db",
				Host:     "localhost",
				Port:     "5432",
			},
			expectedErr: ErrPostgresUserNotSet,
		},
		{
			name: "Missing password",
			config: PostgresConfig{
				User:     "user",
				Password: "",
				DB:       "db",
				Host:     "localhost",
				Port:     "5432",
			},
			expectedErr: ErrPostgresPasswordNotSet,
		},
		{
			name: "Missing DB",
			config: PostgresConfig{
				User:     "user",
				Password: "pass",
				DB:       "",
				Host:     "localhost",
				Port:     "5432",
			},
			expectedErr: ErrPostgresDBNotSet,
		},
		{
			name: "Missing host",
			config: PostgresConfig{
				User:     "user",
				Password: "pass",
				DB:       "db",
				Host:     "",
				Port:     "5432",
			},
			expectedErr: ErrPostgresHostNotSet,
		},
		{
			name: "Missing port",
			config: PostgresConfig{
				User:     "user",
				Password: "pass",
				DB:       "db",
				Host:     "localhost",
				Port:     "",
			},
			expectedErr: ErrPostgresPortNotSet,
		},
		{
			name: "Connection error with unreachable host",
			config: PostgresConfig{
				User:     "user",
				Password: "pass",
				DB:       "db",
				Host:     "nonexistent-host",
				Port:     "5432",
			},
			errContains: "Failed to connect to PostgreSQL",
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			osGetenv = MockGetEnvFromConfig(tc.config)

			// create the mock DB + expectation
			mockDB, mock, err := sqlmock.New(sqlmock.MonitorPingsOption(true))
			if err != nil {
				t.Fatalf("failed to open sqlmock: %v", err)
			}
			// Expect exactly one Ping, and return an error immediately
			mock.ExpectPing().WillReturnError(errors.New("ping boom"))
			// Override sqlOpen
			sqlOpen = func(driverName, dsn string) (*sql.DB, error) {
				return mockDB, nil
			}

			_, err = Postgres(context.Background(), nil)
			if tc.expectedErr != nil {
				assert.ErrorIs(t, err, tc.expectedErr)
			} else if tc.errContains != "" {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tc.errContains)
			}
		})
	}
}

func TestPostgres_configNil(t *testing.T) {
	restore := savePostgresVars()
	defer restore()

	tests := []struct {
		name          string
		config        *DatabaseConfig
		expectedCfg   DatabaseConfig
		wantErr       bool
		expectedError error
	}{
		{
			name: "use provided config instead of env vars",
			config: &DatabaseConfig{
				DBName:      "test_db",
				Environment: "test",
				Namespace:   "TEST_",
			},
			expectedCfg: DatabaseConfig{
				DBName:      "test_db",
				Environment: "test",
				Namespace:   "TEST_",
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// create the mock DB + expectation
			mockDB, mock, err := sqlmock.New(sqlmock.MonitorPingsOption(true))
			if err != nil {
				t.Fatalf("failed to open sqlmock: %v", err)
			}
			// Expect exactly one Ping, and return an error immediately
			mock.ExpectPing().WillReturnError(errors.New("ping boom"))
			// Override sqlOpen
			sqlOpen = func(driverName, dsn string) (*sql.DB, error) {
				return mockDB, nil
			}
			// Execute test
			_, err = Postgres(context.Background(), tt.config)

			// Verify results
			assert.Error(t, err)
			if tt.expectedError != nil {
				assert.Equal(t, tt.expectedError, err)
			}
		})
	}
}

func Test_Postgres_ExecutorInitialization(t *testing.T) {
	// Save original osGetenv and restore it after the test
	restore := savePostgresVars()
	defer restore()

	postgresConfig := PostgresConfig{
		User:     "user",
		Password: "pass",
		DB:       "db",
		Host:     "localhost",
		Port:     "5432",
	}

	config := DatabaseConfig{
		DBName:      "test_db",
		Environment: "test",
		Namespace:   "TEST_",
	}

	// Mock env var
	osGetenv = MockGetEnvFromConfig(postgresConfig)

	// Create a mock DB
	mockDB, mock, err := sqlmock.New(sqlmock.MonitorPingsOption(true))
	if err != nil {
		t.Fatalf("failed to create mock db: %v", err)
	}
	defer mockDB.Close()

	// Create test context
	ctx := context.Background()
	// Expect Ping to succeed
	mock.ExpectPing().WillReturnError(nil)

	// Fake sql.Open
	sqlOpen = func(driver, dsn string) (*sql.DB, error) {
		return mockDB, nil
	}

	// Execute test
	executor, err := Postgres(ctx, &config)

	// Verify results
	assert.NoError(t, err)
	assert.NotNil(t, executor)
	assert.Equal(t, ctx, executor.Ctx)
	assert.Equal(t, mockDB, executor.DB)
}

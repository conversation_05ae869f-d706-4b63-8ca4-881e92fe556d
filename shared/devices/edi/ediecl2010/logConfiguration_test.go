package ediecl2010

import (
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/pubsubdata"
)

func TestLogConfiguration(t *testing.T) {
	tests := []struct {
		name          string
		byteMsg       []byte
		header        *helper.HeaderRecord
		httpHeader    *pubsubdata.HeaderDetails
		expectedError error
		validate      func(t *testing.T, records *helper.ConfigurationChangeLogRecords)
	}{
		{
			name:       "valid configuration change log",
			byteMsg:    buildValidConfigurationChangeLogMsg(1),
			header:     createValidConfigHeader(false),
			httpHeader: createValidConfigHTTPHeader(),
			validate: func(t *testing.T, records *helper.ConfigurationChangeLogRecords) {
				require.NotNil(t, records)
				assert.Equal(t, DeviceModel, records.DeviceModel)
				assert.Equal(t, buildValidConfigurationChangeLogMsg(1), records.RawMessage)
				require.Len(t, records.Record, 1)
			},
		},
		{
			name:          "nil header",
			byteMsg:       buildValidConfigurationChangeLogMsg(1),
			header:        nil,
			httpHeader:    createValidConfigHTTPHeader(),
			expectedError: helper.ErrMsgHeaderRecordNil,
		},
		{
			name:          "invalid checksum",
			byteMsg:       createInvalidConfigurationChangeLogChecksumMsg(),
			header:        createValidConfigHeader(false),
			httpHeader:    createValidConfigHTTPHeader(),
			expectedError: helper.ErrMsgByteChecksum,
		},
		{
			name:          "unsupported device model",
			byteMsg:       buildValidConfigurationChangeLogMsg(1),
			header:        createUnsupportedDeviceHeader(),
			httpHeader:    createValidConfigHTTPHeader(),
			expectedError: helper.ErrUnsupportedDevice,
		},
		{
			name:          "invalid message length",
			byteMsg:       createInvalidConfigurationChangeLogLengthMsg(),
			header:        createValidConfigHeader(false),
			httpHeader:    createValidConfigHTTPHeader(),
			expectedError: helper.ErrMsgByteLen,
		},
		{
			name:          "invalid BCD date-time in record",
			byteMsg:       createInvalidConfigurationChangeLogBCDMsg(),
			header:        createValidConfigHeader(false),
			httpHeader:    createValidConfigHTTPHeader(),
			expectedError: fmt.Errorf("failed to parse configuration change log record 0: bad month - month reported = 255 invalid BCD byte : 0xFF (nibbles 15,15)"),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			device := EDIECL2010{}
			records, err := device.LogConfiguration(tt.httpHeader, tt.byteMsg, tt.header)

			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.ErrorContains(t, err, tt.expectedError.Error())
				assert.Nil(t, records)
			} else {
				assert.NoError(t, err)
				if tt.validate != nil && records != nil {
					tt.validate(t, records)
				}
			}
		})
	}
}

func TestParseConflictMap(t *testing.T) {
	tests := []struct {
		name     string
		input    []byte
		expected [18]int
	}{
		{
			name: "all zeros",
			input: func() []byte {
				bytes := make([]byte, 54)
				for i := range bytes {
					bytes[i] = 0x00
				}
				return bytes
			}(),
			expected: [18]int{0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0},
		},
		{
			name: "all ones",
			input: func() []byte {
				bytes := make([]byte, 54)
				for i := range bytes {
					bytes[i] = 0xFF
				}
				return bytes
			}(),
			expected: [18]int{0xFFFFFF, 0xFFFFFF, 0xFFFFFF, 0xFFFFFF, 0xFFFFFF, 0xFFFFFF, 0xFFFFFF, 0xFFFFFF, 0xFFFFFF, 0xFFFFFF, 0xFFFFFF, 0xFFFFFF, 0xFFFFFF, 0xFFFFFF, 0xFFFFFF, 0xFFFFFF, 0xFFFFFF, 0xFFFFFF},
		},
		{
			name: "alternating pattern",
			input: func() []byte {
				bytes := make([]byte, 54)
				for i := 0; i < 54; i += 3 {
					bytes[i] = 0xAA
					bytes[i+1] = 0x55
					bytes[i+2] = 0x00
				}
				return bytes
			}(),
			expected: [18]int{0xAA5500, 0xAA5500, 0xAA5500, 0xAA5500, 0xAA5500, 0xAA5500, 0xAA5500, 0xAA5500, 0xAA5500, 0xAA5500, 0xAA5500, 0xAA5500, 0xAA5500, 0xAA5500, 0xAA5500, 0xAA5500, 0xAA5500, 0xAA5500},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := parseConflictMap(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestParseEnableFlags(t *testing.T) {
	tests := []struct {
		name     string
		value    int
		count    int
		expected []bool
	}{
		{
			name:     "all false",
			value:    0,
			count:    16,
			expected: make([]bool, 16),
		},
		{
			name:  "all true",
			value: 0xFFFF,
			count: 16,
			expected: func() []bool {
				result := make([]bool, 16)
				for i := range result {
					result[i] = true
				}
				return result
			}(),
		},
		{
			name:  "alternating pattern",
			value: 0x5555,
			count: 16,
			expected: func() []bool {
				result := make([]bool, 16)
				for i := range result {
					result[i] = i%2 == 0
				}
				return result
			}(),
		},
		{
			name:  "first bit only",
			value: 1,
			count: 16,
			expected: func() []bool {
				result := make([]bool, 16)
				result[0] = true
				return result
			}(),
		},
		{
			name:  "last bit only",
			value: 0x8000,
			count: 16,
			expected: func() []bool {
				result := make([]bool, 16)
				result[15] = true
				return result
			}(),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := parseEnableFlags(tt.value, tt.count)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestParseOptions1(t *testing.T) {
	tests := []struct {
		name     string
		options1 byte
		param    byte
		expected *helper.ConfigurationChangeLogRecord
	}{
		{
			name:     "all default values",
			options1: 0x00,
			param:    0x00,
			expected: &helper.ConfigurationChangeLogRecord{
				RedFaultTiming:       "700-1000 ms",
				RecurrentPulse:       true,
				WatchdogTiming:       "1.5 seconds",
				WatchdogEnableSwitch: false,
				GYEnable:             false,
				LEDguardThresholds:   false,
				RedFailEnabledbySSM:  false,
			},
		},
		{
			name:     "all enabled",
			options1: 0x6D, // 01101101
			param:    0x01,
			expected: &helper.ConfigurationChangeLogRecord{
				RedFaultTiming:       "1200-1500 ms",
				RecurrentPulse:       true, // Bit 0x02 is not set in 0x6D
				WatchdogTiming:       "1 second",
				WatchdogEnableSwitch: true,
				GYEnable:             true,
				LEDguardThresholds:   true,
				RedFailEnabledbySSM:  true,
			},
		},
		{
			name:     "recurrent pulse disabled",
			options1: 0x02, // Only bit 0x02 set
			param:    0x00,
			expected: &helper.ConfigurationChangeLogRecord{
				RedFaultTiming:       "700-1000 ms",
				RecurrentPulse:       false,
				WatchdogTiming:       "1.5 seconds",
				WatchdogEnableSwitch: false,
				GYEnable:             false,
				LEDguardThresholds:   false,
				RedFailEnabledbySSM:  false,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			record := &helper.ConfigurationChangeLogRecord{}
			parseOptions1(tt.options1, tt.param, record)
			assert.Equal(t, tt.expected, record)
		})
	}
}

func TestParseFlashingYellowArrows(t *testing.T) {
	tests := []struct {
		name     string
		options1 byte
		options2 byte
		expected *helper.ConfigurationChangeLogRecord
	}{
		{
			name:     "none enabled",
			options1: 0x00,
			options2: 0x00,
			expected: &helper.ConfigurationChangeLogRecord{
				FlashingYellowArrows: []string{"<none>"},
			},
		},
		{
			name:     "all enabled with FYAC mode",
			options1: 0x80,
			options2: 0x0F,
			expected: &helper.ConfigurationChangeLogRecord{
				FlashingYellowArrows: []string{"1-9", "3-10", "5-11", "7-12", "(Mode=FYAC)"},
			},
		},
		{
			name:     "partial enabled with FYA mode",
			options1: 0x00,
			options2: 0x05,
			expected: &helper.ConfigurationChangeLogRecord{
				FlashingYellowArrows: []string{"1-9", "5-11", "(Mode=FYA)"},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			record := &helper.ConfigurationChangeLogRecord{}
			parseFlashingYellowArrows(tt.options1, tt.options2, record)
			assert.Equal(t, tt.expected, record)
		})
	}
}

func TestParseSelect1(t *testing.T) {
	tests := []struct {
		name     string
		select1  byte
		expected *helper.ConfigurationChangeLogRecord
	}{
		{
			name:    "all default values",
			select1: 0x00,
			expected: &helper.ConfigurationChangeLogRecord{
				WDTErrorClearonPU: true,
				MinimumFlash:      false,
				ConfigChangeFault: false,
				RedCableFault:     false,
				AcLineBrownout:    "92 +/- 2 Vrms",
			},
		},
		{
			name:    "all enabled",
			select1: 0x1F,
			expected: &helper.ConfigurationChangeLogRecord{
				WDTErrorClearonPU: false,
				MinimumFlash:      true,
				ConfigChangeFault: true,
				RedCableFault:     true,
				AcLineBrownout:    "98 +/- 2Vrms",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			record := &helper.ConfigurationChangeLogRecord{}
			parseSelect1(tt.select1, record)
			assert.Equal(t, tt.expected, record)
		})
	}
}

func TestParseSelect2(t *testing.T) {
	tests := []struct {
		name     string
		select2  byte
		expected *helper.ConfigurationChangeLogRecord
	}{
		{
			name:    "all default values",
			select2: 0x00,
			expected: &helper.ConfigurationChangeLogRecord{
				PinEEPolarity:             "NORMAL",
				DualIndicationFaultTiming: "350 - 500 ms",
				FYAFlashRateFault:         true,
			},
		},
		{
			name:    "all enabled",
			select2: 0x43,
			expected: &helper.ConfigurationChangeLogRecord{
				PinEEPolarity:             "INVERT",
				DualIndicationFaultTiming: "700 - 1000 ms",
				FYAFlashRateFault:         false,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			record := &helper.ConfigurationChangeLogRecord{}
			parseSelect2(tt.select2, record)
			assert.Equal(t, tt.expected, record)
		})
	}
}

func TestGetIntFromLhMsHs(t *testing.T) {
	tests := []struct {
		name     string
		lh       byte
		ms       byte
		hs       byte
		expected int
	}{
		{
			name:     "all zeros",
			lh:       0x00,
			ms:       0x00,
			hs:       0x00,
			expected: 0,
		},
		{
			name:     "all ones",
			lh:       0xFF,
			ms:       0xFF,
			hs:       0xFF,
			expected: 0xFFFFFF,
		},
		{
			name:     "alternating pattern",
			lh:       0xAA,
			ms:       0x55,
			hs:       0x00,
			expected: 0xAA5500,
		},
		{
			name:     "individual bytes",
			lh:       0x01,
			ms:       0x02,
			hs:       0x03,
			expected: 0x010203,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := getIntFromLhMsHs(tt.lh, tt.ms, tt.hs)
			assert.Equal(t, tt.expected, result)
		})
	}
}

// Helper functions to create test data
func createValidConfigHeader(mainsDC bool) *helper.HeaderRecord {
	return &helper.HeaderRecord{
		Model:          helper.Ecl2010,
		MainsDC:        mainsDC,
		PowerDownLevel: 100,
	}
}

func createValidConfigHTTPHeader() *pubsubdata.HeaderDetails {
	return &pubsubdata.HeaderDetails{
		GatewayTimezone: "UTC",
	}
}

func createUnsupportedDeviceHeader() *helper.HeaderRecord {
	return &helper.HeaderRecord{
		Model:            9999, // not helper.Ecl2010
		FirmwareRevision: 0x51, // > 0x50
	}
}

func buildValidConfigurationChangeLogMsg(numRecords int) []byte {
	// Create header
	msg := make([]byte, HeaderLength)
	msg[0] = 0x01 // Message type
	msg[1] = 0x02 // Device address
	msg[2] = 0x03 // Command code
	msg[3] = 0x00 // Response status
	msg[4] = 0x00 // Reserved byte
	msg[5] = 0x00 // Reserved byte
	msg[6] = 0x00 // Reserved byte

	// Add number of records
	msg = append(msg, byte(numRecords))

	// Create records
	for i := 0; i < numRecords; i++ {
		record := make([]byte, CF2018LogLength)

		// Conflict map (54 bytes)
		for j := 0; j < 54; j++ {
			record[j] = 0x00 // Initialize conflict map to all zeros
		}

		// Enable flags (18 bytes)
		// Red Fail Enable
		record[51] = 0x00 // LH
		record[52] = 0x00 // MS
		record[53] = 0x01 // HS (bit 0 set)

		// Green Yellow Dual Enable
		record[54] = 0x00 // LH
		record[55] = 0x00 // MS
		record[56] = 0x02 // HS (bit 1 set)

		// Yellow Red Dual Enable
		record[57] = 0x00 // LH
		record[58] = 0x00 // MS
		record[59] = 0x04 // HS (bit 2 set)

		// Green Red Dual Enable
		record[60] = 0x00 // LH
		record[61] = 0x00 // MS
		record[62] = 0x08 // HS (bit 3 set)

		// Minimum Yellow Clearance Enable
		record[63] = 0x00 // LH
		record[64] = 0x00 // MS
		record[65] = 0x10 // HS (bit 4 set)

		// Reserved bytes (3 bytes)
		record[66] = 0x00
		record[67] = 0x00
		record[68] = 0x00

		// Yellow Disable (inverted)
		record[69] = 0xFF // LH (all bits set)
		record[70] = 0xFF // MS (all bits set)
		record[71] = 0xFE // HS (all bits except bit 0)

		// Reserved bytes (5 bytes)
		record[72] = 0x00
		record[73] = 0x00
		record[74] = 0x00
		record[75] = 0x00
		record[76] = 0x00

		// DateTime (6 bytes in BCD format)
		now := time.Now()
		record[77] = toBCD(now.Second())     // Second (BCD, 00-59)
		record[78] = toBCD(now.Minute())     // Minute (BCD, 00-59)
		record[79] = toBCD(now.Hour())       // Hour (BCD, 00-23)
		record[80] = toBCD(now.Day())        // Day (BCD, 01-31)
		record[81] = toBCD(int(now.Month())) // Month (BCD, 01-12)
		record[82] = toBCD(now.Year() % 100) // Year (BCD, 00-99)

		// Reserved bytes (2 bytes)
		record[83] = 0x00
		record[84] = 0x00

		msg = append(msg, record...)
	}

	// Calculate checksum
	var sum byte
	for _, b := range msg {
		sum += b
	}
	msg = append(msg, ^sum) // One's complement of sum

	return msg
}

func createInvalidConfigurationChangeLogChecksumMsg() []byte {
	msg := buildValidConfigurationChangeLogMsg(1)
	msg[len(msg)-1] = ^msg[len(msg)-1] // Flipping valid checksum byte always produces invalid checksum
	return msg
}

func createInvalidConfigurationChangeLogLengthMsg() []byte {
	msg := buildValidConfigurationChangeLogMsg(1)
	msg = msg[:len(msg)-2] // Remove 2 bytes to make it invalid length
	// Recalculate checksum for new length
	var sum byte
	for _, b := range msg[:len(msg)-1] {
		sum += b
	}
	msg[len(msg)-1] = ^sum
	return msg
}

func createInvalidConfigurationChangeLogBCDMsg() []byte {
	// Create header
	msg := make([]byte, HeaderLength)
	msg[0] = 0x01 // Message type
	msg[1] = 0x02 // Device address
	msg[2] = 0x03 // Command code
	msg[3] = 0x00 // Response status
	msg[4] = 0x00 // Reserved byte
	msg[5] = 0x00 // Reserved byte
	msg[6] = 0x00 // Reserved byte

	// Add number of records
	msg = append(msg, 0x01) // Number of records

	// Create invalid record with invalid BCD values
	record := make([]byte, CF2018LogLength)

	// Conflict map (54 bytes)
	for j := 0; j < 54; j++ {
		record[j] = 0x00 // Initialize conflict map to all zeros
	}

	// Enable flags (18 bytes)
	// Red Fail Enable
	record[51] = 0x00 // LH
	record[52] = 0x00 // MS
	record[53] = 0x01 // HS (bit 0 set)

	// Green Yellow Dual Enable
	record[54] = 0x00 // LH
	record[55] = 0x00 // MS
	record[56] = 0x02 // HS (bit 1 set)

	// Yellow Red Dual Enable
	record[57] = 0x00 // LH
	record[58] = 0x00 // MS
	record[59] = 0x04 // HS (bit 2 set)

	// Green Red Dual Enable
	record[60] = 0x00 // LH
	record[61] = 0x00 // MS
	record[62] = 0x08 // HS (bit 3 set)

	// Minimum Yellow Clearance Enable
	record[63] = 0x00 // LH
	record[64] = 0x00 // MS
	record[65] = 0x10 // HS (bit 4 set)

	// Reserved bytes (3 bytes)
	record[66] = 0x00
	record[67] = 0x00
	record[68] = 0x00

	// Yellow Disable (inverted)
	record[69] = 0xFF // LH (all bits set)
	record[70] = 0xFF // MS (all bits set)
	record[71] = 0xFE // HS (all bits except bit 0)

	// Reserved bytes (5 bytes)
	record[72] = 0x00
	record[73] = 0x00
	record[74] = 0x00
	record[75] = 0x00
	record[76] = 0x00

	// DateTime with invalid BCD values
	record[77] = 0xFF // Invalid seconds (BCD)
	record[78] = 0xFF // Invalid minutes (BCD)
	record[79] = 0xFF // Invalid hours (BCD)
	record[80] = 0xFF // Invalid day (BCD)
	record[81] = 0xFF // Invalid month (BCD)
	record[82] = 0xFF // Invalid year (BCD)

	// Reserved bytes (2 bytes)
	record[83] = 0x00
	record[84] = 0x00

	msg = append(msg, record...)

	// Calculate checksum
	var sum byte
	for _, b := range msg {
		sum += b
	}
	msg = append(msg, ^sum) // One's complement of sum

	return msg
}

// Helper to convert int to BCD
func toBCD(val int) byte {
	return byte(((val / 10) << 4) | (val % 10))
}

func TestParseChannelPermissives(t *testing.T) {
	tests := []struct {
		name           string
		conflictMap    [18]int
		maxChannels    int
		expectedRecord *helper.ConfigurationChangeLogRecord
	}{
		{
			name: "no permissives - all conflicts",
			conflictMap: func() [18]int {
				var cm [18]int
				// Set all bits to 1 to indicate conflicts
				for i := range cm {
					cm[i] = 0xFFFFFF
				}
				return cm
			}(),
			maxChannels: 4,
			expectedRecord: &helper.ConfigurationChangeLogRecord{
				Ch01Permissives: nil,
				Ch02Permissives: nil,
				Ch03Permissives: nil,
			},
		},
		{
			name: "all permissives - no conflicts",
			conflictMap: func() [18]int {
				var cm [18]int
				// All bits set to 0 to indicate no conflicts
				return cm
			}(),
			maxChannels: 4,
			expectedRecord: &helper.ConfigurationChangeLogRecord{
				Ch01Permissives: []string{"2", "3", "4"},
				Ch02Permissives: []string{"3", "4"},
				Ch03Permissives: []string{"4"},
			},
		},
		{
			name: "mixed permissives",
			conflictMap: func() [18]int {
				var cm [18]int
				// Channel 1 has conflict with 2 but not with 3,4
				cm[0] = 0x000002
				// Channel 2 has conflict with 3 but not with 4
				cm[1] = 0x000004
				// Channel 3 has no conflicts
				return cm
			}(),
			maxChannels: 4,
			expectedRecord: &helper.ConfigurationChangeLogRecord{
				Ch01Permissives: []string{"2", "4"},
				Ch02Permissives: []string{"3"},
				Ch03Permissives: []string{"4"},
			},
		},
		{
			name: "max channels edge case",
			conflictMap: func() [18]int {
				var cm [18]int
				return cm
			}(),
			maxChannels: 16,
			expectedRecord: &helper.ConfigurationChangeLogRecord{
				Ch01Permissives: []string{"2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16"},
				Ch02Permissives: []string{"3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16"},
				Ch03Permissives: []string{"4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16"},
				Ch04Permissives: []string{"5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16"},
				Ch05Permissives: []string{"6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16"},
				Ch06Permissives: []string{"7", "8", "9", "10", "11", "12", "13", "14", "15", "16"},
				Ch07Permissives: []string{"8", "9", "10", "11", "12", "13", "14", "15", "16"},
				Ch08Permissives: []string{"9", "10", "11", "12", "13", "14", "15", "16"},
				Ch09Permissives: []string{"10", "11", "12", "13", "14", "15", "16"},
				Ch10Permissives: []string{"11", "12", "13", "14", "15", "16"},
				Ch11Permissives: []string{"12", "13", "14", "15", "16"},
				Ch12Permissives: []string{"13", "14", "15", "16"},
				Ch13Permissives: []string{"14", "15", "16"},
				Ch14Permissives: []string{"15", "16"},
				Ch15Permissives: []string{"16"},
			},
		},
		{
			name: "single channel",
			conflictMap: func() [18]int {
				var cm [18]int
				return cm
			}(),
			maxChannels:    1,
			expectedRecord: &helper.ConfigurationChangeLogRecord{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			record := &helper.ConfigurationChangeLogRecord{}
			parseChannelPermissives(tt.conflictMap, tt.maxChannels, record)
			assert.Equal(t, tt.expectedRecord, record)
		})
	}
}

package subscriptions

import (
	dlqBqBatch "synapse-its.com/etl/processors/handlers/dlq/batch"
	dlqPubsub "synapse-its.com/etl/processors/handlers/dlq/messages"
	"synapse-its.com/etl/processors/handlers/gateway/faultLogs"
	"synapse-its.com/etl/processors/handlers/gateway/faultNotification"
	"synapse-its.com/etl/processors/handlers/gateway/gatewayLog"
	"synapse-its.com/etl/processors/handlers/gateway/macAddress"
	"synapse-its.com/etl/processors/handlers/gateway/monitorName"
	"synapse-its.com/etl/processors/handlers/gateway/perfStats"
	"synapse-its.com/etl/processors/handlers/gateway/rmsData"
	"synapse-its.com/etl/processors/handlers/gateway/rmsEngine"
	"synapse-its.com/etl/processors/handlers/raw"
)

func DefaultSubscriptions() []Subscription {
	return []Subscription{
		// Message processing handlers
		{Handler: rmsData.Handler, Name: "etl-processing-gateway-rmsData"},
		{Handler: gatewayLog.Handler, Name: "etl-processing-gateway-gatewayLog"},
		{Handler: faultNotification.Handler, Name: "etl-processing-gateway-faultNotification"},
		{Handler: rmsEngine.Handler, Name: "etl-processing-gateway-rmsEngine"},
		{Handler: monitorName.Handler, Name: "etl-processing-gateway-monitorName"},
		{Handler: macAddress.Handler, Name: "etl-processing-gateway-macAddress"},
		{Handler: faultLogs.Handler, Name: "etl-processing-gateway-faultLogs"},
		{Handler: perfStats.Handler, Name: "etl-processing-gateway-perfStats"},
		// Raw handlers
		{Handler: raw.Handler, Name: "etl-raw-gateway-rmsData"},
		{Handler: raw.Handler, Name: "etl-raw-gateway-gatewayLog"},
		{Handler: raw.Handler, Name: "etl-raw-gateway-faultNotification"},
		{Handler: raw.Handler, Name: "etl-raw-gateway-rmsEngine"},
		{Handler: raw.Handler, Name: "etl-raw-gateway-monitorName"},
		{Handler: raw.Handler, Name: "etl-raw-gateway-macAddress"},
		{Handler: raw.Handler, Name: "etl-raw-gateway-faultLogs"},
		{Handler: raw.Handler, Name: "etl-raw-gateway-perfStats"},
		// DLQ handlers
		{Handler: dlqPubsub.Handler, Name: "etl-processing-dlq-messages"},
		{Handler: dlqBqBatch.Handler, Name: "etl-processing-dlq-bqbatch"},
	}
}

import { Component } from '@angular/core';
import { FooService } from '../foo.service';

// TODO: Wire up backend once APIs are built
interface OEMData {
  orgIden: string;
  apiKey: string;
  name: string;
}
@Component({
  selector: 'app-organizations',
  templateUrl: './organizations.component.html',
  standalone: false,
  styleUrl: './organizations.component.css'
})
export class OrganizationsComponent {
  isModalVisible = false;
  isEditMode = false;
  selectedData = null;
  listOfData: OEMData[] = [];
  filteredList: OEMData[] = [];
  searchTerm: string = '';
  currentOEM: OEMData | null = null;
  editIndex: number | null = null;

  constructor(private foo: FooService) { }
  ngOnInit() {
    this.getApi();
  }
  getApi() {
    this.foo.getNames().subscribe((data) => {
      this.listOfData = data;
      this.filteredList = [...this.listOfData];
    });
  }
  searchTable() {
    if (!this.searchTerm) {
      this.filteredList = [...this.listOfData];
      return;
    }
    this.filteredList = this.listOfData.filter(item =>
      item.name.toLowerCase().includes(this.searchTerm.toLowerCase())
    );
  }
  onClickSoftware() {
    console.log('click software');
  }
  onClickUsres() {
    console.log('click users');
  }
  openAddPopup() {
    this.isModalVisible = true;
    this.selectedData = null;
    this.isEditMode = false;
  }
  openEditPopup(index: number) {
    this.isModalVisible = true;
    this.isEditMode = true;
    this.editIndex = index;
    this.currentOEM = { ...this.listOfData[index] };
  }
  closeModal(): void {
    this.isModalVisible = false;
    this.selectedData = null;
    this.isEditMode = false;
  }
  handleModalSave(data: any): void {
    if (this.isEditMode && this.editIndex !== null && this.currentOEM) {
      const updatedOEM = { ...this.currentOEM, ...data };
      this.listOfData[this.editIndex] = updatedOEM;
    } else {
      this.listOfData = [...this.listOfData, data];
    }
    this.filteredList = [...this.listOfData];
    this.isModalVisible = false;
    this.editIndex = null;
    this.currentOEM = null;
  }
}

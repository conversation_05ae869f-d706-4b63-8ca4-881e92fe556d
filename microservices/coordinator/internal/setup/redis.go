package setup

import (
	"context"
	"fmt"
	"os"
	"time"

	// https://pkg.go.dev/github.com/go-redis/redis/v9
  "github.com/redis/go-redis/v9"
	
	"synapse-its.com/shared/logger"
)

func Redis(ctx context.Context) (*redis.Client, error) {
	// Read environment variables for Redis with defaults.
	redisHost := os.Getenv("REDIS_HOST")
	if redisHost == "" {
		redisHost = "redis"
	}
	redisPort := os.Getenv("REDIS_PORT")
	if redisPort == "" {
		redisPort = "6379"
	}
	redisAddr := fmt.Sprintf("%s:%s", redisHost, redisPort)
	logger.Infof("Connecting to Redis at %s", redisAddr)

	// Create the Redis client.
	redisClient := redis.NewClient(&redis.Options{
		Addr: redisAddr,
	})

	// Exponential backoff retry loop to wait for Redis to be ready.
	var pong string
	var err error
	backoff := time.Second
	maxRetries := 5

	for i := 0; i < maxRetries; i++ {
		pong, err = redisClient.Ping(ctx).Result()
		if err == nil {
			break
		}
		logger.Infof("Attempt %d/%d: Redis not ready, retrying in %s: %v", i+1, maxRetries, backoff, err)
		time.Sleep(backoff)
		backoff *= 2
	}

	if err != nil {
		return nil, fmt.Errorf("Could not connect to Redis after %d attempts: %v", maxRetries, err)
	}
	logger.Infof("Redis connected: %s", pong)

	return redisClient, nil
}

package authenticate

import (
	"net/http"
	"strconv"
	"strings"

	response "synapse-its.com/shared/api/response"
)

// Interfaces for testing
type UserAccessor interface {
	UserAccess(userName, password string, w http.ResponseWriter, r *http.Request)
	WebAccess(userName, password string, w http.ResponseWriter, r *http.Request)
}

type TokenAccessor interface {
	TokenAccess(token string, w http.ResponseWriter, r *http.Request)
}

// For testing purposes
var (
	newUserHandlerFunc  = func() UserAccessor { return NewUserHandler() }
	newTokenHandlerFunc = func() TokenAccessor { return NewTokenHandler() }
)

func Handler(w http.ResponseWriter, r *http.Request) {
	switch len(r.URL.Query()) {
	case 0:
		// Process as a normal auth request
		userName, password, err := extractUserNameAndPassword(r.Body)
		if err != nil {
			response.CreateUnauthorizedResponse(w)
			return
		}
		userHandler := newUserHandlerFunc()
		userHandler.UserAccess(userName, password, w, r)

	case 1:
		var existsWebAccessParam, existsTokenAccessParam bool = false, false
		var queryParamWebAccess, queryParamTokenAccess string
		queryParams := r.URL.Query()
		if webaccess, ok := queryParams["webaccess"]; ok && len(webaccess) > 0 {
			queryParamWebAccess = webaccess[0]
			existsWebAccessParam = true
		}
		if token, ok := queryParams["token"]; ok && len(token) > 0 {
			queryParamTokenAccess = token[0]
			existsTokenAccessParam = true
		}

		if existsWebAccessParam && !existsTokenAccessParam {
			// Web access requested
			paramWebAccess, err := strconv.ParseBool(queryParamWebAccess)
			if err != nil || !paramWebAccess {
				// Unable to parse the parameter to true or false or the parameter pass in stated false
				response.CreateUnauthorizedResponse(w)
				return
			}
			userName, password, err := extractUserNameAndPassword(r.Body)
			if err != nil {
				response.CreateUnauthorizedResponse(w)
				return
			}
			userHandler := newUserHandlerFunc()
			userHandler.WebAccess(userName, password, w, r)
			return
		} else if existsTokenAccessParam && !existsWebAccessParam {
			// Token access
			queryParamTokenAccess = strings.TrimSpace(queryParamTokenAccess) // remove any extraneous spaces
			if len(queryParamTokenAccess) != (2 * NonceByteLength) {
				// The nonce is 128 bytes in length, we are expecting it to be passed to us in hex -- so it will be 256 bytes
				response.CreateUnauthorizedResponse(w)
				return
			}
			tokenHandler := newTokenHandlerFunc()
			tokenHandler.TokenAccess(queryParamTokenAccess, w, r)
			return
		} else {
			// If a single query parameter is passed in, it must be either for web or token access
			response.CreateUnauthorizedResponse(w)
			return
		}
	default:
		// Expect either no parameters or a single specific parameter -- if not met, unauthorized
		response.CreateUnauthorizedResponse(w)
		return
	}
}

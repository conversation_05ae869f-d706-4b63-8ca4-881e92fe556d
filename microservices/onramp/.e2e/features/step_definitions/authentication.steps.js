const { Given, When, Then } = require('@cucumber/cucumber');
const { By, until } = require('selenium-webdriver');

When('I click the "Log in" button', async function () {
  try {
    // wait up to 5s for the nav itself to exist
    const nav = await this.driver.wait(
      until.elementLocated(By.css('nav')),
      5000
    );
    // then wait for the button under it
    const btn = await this.driver.wait(
      until.elementLocated(By.css('nav button')),
      5000
    );
    await btn.click();
  }
  catch (err) {
    console.error(err);
    console.error('page source at error:\n', await this.driver.getPageSource());
    throw err;
  }
});

When('I complete the Keycloak login form with username {string} and password {string}',
  async function (username, password) {
    try {
      // Wait for Keycloak login form
      await this.driver.wait(
        until.elementLocated(By.id('kc-form-login')),
        10000
      );
      // Fill and submit
      await this.driver.findElement(By.id('username')).sendKeys(username);
      await this.driver.findElement(By.id('password')).sendKeys(password);
      await this.driver.findElement(By.name('login')).click();
    }
    catch (err) {
      console.error(err);
      console.error('page source at error:\n', await this.driver.getPageSource());
      throw err;
    }
  }
);

Then('I should be redirected back to the application', async function () {
  try {
    await this.driver.wait(
      until.urlContains(process.env.BASE_URL),
      15000,
      `Expected URL to start with ${process.env.BASE_URL}`
    );
    await this.waitForAngularStable();
  }
  catch (err) {
    console.error(err);
    console.error('page source at error:\n', await this.driver.getPageSource());
    throw err;
  }
});

Then('I should see the message {string}', async function (expectedText) {
  try {
    const el = await this.driver.wait(
      until.elementLocated(By.xpath(`//*[contains(normalize-space(.), "${expectedText}")]`)),
      5000
    );
    const actual = await el.getText();
    if (!actual.includes(expectedText)) {
      throw new Error(`Expected text "${expectedText}", but found "${actual}"`);
    }
  }
  catch (err) {
    console.error(err);
    console.error('page source at error:\n', await this.driver.getPageSource());
    throw err;
  }
});

When('I click the "Log out" button', async function () {
  try {
    const btn = await this.driver.findElement(By.css('nav button'));
    await btn.click();
  }
  catch (err) {
    console.error(err);
    console.error('page source at error:\n', await this.driver.getPageSource());
    throw err;
  }
});

Then('I should see the "Log in" button', async function () {
  try {
    const btn = await this.driver.wait(
      until.elementLocated(By.css('nav button')),
      5000
    );
    const text = await btn.getText();
    if (text !== 'Log in') {
      throw new Error(`Expected "Log in" button after logout, but saw "${text}"`);
    }
  }
  catch (err) {
    console.error(err);
    console.error('page source at error:\n', await this.driver.getPageSource());
    throw err;
  }
});

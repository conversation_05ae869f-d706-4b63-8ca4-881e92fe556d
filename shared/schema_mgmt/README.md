# schema_mgmt

---

## Overview

The `schema_mgmt` package manages database schema versioning and migrations for PostgreSQL and BigQuery, including automatic execution of `data-core` migrations in the Coordinator microservice.

---

## Package Layout

```
shared/
├── schema_mgmt/
│   ├── schema_mgmt.go
│   ├── schemas_test.go
│   └── sql.go
```

---

## Features

- **Semantic Versioning**: scans versioned directories under `schemas/<name>/`  
- **Migration Table**: auto-creates `schema_migrations` and records applied versions  
- **DDL/DML/Sequence Updates**: applies in correct order with MD5 validation  
- **Multi-DB Support**: `PostgresMigrationExecutor`, `BigQueryMigrationExecutor`  
- **Coordinator Integration**: auto-runs all `data-core` migrations on startup  

---

## Usage

```go
executor := &schema_mgmt.PostgresMigrationExecutor{Client: pgClient}
if err := schema_mgmt.ApplyMigrations(executor, "data-core-pg", ""); err != nil {
    log.Fatalf("data-core migrations failed: %v", err)
}
```

---

## API Reference

### Functions

```go
func GetMostRecentSchema(schemaName string) (string, error)
func ApplyMigrations(executor SchemaMigrationExecutor, schemaName, targetVersion string) error
```

### Types

```go
type SchemaMigrationExecutor interface {
    EnsureSchemaMigrationsTable() error
    QueryGeneric(query string, args ...interface{}) ([]map[string]interface{}, error)
    Exec(query string, args ...interface{}) (sql.Result, error)
    ExecMultiple(query string) error
    QueryRow(query string, args ...interface{}) (map[string]interface{}, error)
    Config() connect.DatabaseConfig
}

type PostgresMigrationExecutor
type BigQueryMigrationExecutor
type Migration
```

---

## Internals

- **Directory Scanning**: `getSchemaPaths`, `listVersionDirectories`  
- **Sequence Parsing**: `parseSequenceFiles` with MD5 checks  
- **SQL Execution**: `executeSQLFile`, `ExecMultiple`  
- **Error Types**: wrapped errors like `ErrApplyMigration`  

---

## Example

```bash
go run cmd/coordinator/main.go
```

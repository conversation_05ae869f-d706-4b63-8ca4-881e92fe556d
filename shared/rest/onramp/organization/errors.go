package organization

import "errors"

var (
	ErrUnexpectedFields     = errors.New("request contains unexpected fields")
	ErrInvalidRequestBody   = errors.New("invalid request body")
	ErrInvalidDescription   = errors.New("description cannot be empty")
	ErrDatabaseOperation    = errors.New("database operation failed")
	ErrInvalidIdentifier    = errors.New("invalid organization identifier")
	ErrOrganizationNotFound = errors.New("organization not found")
)

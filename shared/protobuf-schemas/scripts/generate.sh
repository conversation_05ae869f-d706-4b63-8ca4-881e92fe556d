#!/usr/bin/env bash
set -euo pipefail

# where .proto files live
PROTO_DIR=./models
# where .go files land
OUT_DIR=./protomessages

rm -rf "$OUT_DIR"
mkdir -p "$OUT_DIR"

# find and compile every .proto under $PROTO_DIR
find "$PROTO_DIR" -name '*.proto' | while read -r proto; do
  protoc \
    --proto_path="$PROTO_DIR" \
    --go_out="$OUT_DIR" --go_opt=paths=source_relative \
    --go-grpc_out="$OUT_DIR" --go-grpc_opt=paths=source_relative \
    "$proto"
done

# Run go mod tidy and download dependencies
if [ -f "go.mod" ]; then
  echo "Running go mod tidy..."
  go mod tidy
  echo "Running go mod download..."
  go mod download
fi

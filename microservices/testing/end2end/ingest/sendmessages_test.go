// Package ingest
//
// CSV file expects data in a specific format. This can be done with the
// following BigQuery query
//
// SELECT
//   (SELECT value FROM UNNEST(attributes) WHERE key = "http_headers") AS headers,
//   DATA AS body
// FROM `dev_dataset.DEV__EdiRawMessages`

package ingest

import (
	"bytes"
	"encoding/base64"
	"encoding/csv"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"testing"
)

func TestSendMessagesFromCSV(t *testing.T) {
	// Find all CSV files in testdata/
	files, err := filepath.Glob("testdata/*.csv")
	if err != nil {
		t.Fatalf("glob csv files: %v", err)
	}
	if len(files) == 0 {
		t.Fatal("no CSV files found in testdata/")
	}

	for _, file := range files {
		file := file // capture range variable
		t.Run(fmt.Sprintf("file_%s", filepath.Base(file)), func(t *testing.T) {
			t.<PERSON>l()

			f, err := os.Open(file)
			if err != nil {
				t.Fatalf("opening CSV %s: %v", file, err)
			}
			defer f.Close()

			r := csv.NewReader(f)
			r.FieldsPerRecord = 2

			// Skip header row
			if _, err := r.Read(); err != nil {
				t.Fatalf("reading header row: %v", err)
			}

			lineNum := 0
			for {
				rec, err := r.Read()
				if err == io.EOF {
					break
				}
				lineNum++
				if err != nil {
					t.Errorf("line %d: read error: %v", lineNum, err)
					continue
				}

				rawHdr, rawBody := rec[0], rec[1]
				t.Run(fmt.Sprintf("line_%d", lineNum), func(t *testing.T) {
					t.Parallel()

					// Parse headers JSON
					var hdrs map[string]string
					if err := json.Unmarshal([]byte(rawHdr), &hdrs); err != nil {
						t.Fatalf("invalid header JSON: %v", err)
					}
					hdrs["x-api-key"] = "qiLkND6fYR2zVIQbgifvR2VbyOStT6qt6OgzPZTd"

					// Base64-decode body
					payload, err := base64.StdEncoding.DecodeString(rawBody)
					if err != nil {
						t.Fatalf("invalid base64 body: %v", err)
					}

					// Build request
					endpoint := "http://broker:8080/api/v3/gateway/ingest"
					req, err := http.NewRequest("POST", endpoint, bytes.NewReader(payload))
					if err != nil {
						t.Fatalf("creating request: %v", err)
					}

					// Set headers
					for k, v := range hdrs {
						req.Header.Set(k, v)
					}

					// Send request
					resp, err := http.DefaultClient.Do(req)
					if err != nil {
						t.Fatalf("sending request: %v", err)
					}
					defer resp.Body.Close()

					// Validate response
					if resp.StatusCode != http.StatusOK {
						body, _ := io.ReadAll(resp.Body)
						t.Errorf("expected 200 OK, got %d: %s", resp.StatusCode, string(body))
					}
				})
			}
		})
	}
}

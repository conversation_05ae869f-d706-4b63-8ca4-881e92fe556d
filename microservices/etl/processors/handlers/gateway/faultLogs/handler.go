package faultLogs

import (
	"context"
	"fmt"

	gatewayv1 "bitbucket.org/synapse-its/protobuf-schemas/protomessages/gateway/v1"
	"cloud.google.com/go/pubsub"
	"github.com/google/uuid"
	"google.golang.org/protobuf/proto"
	"synapse-its.com/etl/processors/handlers/etlShared"
	"synapse-its.com/shared/bqbatch"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/devices"
	edihelper "synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/logger"
	"synapse-its.com/shared/pubsubdata"
	"synapse-its.com/shared/schemas"
)

// --- Dependency abstractions for injection and testing ---
type (
	ConnectorFunc                             func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error)
	ParseAttributesFunc                       func(attrs map[string]string) (pubsubdata.CommonAttributes, pubsubdata.HeaderDetails, error)
	DLQSender                                 func(ctx context.Context, client connect.PsClient, msg *pubsub.Message, reason string) error
	UnmarshalDeviceLogsFunc                   func(raw []byte) (*gatewayv1.DeviceLogs, error)
	BatchGetter                               func(ctx context.Context) (bqbatch.Batcher, error)
	MarshalDeviceLogsFunc                     func(msg proto.Message) ([]byte, error)
	InitLogMonitorResetBQConverterFunc        func(httpHeader *pubsubdata.HeaderDetails, commonAttrs *pubsubdata.CommonAttributes, pubsubMessage *pubsub.Message, translater edihelper.LogMonitorResetTranslater, logUUID string, deviceID string) edihelper.BQConverter
	InitLogPreviousFailBQConverterFunc        func(httpHeader *pubsubdata.HeaderDetails, commonAttrs *pubsubdata.CommonAttributes, pubsubMessage *pubsub.Message, translater edihelper.LogPreviousFailTranslater, logUUID string, deviceID string) edihelper.BQConverter
	InitLogACLineEventBQConverterFunc         func(httpHeader *pubsubdata.HeaderDetails, commonAttrs *pubsubdata.CommonAttributes, pubsubMessage *pubsub.Message, translater edihelper.LogACLineEventTranslater, logUUID string, deviceID string) edihelper.BQConverter
	InitLogFaultSignalSequenceBQConverterFunc func(httpHeader *pubsubdata.HeaderDetails, commonAttrs *pubsubdata.CommonAttributes, pubsubMessage *pubsub.Message, translater edihelper.FaultSignalSequenceTranslater, existingSeq *schemas.LogFaultSignalSequence, logUUID string, deviceID string) edihelper.BQConverter
	InitLogConfigurationBQConverterFunc       func(httpHeader *pubsubdata.HeaderDetails, commonAttrs *pubsubdata.CommonAttributes, pubsubMessage *pubsub.Message, translater edihelper.ConfigurationChangeLogTranslater, logUUID string, deviceID string) edihelper.BQConverter
)

// HandlerDeps bundles all external dependencies.
type HandlerDeps struct {
	Connector                                    ConnectorFunc
	ParseAttributes                              ParseAttributesFunc
	SendToDLQ                                    DLQSender
	UnmarshalLogs                                UnmarshalDeviceLogsFunc
	GetBatch                                     BatchGetter
	LogMonitorResetBQConverterConstructor        InitLogMonitorResetBQConverterFunc
	LogPreviousFailBQConverterConstructor        InitLogPreviousFailBQConverterFunc
	LogACLineEventBQConverterConstructor         InitLogACLineEventBQConverterFunc
	LogFaultSignalSequenceBQConverterConstructor InitLogFaultSignalSequenceBQConverterFunc
	LogConfigurationBQConverterConstructor       InitLogConfigurationBQConverterFunc
}

// HandlerWithDeps constructs a Pub/Sub processing function with injected dependencies.
func HandlerWithDeps(deps HandlerDeps) func(ctx context.Context, subscriptionName string) {
	return func(ctx context.Context, subscriptionName string) {
		batch, batchErr := deps.GetBatch(ctx)
		if batchErr != nil {
			logger.Errorf("Error getting batch: %v", batchErr)
			return
		}
		connections, err := deps.Connector(ctx)
		if err != nil {
			logger.Errorf("Error getting connections%v", err)
			return
		}
		sub := connections.Pubsub.Subscription(subscriptionName)
		err = sub.Receive(ctx, func(ctx context.Context, msg *pubsub.Message) {
			logger.Debugf("Received message on %s MessageID: %s. Message Data:%s", subscriptionName, string(msg.ID), string(msg.Data))

			// Parse Attributes
			commonAttrs, httpHeader, errPa := deps.ParseAttributes(msg.Attributes)
			if errPa != nil {
				logger.Errorf("Unable to parse attributes: %v", msg.Attributes)
				err = deps.SendToDLQ(ctx, connections.Pubsub, msg, fmt.Sprintf("Unable to parse attributes: %v", errPa))
				if err != nil {
					logger.Errorf("Error sending message to the DLQ topic %v", err)
					msg.Nack()
					return
				}
				msg.Ack()
				return
			}

			// Unmarshal device logs
			deviceLogs, errUm := deps.UnmarshalLogs(msg.Data)
			if errUm != nil {
				logger.Errorf("Error Unmarshaling the Device Logs: %v", errUm)
				err = deps.SendToDLQ(ctx, connections.Pubsub, msg, fmt.Sprintf("Error Unmarshaling the Device Logs: %v", errUm))
				if err != nil {
					logger.Errorf("Error sending message to the DLQ topic %v", err)
					msg.Nack()
					return
				}
				msg.Ack()
				return
			}

			// Generate a log UUID that can be used to link all of the logs together
			logUUID := uuid.New().String()

			// Create a new FaultLogs struct
			faultLogs := &schemas.FaultLogs{
				OrganizationIdentifier: commonAttrs.OrganizationIdentifier,
				SoftwareGatewayID:      httpHeader.GatewayDeviceID,
				TZ:                     httpHeader.GatewayTimezone,
				Topic:                  commonAttrs.Topic,
				PubsubTimestamp:        msg.PublishTime.UTC(),
				PubsubID:               msg.ID,
				DeviceID:               deviceLogs.GetDeviceId(),
				RawLogMessages: schemas.RawLogMessages{
					LogMonitorReset:        make([][]byte, 0),
					LogPreviousFail:        make([][]byte, 0),
					LogACLineEvent:         make([][]byte, 0),
					LogFaultSignalSequence: make([][]byte, 0),
					LogConfiguration:       make([][]byte, 0),
				},
				LogUUID: logUUID,
			}

			// Process each log entry
			for _, logEntry := range deviceLogs.GetLogs() {
				// Add the message to the appropriate RawLogMessages field
				switch logEntry.GetLogType() {
				case "MonitorReset":
					faultLogs.RawLogMessages.LogMonitorReset = append(faultLogs.RawLogMessages.LogMonitorReset, logEntry.GetMessage()...)
				case "PreviousFail":
					faultLogs.RawLogMessages.LogPreviousFail = append(faultLogs.RawLogMessages.LogPreviousFail, logEntry.GetMessage()...)
				case "ACLine":
					faultLogs.RawLogMessages.LogACLineEvent = append(faultLogs.RawLogMessages.LogACLineEvent, logEntry.GetMessage()...)
				case "FaultSignalSequence":
					faultLogs.RawLogMessages.LogFaultSignalSequence = append(faultLogs.RawLogMessages.LogFaultSignalSequence, logEntry.GetMessage()...)
				case "Configuration":
					faultLogs.RawLogMessages.LogConfiguration = append(faultLogs.RawLogMessages.LogConfiguration, logEntry.GetMessage()...)
				default:
					logger.Warnf("Unknown log entry message type: %s", logEntry.GetLogType())
				}
			}

			// Add faultLogs to BigQuery batch
			if err = batch.Add(faultLogs); err != nil {
				logger.Errorf("Error adding to batch: %v", err)
				err = deps.SendToDLQ(ctx, connections.Pubsub, msg, fmt.Sprintf("Error adding to batch: %v", err))
				if err != nil {
					logger.Errorf("Error sending message to the DLQ topic %v", err)
					msg.Nack()
					return
				}
			}

			// Add individual log items to the BigQuery batch
			monitorResetBQItems := createIndividualLogBQItems(faultLogs.RawLogMessages.LogMonitorReset, deps.LogMonitorResetBQConverterConstructor(&httpHeader, &commonAttrs, msg, devices.ProcessLogMonitorReset, logUUID, deviceLogs.GetDeviceId()))
			previousFailBQItems := createIndividualLogBQItems(faultLogs.RawLogMessages.LogPreviousFail, deps.LogPreviousFailBQConverterConstructor(&httpHeader, &commonAttrs, msg, devices.ProcessLogPreviousFail, logUUID, deviceLogs.GetDeviceId()))
			acLineEventBQItems := createIndividualLogBQItems(faultLogs.RawLogMessages.LogACLineEvent, deps.LogACLineEventBQConverterConstructor(&httpHeader, &commonAttrs, msg, devices.ProcessLogACLineEvent, logUUID, deviceLogs.GetDeviceId()))
			configurationBQItems := createIndividualLogBQItems(faultLogs.RawLogMessages.LogConfiguration, deps.LogConfigurationBQConverterConstructor(&httpHeader, &commonAttrs, msg, devices.ProcessLogConfiguration, logUUID, deviceLogs.GetDeviceId()))
			individualLogBQItems := append(monitorResetBQItems, previousFailBQItems...)
			individualLogBQItems = append(individualLogBQItems, acLineEventBQItems...)
			individualLogBQItems = append(individualLogBQItems, configurationBQItems...)

			// For LogFaultSignalSequence there are going to be multiple entries for this type
			// Because of this we populate existingSeq in the NewFaultSignalSequenceBQConverter()
			// So that we can append the records for each of the entries for LogFaultSignalSequence
			// This is why we are ignoring the return value for createIndividualLogBQItems
			existingSeq := schemas.LogFaultSignalSequence{}
			_ = createIndividualLogBQItems(faultLogs.RawLogMessages.LogFaultSignalSequence, deps.LogFaultSignalSequenceBQConverterConstructor(&httpHeader, &commonAttrs, msg, devices.ProcessLogFaultSignalSequence, &existingSeq, logUUID, deviceLogs.GetDeviceId()))
			if existingSeq.OrganizationIdentifier != "" {
				individualLogBQItems = append(individualLogBQItems, existingSeq)
			}

			for _, item := range individualLogBQItems {
				if err = batch.Add(item); err != nil {
					// TODO: Add process to recreate the message with only the failed items and send to DLQ
					logger.Errorf("Error adding to batch for individual log: %v", err)
				}
			}

			msg.Ack()
		})
		if err != nil {
			logger.Errorf("Failed to receive messages from subscription"+subscriptionName+": %v", err)
		}
	}
}

// Handler is the production-ready Pub/Sub processor using real dependencies.
var Handler = HandlerWithDeps(HandlerDeps{
	Connector:                             connect.GetConnections,
	ParseAttributes:                       pubsubdata.ParseAttributes,
	SendToDLQ:                             etlShared.SendToDLQ,
	UnmarshalLogs:                         etlShared.UnmarshalDeviceLogs,
	GetBatch:                              bqbatch.GetBatch,
	LogMonitorResetBQConverterConstructor: edihelper.NewLogMonitorResetBQConverter,
	LogPreviousFailBQConverterConstructor: edihelper.NewLogPreviousFailBQConverter,
	LogACLineEventBQConverterConstructor:  edihelper.NewLogACLineEventBQConverter,
	LogFaultSignalSequenceBQConverterConstructor: edihelper.NewFaultSignalSequenceBQConverter,
	LogConfigurationBQConverterConstructor:       edihelper.NewConfigurationChangeLogBQConverter,
})

func createIndividualLogBQItems(rawLogMessages [][]byte, converter edihelper.BQConverter) []any {
	returnedItems := make([]any, 0)
	for _, rawLogMessage := range rawLogMessages {
		// 0 byte message means no records
		if len(rawLogMessage) == 0 {
			continue
		}
		convertedItem, err := converter(rawLogMessage)
		if err != nil {
			logger.Errorf("Error converting log message: %v", err)
			continue
		}
		returnedItems = append(returnedItems, convertedItem)
	}
	return returnedItems
}

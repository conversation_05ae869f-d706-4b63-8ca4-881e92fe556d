package schema_mgmt

import (
	"os"
	"path/filepath"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
)

// This test file is for testing the database schemas (/schemas) in terms of
// file/directory structure and existence, and contents to conform to our
// business rules.

// helper function used to get the list of schemas from the /schemas directory.
func listSubdirs(dir string) ([]string, error) {
	entries, err := os.ReadDir(dir)
	if err != nil {
		return nil, err
	}
	var subs []string
	for _, e := range entries {
		if e.IsDir() {
			subs = append(subs, e.Name())
		}
	}
	return subs, nil
}

// Actually test the file structure and contents of the schemas directory.
// The SQL files are not actually executed here, but that is done in the
// integration tests.
func Test_Schemas(t *testing.T) {
	assert := assert.New(t)

	// Get a list of all immediate (non-recursive) subdirectories in /schemas
	schemaPaths, err := listSubdirs("/schemas")
	if err != nil {
		assert.Fail("failed to list subdirectories in /schemas: %v", err)
	}

	for _, schemaName := range schemaPaths {
		// Get the versions for the schema.
		versions, err := getSchemaPaths(schemaName)
		if err != nil {
			assert.Fail("failed to get schema paths for %s: %v", schemaName, err)
			continue
		}

		for _, version := range versions {
			// Verify that the DDL file exists.
			ddlFile := filepath.Join(version, "DDL", "schema.sql")
			if _, err := os.Stat(ddlFile); os.IsNotExist(err) {
				assert.Fail("DDL file does not exist for %s version %s: %v", schemaName, version, err)
			}

			// Verify that the DML file exists.
			dmlFile := filepath.Join(version, "DML", "data.sql")
			if _, err := os.Stat(dmlFile); os.IsNotExist(err) {
				assert.Fail("DML file does not exist for %s version %s: %v", schemaName, version, err)
			}

			// Verify that contents of the sequence files.
			updatesPath := filepath.Join(version, "updates")

			// Get a list of all files that match the pattern "sequence.*.sql" and
			// extract the environment name from the filename.
			environments := []string{}
			sequenceFiles, err := filepath.Glob(filepath.Join(updatesPath, "sequence.*.sql"))
			if err != nil {
				assert.Fail("failed to list sequence files for %s version %s: %v", schemaName, version, err)
				continue
			}
			for _, sequenceFile := range sequenceFiles {
				// Extract the environment name from the filename.
				// The filename format is "sequence.<environment>.sql".
				parts := strings.Split(filepath.Base(sequenceFile), ".")
				if len(parts) != 3 {
					assert.Fail("invalid sequence file name for %s version %s: %s", schemaName, version, sequenceFile)
					continue
				}
				envName := parts[1]
				if envName == "" {
					assert.Fail("empty environment name in sequence file for %s version %s: %s", schemaName, version, sequenceFile)
					continue
				}
				environments = append(environments, envName)
			}

			// For each environment, validate the sequence files.
			for _, envName := range environments {
				environmentSequenceFiles, _ := getSequenceFiles(schemaName, version, envName)
				migrations, err := parseSequenceFiles(environmentSequenceFiles)
				if err != nil {
					assert.Fail("failed to parse sequence files for %s version %s environment %s: %v", schemaName, version, envName, err)
					continue
				}

				// parseSequenceFiles errors if the sequence file contents are not
				// properly formatted, or if the MD5 could not be loaded, so we don't
				// need to check for those errors here.
				// There are a few checks that we need to enforce, though:
				//  * no sequence numbers may be duplicated.
				//  * the filename and MD5 must not be empty.
				//  * verify that the filename exists.
				//  * verify that the file is a proper file (many checks)
				seen := make(map[float64]bool)
				for _, migration := range migrations {
					// Validate that the sequence number is unique.
					if _, ok := seen[migration.Sequence]; ok {
						assert.Fail("duplicate sequence number found for %s version %s environment %s: %f", schemaName, version, envName, migration.Sequence)
					}
					seen[migration.Sequence] = true

					// Validate that each migration's FileName and MD5 are not empty.
					if migration.FileName == "" {
						assert.Fail("empty filename in migration for %s version %s environment %s: %v", schemaName, version, envName, migration)
					}
					if migration.MD5Hash == "" {
						assert.Fail("empty MD5 in migration for %s version %s environment %s: %v", schemaName, version, envName, migration)
					}

					// Validate that the filename exists.
					filePath := filepath.Join(updatesPath, migration.FileName)
					if _, err := os.Stat(filePath); os.IsNotExist(err) {
						assert.Fail("file does not exist for %s version %s environment %s: %s", schemaName, version, envName, filePath)
					}
					// Validate that the file is not empty.
					fileInfo, err := os.Stat(filePath)
					if err != nil {
						assert.Fail("failed to stat file for %s version %s environment %s: %s", schemaName, version, envName, filePath)
					}
					if fileInfo.Size() == 0 {
						assert.Fail("file is empty for %s version %s environment %s: %s", schemaName, version, envName, filePath)
					}
					// Validate that the file is a valid SQL file.
					if filepath.Ext(filePath) != ".sql" {
						assert.Fail("file is not a valid SQL file for %s version %s environment %s: %s", schemaName, version, envName, filePath)
					}
					// Validate that the file is not a directory.
					if fileInfo.IsDir() {
						assert.Fail("file is a directory for %s version %s environment %s: %s", schemaName, version, envName, filePath)
					}
					// Validate that the file is not a symlink.
					if fileInfo.Mode()&os.ModeSymlink != 0 {
						assert.Fail("file is a symlink for %s version %s environment %s: %s", schemaName, version, envName, filePath)
					}
					// Validate that the file is not a socket.
					if fileInfo.Mode()&os.ModeSocket != 0 {
						assert.Fail("file is a socket for %s version %s environment %s: %s", schemaName, version, envName, filePath)
					}
					// Validate that the file is not a named pipe.
					if fileInfo.Mode()&os.ModeNamedPipe != 0 {
						assert.Fail("file is a named pipe for %s version %s environment %s: %s", schemaName, version, envName, filePath)
					}
					// Validate that the file is not a block device.
					if fileInfo.Mode()&os.ModeDevice != 0 {
						assert.Fail("file is a block device for %s version %s environment %s: %s", schemaName, version, envName, filePath)
					}
					// Validate that the file is not a character device.
					if fileInfo.Mode()&os.ModeCharDevice != 0 {
						assert.Fail("file is a character device for %s version %s environment %s: %s", schemaName, version, envName, filePath)
					}
					// Validate that the file is not a FIFO.
					if fileInfo.Mode()&os.ModeNamedPipe != 0 {
						assert.Fail("file is a FIFO for %s version %s environment %s: %s", schemaName, version, envName, filePath)
					}
				}
			}
		}
	}
}

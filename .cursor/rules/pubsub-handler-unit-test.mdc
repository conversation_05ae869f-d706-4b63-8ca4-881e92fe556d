---
description: 
globs: microservices/etl/processors/handlers/**/handler_test.go
alwaysApply: false
---
# PubSub Handler Unit Test Rule

This rule describes the standard approach for unit testing PubSub handler functions that:
- Pull messages from a PubSub subscription,
- Use `connect.GetConnections` to obtain dependencies,
- And primarily process, log, or acknowledge messages.

## Steps for Writing a Unit Test

1. **Setup Test Context and Mocks**
   - Import: `context`, `testing`, `time`, `cloud.google.com/go/pubsub`, `synapse-its.com/shared/connect`, and `synapse-its.com/shared/mocks`.
   - Create a new context and fake connections using `mocks.FakeConns()`.
   - Add the fake connections to the context with `connect.WithConnections`.

2. **Patch `connect.GetConnections`**
   - Save the original `connect.GetConnections`.
   - Override it in the test to always return your fake connections.
   - Use `defer` to restore the original after the test.

3. **Setup PubSub Topic and Subscription**
   - Create a fake topic: `conns.Pubsub.Topic("test-topic")`.
   - Link a subscription to the topic using:  
     `conns.Pubsub.CreateSubscription(ctx, "test-sub", connect.SubscriptionConfig{Topic: topic})`.

4. **Publish Test Messages**
   - Use `topic.Publish(ctx, &pubsub.Message{Data: []byte("your-message")})` to add messages.

5. **Run the Handler**
   - Start the handler in a goroutine, passing the test context and subscription name.
   - Use a `done` channel to signal completion.

6. **Wait and Assert**
   - Use a `select` with a timeout to wait for handler completion.
   - Optionally, add assertions for side effects if needed.

7. **Clean Up**
   - The `defer` statement restores the original `connect.GetConnections`.

## Example

See [handler_test.go](mdc:microservices/etl/processors/handlers/gatewayGatewayLog/handler_test.go) for a concrete implementation.

---

**Reuse this rule for any handler that matches the above criteria.**

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.30.2
// source: gateway/v1/deviceData.proto

package gatewayv1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// DeviceData represents the overall body payload.
type DeviceData struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Repeated list of all device entries.
	Messages      []*DeviceEntry `protobuf:"bytes,1,rep,name=messages,proto3" json:"messages,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeviceData) Reset() {
	*x = DeviceData{}
	mi := &file_gateway_v1_deviceData_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceData) ProtoMessage() {}

func (x *DeviceData) ProtoReflect() protoreflect.Message {
	mi := &file_gateway_v1_deviceData_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceData.ProtoReflect.Descriptor instead.
func (*DeviceData) Descriptor() ([]byte, []int) {
	return file_gateway_v1_deviceData_proto_rawDescGZIP(), []int{0}
}

func (x *DeviceData) GetMessages() []*DeviceEntry {
	if x != nil {
		return x.Messages
	}
	return nil
}

// Defines an individual message from a device.
type DeviceEntry struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Unique identifier for the device.
	DeviceId string `protobuf:"bytes,1,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	// The payload message.
	// For most message-types, this field is a byte array (typically containing byte data directly from the device).
	// For message type "Mac Address", the content is a UTF-8 string.
	Message       []byte `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeviceEntry) Reset() {
	*x = DeviceEntry{}
	mi := &file_gateway_v1_deviceData_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceEntry) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceEntry) ProtoMessage() {}

func (x *DeviceEntry) ProtoReflect() protoreflect.Message {
	mi := &file_gateway_v1_deviceData_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceEntry.ProtoReflect.Descriptor instead.
func (*DeviceEntry) Descriptor() ([]byte, []int) {
	return file_gateway_v1_deviceData_proto_rawDescGZIP(), []int{1}
}

func (x *DeviceEntry) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *DeviceEntry) GetMessage() []byte {
	if x != nil {
		return x.Message
	}
	return nil
}

var File_gateway_v1_deviceData_proto protoreflect.FileDescriptor

const file_gateway_v1_deviceData_proto_rawDesc = "" +
	"\n" +
	"\x1bgateway/v1/deviceData.proto\x12\n" +
	"gateway.v1\"A\n" +
	"\n" +
	"DeviceData\x123\n" +
	"\bmessages\x18\x01 \x03(\v2\x17.gateway.v1.DeviceEntryR\bmessages\"D\n" +
	"\vDeviceEntry\x12\x1b\n" +
	"\tdevice_id\x18\x01 \x01(\tR\bdeviceId\x12\x18\n" +
	"\amessage\x18\x02 \x01(\fR\amessageBOZMbitbucket.org/synapse-its/protobuf-schemas/protomessages/gateway/v1;gatewayv1b\x06proto3"

var (
	file_gateway_v1_deviceData_proto_rawDescOnce sync.Once
	file_gateway_v1_deviceData_proto_rawDescData []byte
)

func file_gateway_v1_deviceData_proto_rawDescGZIP() []byte {
	file_gateway_v1_deviceData_proto_rawDescOnce.Do(func() {
		file_gateway_v1_deviceData_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_gateway_v1_deviceData_proto_rawDesc), len(file_gateway_v1_deviceData_proto_rawDesc)))
	})
	return file_gateway_v1_deviceData_proto_rawDescData
}

var file_gateway_v1_deviceData_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_gateway_v1_deviceData_proto_goTypes = []any{
	(*DeviceData)(nil),  // 0: gateway.v1.DeviceData
	(*DeviceEntry)(nil), // 1: gateway.v1.DeviceEntry
}
var file_gateway_v1_deviceData_proto_depIdxs = []int32{
	1, // 0: gateway.v1.DeviceData.messages:type_name -> gateway.v1.DeviceEntry
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_gateway_v1_deviceData_proto_init() }
func file_gateway_v1_deviceData_proto_init() {
	if File_gateway_v1_deviceData_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_gateway_v1_deviceData_proto_rawDesc), len(file_gateway_v1_deviceData_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_gateway_v1_deviceData_proto_goTypes,
		DependencyIndexes: file_gateway_v1_deviceData_proto_depIdxs,
		MessageInfos:      file_gateway_v1_deviceData_proto_msgTypes,
	}.Build()
	File_gateway_v1_deviceData_proto = out.File
	file_gateway_v1_deviceData_proto_goTypes = nil
	file_gateway_v1_deviceData_proto_depIdxs = nil
}

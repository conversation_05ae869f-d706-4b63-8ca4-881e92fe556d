package authenticate

import (
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"time"

	response "synapse-its.com/shared/api/response"
	security "synapse-its.com/shared/api/security"
	softwareGateway "synapse-its.com/shared/api/softwaregateway"
	connect "synapse-its.com/shared/connect"
	logger "synapse-its.com/shared/logger"
)

// <PERSON><PERSON> serves as the handler for the main function for the gateway authentication endpoint.
func Handler(w http.ResponseWriter, r *http.Request) {
	// get the following attributes passed in via the header:
	//    gateway-device-id
	//    message-type
	//    x-api-key is not used and can be ignored
	// get the following from the body:
	//    {"token": "thetokenvalue"} the value may be empty

	// obtain the software gateway record from the db (lookup SoftwareGateway)
	//    message-validation
	//    if the gateway-device-id empty - reject
	//    if the message-type <> authenticate - reject
	//    if token missing from body - reject
	//
	//    db-validation
	//    if the gateway-device-id not found in db - reject
	//    if db software gateway not enabled - reject
	//    if software gateway token does not match the db token (when the db token is not blank) - reject
	//
	//	  if the db software gateway token does match - return the settings and rotate the token
	//
	//	  if the db software gateway token matches and there is not a db token - return the setting and establish a token
	//

	gatewayId, rqToken, err := parseRequest(r)
	if err != nil {
		logger.Infof("Unable to parse request: %v", err)
		response.CreateUnauthorizedResponse(w)
		return
	}

	// Get the postgres connection.
	connections, err := connect.GetConnections(r.Context())
	if err != nil {
		logger.Errorf("%v", err)
		response.CreateInternalErrorResponse(w)
		return
	}
	pg := connections.Postgres

	// Query database for token information
	gatewayInfo, err := getGatewayInfo(pg, gatewayId)
	if err != nil {
		logger.Infof("Error getting gateway info: %v", err)
		response.CreateUnauthorizedResponse(w)
		return
	}

	// Check whether the token from the DB matches the request, if token is blank continue and set a new token
	if err = validateToken(gatewayInfo.Token, rqToken); err != nil {
		logger.Infof("Error validating token: %v", err)
		response.CreateUnauthorizedResponse(w)
		return
	}

	// Query the database for gateway configuration
	gatewaySettings, err := getGatewayConfig(pg, gatewayInfo.Id)
	if err != nil {
		logger.Infof("Error getting gateway config: %v", err)
		response.CreateInternalErrorResponse(w)
		return
	}

	// Query the database for firebase config
	googleConfig, err := getFirebaseConfig(pg)
	if err != nil {
		logger.Infof("Error getting firebase config: %v", err)
		response.CreateInternalErrorResponse(w)
		return
	}

	// Get JWT public key
	_, publicKey, err := security.GetJWTAsymmetric()
	if err != nil {
		logger.Infof("Public key not found in secrets manager: %v", err)
		response.CreateInternalErrorResponse(w)
		return
	}

	// Query the database for all associated devices to the gateway
	deviceSettings, err := getDeviceSettings(pg, gatewayInfo.Id)
	if err != nil {
		logger.Infof("Error device config: %v", err)
		response.CreateInternalErrorResponse(w)
		return
	}

	// Generate new token and update the database with a new token and the current utc time
	cloudInfo, err := setNewGatewayToken(pg, gatewayId)
	if err != nil {
		logger.Errorf("Error setting gateway token: %v", err)
		response.CreateInternalErrorResponse(w)
		return
	}
	cloudInfo.ApiKey = gatewayInfo.APIKey

	globalSettings := &softwareGateway.GlobalSettings{}
	globalSettings.OrganizationId = gatewayInfo.OrganizationIdentifier
	globalSettings.PublicKey = publicKey
	globalSettings.AWS = *cloudInfo
	globalSettings.Devices = *deviceSettings
	globalSettings.Google = *googleConfig
	globalSettings.Gateway = *gatewaySettings

	logger.Debugf("Successfully authenticated: %v", gatewayId)
	response.CreateSuccessResponse(globalSettings, w)
}

var parseRequest = func(r *http.Request) (string, string, error) {
	gatewayId := r.Header.Get("gateway-device-id")
	if gatewayId == "" {
		return "", "", errors.New("header gateway-device-id is not present")
	}

	messageType := r.Header.Get("message-type")
	if messageType == "" {
		return "", "", errors.New("header message-type is not present")
	}
	if messageType != "authenticate" {
		return "", "", errors.New("header message-type != authenticate")
	}

	var body requestBody
	err := json.NewDecoder(r.Body).Decode(&body)
	if err != nil {
		return "", "", err
	}

	if body.Token == nil {
		return "", "", errors.New("token does not exist in the body")
	}
	return gatewayId, *body.Token, nil
}

var getGatewayInfo = func(pg connect.DatabaseExecutor, gatewayId string) (*dbGatewayInfo, error) {
	query := `
		SELECT 
			s.Id, 
			o.OrganizationIdentifier, 
			s.APIKey, 
			COALESCE(s.Token, '') AS Token
		FROM {{SoftwareGateway}} s
		INNER JOIN {{Organization}} o
			ON s.OrganizationId = o.Id
		WHERE s.IsEnabled AND s.SoftwareGatewayIdentifier = $1`
	gatewayInfo := &dbGatewayInfo{}
	err := pg.QueryRowStruct(gatewayInfo, query, gatewayId)

	// Query returns no rows
	if errors.Is(err, sql.ErrNoRows) {
		return nil, fmt.Errorf("GatewayId (%s) was not found. Either the gatewayId does not exist, the gatewayId is disabled, etc", gatewayId)
	}
	if err != nil {
		return nil, err
	}
	return gatewayInfo, nil
}

var validateToken = func(dbToken string, rqToken string) error {
	if dbToken != "" && rqToken != dbToken {
		return errors.New("token presented by gateway does not match the token in the db")
	}
	return nil
}

var getGatewayConfig = func(pg connect.DatabaseExecutor, softwareGatewayId int64) (*softwareGateway.GatewaySettings, error) {
	// Get the software gateway configs
	query := `
		SELECT 
			s.Config AS Value
		FROM {{SoftwareGatewayConfig}} s
		WHERE s.SoftwareGatewayId = $1`
	config := dbConfig{}
	gatewaySettings := &softwareGateway.GatewaySettings{}
	err := pg.QueryRowStruct(&config, query, softwareGatewayId)
	// Query returns no rows
	if errors.Is(err, sql.ErrNoRows) {
		return nil, fmt.Errorf("SoftwareGatewayId (%v) was not found in {{SoftwareGatewayConfig}}. The SoftwareGatewayId does not exist, etc", softwareGatewayId)
	}
	// Query error
	if err != nil {
		return nil, err
	}

	// Pull default gateway settings
	err = json.Unmarshal([]byte(config.Value), gatewaySettings)
	if err != nil {
		return nil, err
	}
	return gatewaySettings, nil
}

var getFirebaseConfig = func(pg connect.DatabaseExecutor) (*string, error) {
	query := `
		SELECT 
			c.Value
		FROM {{Config}} c
		WHERE c.Key = 'FirebaseAuth'`
	config := &dbConfig{}
	err := pg.QueryRowStruct(config, query)
	// Query returns no rows
	if errors.Is(err, sql.ErrNoRows) {
		return nil, errors.New("FirebaseAuth was not found in {{Config}}. The FirebaseAuth does not exist, etc")
	}
	if err != nil {
		return nil, err
	}
	return &config.Value, nil
}

var getDeviceSettings = func(pg connect.DatabaseExecutor, dbSoftwareGatewayId int64) (*[]softwareGateway.DeviceSettings, error) {
	query := `
		SELECT 
			DeviceIdentifier AS device_id, 
			Latitude, 
			Longitude, 
			IPAddress AS ip_address, 
			Port, 
			FlushConnectionMs AS flush_connection_ms, 
			EnableRealtime AS enable_realtime
		FROM {{DeviceCommunicationConfig}} 
		WHERE IsEnabled = 1 AND SoftwareGatewayId = $1`
	deviceSettings := []softwareGateway.DeviceSettings{}
	if err := pg.QueryGenericSlice(&deviceSettings, query, dbSoftwareGatewayId); err != nil {
		return nil, err
	}
	return &deviceSettings, nil
}

var setNewGatewayToken = func(pg connect.DatabaseExecutor, gatewayId string) (*softwareGateway.CloudSettings, error) {
	newToken, err := generateRandomTokenHex(50)
	if err != nil {
		return nil, err
	}
	cloudInfo := softwareGateway.CloudSettings{}
	cloudInfo.Token = newToken
	query := `
		UPDATE {{SoftwareGateway}} 
		SET 
			Token = $1, 
			DateLastCheckedInUTC = $2 
		WHERE SoftwareGatewayIdentifier = $3`
	if _, err = pg.Exec(query, newToken, time.Now().UTC().Format(time.DateTime), gatewayId); err != nil {
		return nil, err
	}
	return &cloudInfo, nil
}

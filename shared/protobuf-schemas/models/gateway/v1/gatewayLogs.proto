syntax = "proto3";
 
import "google/protobuf/timestamp.proto";
 
package gateway.v1;

option go_package = "bitbucket.org/synapse-its/protobuf-schemas/protomessages/gateway/v1;gatewayv1";
 
// GatewayLogs represents the overall body payload.
message GatewayLogs {
  
  // Field 1: A timestamp indicating when the message was created.
  google.protobuf.Timestamp message_time = 1;
 
  // Field 2: A byte-array containing the zipped bytes of either the perfStats or a gatewayLog
  // The message is always uses zip compression.
  bytes message = 2;
}

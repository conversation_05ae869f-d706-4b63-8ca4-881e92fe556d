package authorizer

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"net/http"
	"strings"
	"time"

	"synapse-its.com/shared/api/jwttokens"
	"synapse-its.com/shared/api/security"
	connect "synapse-its.com/shared/connect"
)

type ctxKey string

const (
	authInfoKey ctxKey = "userInfo"
	headerJWT   string = "jwt-token"
)

var Authorize = func(ctx context.Context, r *http.Request) (context.Context, error) {
	// Get the jwt-token
	authToken := r.Header.Get(headerJWT)
	if authToken == "" {
		return ctx, fmt.Errorf("%w: jwt-token not found in header", ErrUnauthorized)
	}

	// Validate the jwt-token in the header
	_, _, err := jwttokens.ValidateJJwtToken(authToken)
	if err != nil {
		return ctx, fmt.Errorf("%w: %v", ErrUnauthorized, err)
	}

	// Get the postgres connection.
	connections, err := connect.GetConnections(ctx)
	if err != nil {
		return ctx, fmt.Errorf("%w: %w", ErrInternal, err)
	}
	pg := connections.Postgres

	// Validate token and get user info
	userInfo, err := getUserFromToken(pg, authToken)
	if err != nil {
		return ctx, err
	}

	// EMA-321 - check the RoleId - we want to restrict integrators -- roleId = 2 to only having access to the data/v2/device rest API
	// TODO: Make this configurable in the future with user access management
	if !validEndPointRequestFromRole(userInfo, r) {
		return ctx, fmt.Errorf("%w: user does not have the correct permissions to access %v", ErrForbidden, r.URL.Path)
	}

	// allow access
	ui := &UserInfo{ID: userInfo.UserID, RoleID: userInfo.RoleID}
	return context.WithValue(ctx, authInfoKey, ui), nil
}

func UserInfoFromContext(ctx context.Context) (*UserInfo, bool) {
	ui, ok := ctx.Value(authInfoKey).(*UserInfo)
	return ui, ok
}

// Given a valid JWT token, checks whether token is active in the database
var getUserFromToken = func(pg connect.DatabaseExecutor, jwtToken string) (*dbUserInfo, error) {
	// All DB jwts are stored as SHA256
	sha256TokenValue := security.CalculateSHA256(jwtToken)

	query := `
		SELECT 
			u.Id, 
			u.RoleId,
			ut.ExpirationUTC::timestamptz AS expirationutc 
		FROM {{User}} u
		JOIN {{UserToken}} ut
			ON u.Id = ut.UserId 
		WHERE u.IsEnabled = 1 AND ut.JWTTokenSha256 = $1`
	userInfo := &dbUserInfo{}
	if err := pg.QueryRowStruct(userInfo, query, sha256TokenValue); err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			// if no rows, return error
			return nil, fmt.Errorf("%w: token not found or the user is disabled", ErrUnauthorized)
		}
		// return general error
		return nil, fmt.Errorf("%w: %v", ErrInternal, err)
	}

	// check the token expiration
	if now := time.Now().UTC(); now.After(userInfo.ExpirationUTC) {
		return nil, fmt.Errorf("%w: token expired at %s", ErrForbidden, userInfo.ExpirationUTC)
	}
	return userInfo, nil
}

// Validate whether the user has access to the endpoint from the http request
var validEndPointRequestFromRole = func(userInfo *dbUserInfo, r *http.Request) bool {
	switch userInfo.RoleID {

	// roleID = 1 is an admin role, they will have access to all endpoints
	case 1:
		return true

	// roleid = 2 is an integrator role, validate that they only have access to the device endpoint
	case 2:
		allowedEndPoints := []string{
			"/data/v2/device",
			"/api/data/v3/device",
		}
		for _, endPoint := range allowedEndPoints {
			if strings.HasPrefix(r.URL.Path, endPoint) {
				return true
			}
		}
	}

	// If roleID is not specified or path doesn't match, return false
	return false
}

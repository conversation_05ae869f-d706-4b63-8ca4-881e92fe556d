// Package softwaregateway contains elements that are shared between the gateway-app and the cloud
package softwaregateway

// DeviceSettings contains all configuration info for a device
type DeviceSettings struct {
	Device_ID          string `json:"device_id"`                                    // uniquely identifies this device amongst all possible devices
	Latitude           string `json:"latitude"`                                     // the device latitude
	Longitude          string `json:"longitude"`                                    // the device longitude
	IP_Address         string `json:"ip_address"`                                   // the device ip address
	Port               string `json:"port"`                                         // the device port
	FlushConnection_MS string `json:"flush_connection_ms" db:"flush_connection_ms"` // the amount of time to wait to flush all bytes from the tcp connection for the device
	EnableRealtime     string `json:"enable_realtime" db:"enable_realtime"`         // flag indiciating whether or not to send realtime channel state to firebase
}

// GatewaySettings contains all configuration info for the software gateway
type GatewaySettings struct {
	ApplicationVersion                            string `json:"application_version"`                                // ApplicationVersion represents the version of this software.
	RecordHttpRequestsToLogFolder                 bool   `json:"record_http_requests_to_folder"`                     // RecordHttpRequestsToLogFolder allows for the recording of bytes to be sent over https to be persisted to disk for analysis
	DeviceStateSendFrequencySeconds               int    `json:"device_state_send_frequency_seconds"`                // DeviceStateSendFrequencySeconds is the frequency (sec) to send heartbeat message to the cloud
	ChannelStateSendFrequencyMilliseconds         int    `json:"channel_state_send_frequency_milliseconds"`          // DeviceStateSendFrequencySeconds is the frequency (sec) to send heartbeat message to the cloud
	SoftwareUpdateCheckFrequencySeconds           int    `json:"software_update_check_frequency_seconds"`            // SoftwareUpdateCheckFrequencySeconds is the frequency (sec) to check for software update available
	GatewayPerformanceStatsOutputFrequencySeconds int    `json:"gateway_performance_stats_output_frequency_seconds"` // GatewayPerformanceStatsOutputFrequencySeconds is the frequency to output processor engine stats
	EdiDeviceProcessingRetries                    int    `json:"edi_device_processing_retries"`                      // EdiDeviceProcessingRetries is the the max number of times to (transmit a reuest to the device + have the response from the device satisfy the response checksum)
	EdiDevicePersistConnection                    bool   `json:"edi_device_persist_connection"`                      // keep a open persistent tcp connection to the EDI device?
	RestAPIDeviceEndpoint                         string `json:"rest_api_device_endpoint"`                           // RestAPIDeviceEndpoint is the endpoint responsible for ingesting device information
	LogFilename                                   string `json:"log_filename"`                                       // LogFilename is the name of the log file
	LogFileMaxSizeMB                              int    `json:"log_file_max_size_mb"`                               // LogFileMaxSizeMB is the max size of the log file before being rolled over
	LogMaxBackups                                 int    `json:"log_max_backups"`                                    // LogMaxBackups is the max number of rolled over logs to retain
	LogMaxAgeInDays                               int    `json:"log_max_age_in_days"`                                // LogMaxAgeInDays serves as the maximum log retention
	LogCompressBackups                            bool   `json:"log_compress_backups"`                               // LogCompressBackups indicates whether or not to comrpess the rolled over logs
	ConfigChangeCheckFrequencySeconds             int    `json:"config_change_check_frequency_seconds"`              // ConfigChangeCheckFrequencySeconds is the frequency for the gateway to check for config changes and instructions
	SendGatewayLogsToCloud                        bool   `json:"send_gateway_logs_to_cloud"`                         // SendGatewayLogsToCloud indicates whether or not to send gateway logs to the cloud
	SendGatewayPerformanceStatsToCloud            bool   `json:"send_gateway_performance_stats_to_cloud"`            // SendGatewayPerformanceStatsToCloud indicates whether or not to send gateway performance stats to the cloud
	LogLevel                                      string `json:"log_level"`                                          // LogLevel indicates the log level output to be generator by zap -- debug, info, error
	WebSocketActive                               bool   `json:"ws_active"`                                          // WebSocketActive is a flag indicating whether or not to turn on ws integration for the gateway
	WebSocketPort                                 string `json:"ws_port"`                                            // WebSocketPort is the port the gateway web socket will communicate on
	WebSocketEndPoint                             string `json:"ws_endpoint"`                                        // WebSocketEndPoint is the endpoint the web socket is listenting on e.g., /gateway
	WebSocketMaxConnections                       int    `json:"ws_max_connections"`                                 // WebSocketMaxConnections is the max connections allowable on the web socket port
	WebSocketSendFrequencyMilliseconds            int    `json:"ws_send_frequency_milliseconds"`                     // WebSocketSendFrequencyMilliseconds is the frequency to push data across the ws connection
	WebSocketHeartbeatSendFrequencyMilliseconds   int    `json:"ws_heartbeat_send_frequency_milliseconds"`           // WebSocketHeartbeatSendFrequencyMillisecondsis the frequency to send heartbeat packets across the web socket connection giving an indication the web socket is active
	ThresholdDeviceErrorSeconds                   int    `json:"threshold_device_error_seconds"`                     // ThresholdDeviceErrorSeconds is the seconds that need to elapsed before reporting the device is in an 'error' state
}

// GoogleSettings contains all info used to establish a connection to gogle firebase
type GoogleSettings struct {
	Type                     string `json:"type"`
	ProjectId                string `json:"project_id"`
	PrivateKeyID             string `json:"private_key_id"`
	PrivateKey               string `json:"private_key"`
	ClientEmail              string `json:"client_email"`
	ClientId                 string `json:"clientId"`
	AuthUri                  string `json:"auth_uri"`
	TokenUri                 string `json:"token_uri"`
	AuthProviderX509CcertUrl string `json:"auth_provider_x509_cert_url"`
	ClientX509CertUrl        string `json:"client_x509_cert_url"`
	UniverseDomain           string `json:"universe_domain"`
}

// CloudSettings contains settings used by the software gateway to connect to the data-core cloud
type CloudSettings struct {
	Token  string `json:"token"`   // the rotating token used by the software gateway to authenticate with the cloud
	ApiKey string `json:"api_key"` // the api key used by the software gateway to communicate with the cloud
}

// GlobalSettings contains all settings used by the software gateway
type GlobalSettings struct {
	OrganizationId string           `json:"organization_id"` // the organization the software gateway is assigned to
	PublicKey      string           `json:"public_key"`      // the public key used to authenticate the signature of a jwt
	AWS            CloudSettings    `json:"aws"`             // all cloud settings
	Gateway        GatewaySettings  `json:"gateway"`         // all software gateway settings
	Google         string           `json:"google"`          // google settings (aka firebase connection info) base 64 end encrypted
	Devices        []DeviceSettings `json:"devices"`         // device info for all devices the software gateway communicates with
}

// OnDemandPayload contains individual instructions per device
type OnDemandPayload struct {
	DeviceIdentifier string `json:"device_identifier"`
	Instruction      string `json:"instruction"`
}

// SoftwareGatewayUpdateResponse contains the list of all instructions for the gateway
type SoftwareGatewayUpdateResponse struct {
	ConfigUpdateAvailable string            `json:"config_update_available"`
	Instructions          []OnDemandPayload `json:"instructions"`
}

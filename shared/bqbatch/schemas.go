package bqbatch

import (
	"time"
)

// The table used for the integration test.
var T_BQBATCH = "test_bqbatch"

type BatchTest struct {
	Key   string        `bigquery:"key"`
	Value string        `bigquery:"value"`
	Obj   *BatchTestObj `bigquery:"obj"`
	Arr   []bool        `bigquery:"arr"`
}

type BatchTestObj struct {
	Flag1 bool `bigquery:"flag1"`
	Flag2 bool `bigquery:"flag2"`
}

// Pubsub wrapper fallback incase batch load fails
type FailedBatch struct {
	Table      string           `json:"table"`
	Timestamp  time.Time        `json:"timestamp"`
	Error      string           `json:"error"`
	Rows       []map[string]any `json:"rows"`
	RetryCount int              `json:"retry_count"`
}

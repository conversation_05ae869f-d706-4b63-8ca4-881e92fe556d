package raw

import (
	"context"
	"errors"
	"sync/atomic"
	"testing"
	"time"

	"cloud.google.com/go/pubsub"
	"synapse-its.com/shared/bqbatch"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/mocks"
	"synapse-its.com/shared/pubsubdata"
)

func TestRawHandlerWithDeps(t *testing.T) {
	tests := []struct {
		name         string
		connectorErr error
		batchErr     error
		receiveErr   error
		attrErr      error
		sendDLQErr   error
		addErr       error
		wantAdds     int32
		wantDLQs     int32
	}{
		{"BatchError", nil, errors.New("fail batch"), nil, nil, nil, nil, 0, 0},
		{"ConnectionError", errors.New("conn fail"), nil, nil, nil, nil, nil, 0, 0},
		{"ReceiveError", nil, nil, errors.New("recv fail"), nil, nil, nil, 0, 0},
		{"AttributeParseErrorWithDLQSuccess", nil, nil, nil, errors.New("bad attr"), nil, nil, 0, 1},
		{"AttributeParseErrorWithDLQFailure", nil, nil, nil, errors.New("bad attr"), errors.New("DLQ fail"), nil, 0, 1},
		{"BatchAddError", nil, nil, nil, nil, nil, errors.New("add fail"), 1, 0},
		{"HappyPath", nil, nil, nil, nil, nil, nil, 1, 0},
	}

	for _, tc := range tests {
		tc := tc
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()

			ctx := context.Background()
			var addCount int32
			var sendDLQCount int32

			conn := mocks.FakeConns()
			client := conn.Pubsub.(*mocks.FakePubsubClient)
			client.ReceiveError = tc.receiveErr

			batch, _ := mocks.FakeBatch(ctx)
			batch.(*mocks.FakeBatcher).AddFn = func(row interface{}) error {
				atomic.AddInt32(&addCount, 1)
				return tc.addErr
			}

			deps := HandlerDeps{
				Connector: func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
					return conn, tc.connectorErr
				},
				GetBatch: func(ctx context.Context) (bqbatch.Batcher, error) {
					return batch, tc.batchErr
				},
				ParseAttributes: func(attrs map[string]string) (pubsubdata.CommonAttributes, pubsubdata.HeaderDetails, error) {
					return pubsubdata.CommonAttributes{}, pubsubdata.HeaderDetails{}, tc.attrErr
				},
				SendToDLQ: func(ctx context.Context, c connect.PsClient, msg *pubsub.Message, reason string) error {
					atomic.AddInt32(&sendDLQCount, 1)
					return tc.sendDLQErr
				},
			}

			if tc.connectorErr == nil && tc.batchErr == nil && tc.receiveErr == nil {
				topic := client.Topic("test-topic")
				client.CreateSubscription(ctx, "test-sub", connect.SubscriptionConfig{Topic: topic})

				topic.Publish(ctx, &pubsub.Message{
					Data:            []byte("abc"),
					Attributes:      map[string]string{"dlq_reason": "test"},
					ID:              "msg-id",
					PublishTime:     time.Now(),
					DeliveryAttempt: func(i int) *int { return &i }(1),
				})
			}

			handler := HandlerWithDeps(deps)
			handler(ctx, "test-sub")

			if got := atomic.LoadInt32(&addCount); got != tc.wantAdds {
				t.Errorf("Expected batch.Add calls = %d; got %d", tc.wantAdds, got)
			}
			if got := atomic.LoadInt32(&sendDLQCount); got != tc.wantDLQs {
				t.Errorf("Expected SendToDLQ calls = %d; got %d", tc.wantDLQs, got)
			}
		})
	}
}

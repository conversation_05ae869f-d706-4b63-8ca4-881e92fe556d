// Package constants contains the constants used by this lambda function.
package authenticate

// MinUsernameLength is the minimum length of the user's username.
const MinUsernameLength = 5

// MaxUsernameLength is the maximum length of the user's username.
const MaxUsernameLength = 128

// MinUserPasswordLength is the minimum length of the user's password.
const MinUserPasswordLength = 8

// MaxUserPasswordLength is the maximum length of the user's password.
const MaxUserPasswordLength = 256

// NonceByteLength is length the nonce must be in order to pass being a valid parameter - since the parameter pass in is hex, it will be 2x this value
const NonceByteLength = 128

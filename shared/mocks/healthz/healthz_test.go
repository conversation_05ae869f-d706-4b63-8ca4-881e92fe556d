package healthz

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestFakeHealthzServer_ListenAndServe(t *testing.T) {
	// Case 1: no error returned
	f := &FakeHealthzServer{}
	err := f.ListenAndServe()
	assert.NoError(t, err, "expected no error when ListenErr is nil")
	assert.True(t, f.ListenCalled, "expected ListenCalled to be true")

	// Case 2: error returned
	expectedErr := errors.New("listen failed")
	f2 := &FakeHealthzServer{ListenErr: expectedErr}
	err2 := f2.ListenAndServe()
	assert.Equal(t, expectedErr, err2, "expected ListenAndServe to return ListenErr")
	assert.True(t, f2.ListenCalled, "expected ListenCalled to be true even on error")
}

func TestFakeHealthzServer_Shutdown(t *testing.T) {
	ctx := context.Background()

	// Case 1: no error returned
	f := &FakeHealthzServer{}
	err := f.Shutdown(ctx)
	assert.NoError(t, err, "expected no error when ShutdownErr is nil")
	assert.True(t, f.ShutdownCalled, "expected ShutdownCalled to be true")

	// Case 2: error returned
	expectedErr := errors.New("shutdown failed")
	f2 := &FakeHealthzServer{ShutdownErr: expectedErr}
	err2 := f2.Shutdown(ctx)
	assert.Equal(t, expectedErr, err2, "expected Shutdown to return ShutdownErr")
	assert.True(t, f2.ShutdownCalled, "expected ShutdownCalled to be true even on error")
}

func TestFakeHealthzServer_SetBootComplete(t *testing.T) {
	f := &FakeHealthzServer{}
	assert.False(t, f.SetBootCalled, "initially, SetBootCalled should be false")
	f.SetBootComplete()
	assert.True(t, f.SetBootCalled, "SetBootComplete should set SetBootCalled to true")
}

func TestFakeHealthzServer_SetReady(t *testing.T) {
	f := &FakeHealthzServer{}
	assert.False(t, f.SetReadyCalled, "initially, SetReadyCalled should be false")
	f.SetReady()
	assert.True(t, f.SetReadyCalled, "SetReady should set SetReadyCalled to true")
}

func TestFakeHealthzServer_SetNotReady(t *testing.T) {
	f := &FakeHealthzServer{}
	assert.False(t, f.SetNotReadyCalled, "initially, SetNotReadyCalled should be false")
	f.SetNotReady()
	assert.True(t, f.SetNotReadyCalled, "SetNotReady should set SetNotReadyCalled to true")
}

func TestFakeHealthzServer_SetCustomReadinessCheck(t *testing.T) {
	f := &FakeHealthzServer{}
	assert.False(t, f.SetCustomCalled, "initially, SetCustomCalled should be false")
	assert.Nil(t, f.CustomFn, "initially, CustomFn should be nil")

	// Define a dummy custom function
	dummyErr := errors.New("dummy error")
	dummyFn := func() error { return dummyErr }

	f.SetCustomReadinessCheck(dummyFn)
	assert.True(t, f.SetCustomCalled, "SetCustomReadinessCheck should set SetCustomCalled to true")
	assert.NotNil(t, f.CustomFn, "CustomFn should be non-nil after setting")

	// Verify that invoking CustomFn returns the expected error
	err := f.CustomFn()
	assert.Equal(t, dummyErr, err, "CustomFn should return the dummy error")
}

#!/bin/sh

# This file should be run from the microservice directory (e.g., /app).
# It should be run as the startup/build script of a container (before
# running `go mod tidy && go mod download` for the microservice itself).
# The script does the following:
#   1. For each directory in /shared that contains a `go.mod` file, it will
#      add the appropriate replace directive into the microservice's `go.mod`
#      file (the directory from which the script is being executed).
#   2. For each directory in /shared that contains a `go.mod` file, it will
#      execute `go mod tidy && go mod download`, which will get the needed
#      dependencies for the shared libraries (which is not done automatically
#      when running those commands in the context of the microservice itself).

# Loop over all directories in /shared to add replace directives
for d in /shared/*; do
  if [ -z "$d" ]; then
    # echo "DEBUG: skipping empty value"
    continue
  fi
  # echo "DEBUG: Processing directory '$d'"
  if [ -d "$d" ] && [ -f "$d/go.mod" ]; then
    moduleName=$(grep '^module ' "$d/go.mod" | awk '{print $2}')
    # echo "DEBUG: Found $moduleName in $d"
    for t in /app /shared/*; do
      if [ -z "$t" ]; then
        # echo "DEBUG: skipping empty value"
        continue
      fi
      # echo "DEBUG: Processing directory '$t'"
      if [ -d "$t" ] && [ -f "$t/go.mod" ]; then
        # echo "DEBUG: Adding replace directive for $moduleName in $t"
        cd "$t" 
        GUARD="replace ${moduleName} => $d"
        if ! grep -qF "${GUARD}" go.mod; then 
          # echo "DEBUG: adding replace ${moduleName}=>${d}"
          go mod edit -replace=${moduleName}=$d
        fi
      else
        # echo "DEBUG: Skipping $t: go.mod not found"
        continue
      fi
    done
  else
    # echo "DEBUG: Skipping $d: go.mod not found"
    continue
  fi
done


# Add explicit replace directives for shared module dependencies.
cd /shared/schema_mgmt && go mod edit -replace="synapse-its.com/shared/connect"="/shared/connect"


# Loop over all directories in /shared to run go mod tidy and go mod download
for d in /shared/*; do
  if [ -d "$d" ] && [ -f "$d/go.mod" ]; then
    # echo "DEBUG: Running go mod tidy in $d"
    (cd "$d" && go mod tidy && go mod download)
  else
    # echo "DEBUG: Skipping $d: go.mod not found"
    continue
  fi
done

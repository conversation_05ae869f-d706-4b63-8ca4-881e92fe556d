// assets/envjs_handler_test.go
package assets

import (
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestEnvJsHandler(t *testing.T) {
	// Backup and restore the real envJs
	orig := envJs
	defer func() { envJs = orig }()

	// Stub in a known envJs value
	envJs = "http://keycloak:8080/foo/bar"

	cases := []struct {
		name       string
		hostHeader string
		wantBody   string
	}{
		{
			name:       "local dev browser → swap to localhost:8091",
			hostHeader: "localhost:4200",
			wantBody: strings.ReplaceAll(
				envJs,
				"http://keycloak:8080/",
				"http://localhost:8091/",
			),
		},
		{
			name:       "any other host → leave keycloak:8080",
			hostHeader: "example.com",
			wantBody: strings.ReplaceAll(
				envJs,
				"http://keycloak:8080/",
				"http://keycloak:8080/",
			),
		},
	}

	for _, tc := range cases {
		t.Run(tc.name, func(t *testing.T) {
			req := httptest.NewRequest("GET", "/env.js", nil)
			req.Host = tc.hostHeader
			rr := httptest.NewRecorder()

			EnvJsHandler(rr, req)

			assert.Equal(t, "application/javascript",
				rr.Header().Get("Content-Type"),
				"should set Content-Type header")

			assert.Equal(t, tc.wantBody,
				rr.Body.String(),
				"response body")
		})
	}
}

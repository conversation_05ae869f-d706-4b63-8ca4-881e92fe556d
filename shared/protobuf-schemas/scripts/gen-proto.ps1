<#
.SYNOPSIS
Optionally builds the proto-builder Docker image and generates Go protobuf code.
.DESCRIPTION
This script optionally rebuilds the Docker image tagged 'proto-builder' when invoked with
-BuildImage, then runs the container to execute 'scripts/generate.sh', mounting the current
repository root as /workspace. It overrides the container ENTRYPOINT to ensure the generator
script runs rather than protoc's default help.
.PARAMETER BuildImage
If specified, rebuilds the Docker image before regenerating code. If omitted, skips the build.
.EXAMPLE
# Only generate code (requires existing image)
PS> .\scripts\generate-proto.ps1

# Rebuild image then regenerate code
PS> .\scripts\generate-proto.ps1 -BuildImage
#>

Param (
    [switch]$BuildImage
)

# Fail fast on errors
Set-StrictMode -Version Latest
$ErrorActionPreference = 'Stop'

# Change to the script's directory (project root assumed one level up)
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Definition
Set-Location "$ScriptDir/.."

# Rebuild the Docker image if requested
if ($BuildImage) {
    Write-Host "Building proto-builder image..."
    docker build -t proto-builder .
} else {
    Write-Host "Skipping Docker image build. Use -BuildImage to force rebuild."
}

# Determine project root path for mounting
$ProjectRoot = (Get-Location).ProviderPath

Write-Host "Generating Go code from .proto files..."

# Run the generator inside the container, forcing a shell entrypoint
# Use /bin/sh for compatibility and absolute path for the script
docker run --rm `
    --entrypoint /bin/sh `
    -v "${ProjectRoot}:/workspace" `
    -w /workspace `
    proto-builder `
    -c "/workspace/scripts/generate.sh"

Write-Host "Protobuf Go code generation complete."

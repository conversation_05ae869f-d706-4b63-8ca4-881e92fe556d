#!/usr/bin/env bash
# Description: Reset settings to the default values
#   bqe reset

set -euo pipefail

# Determine the directory containing this script, then its parent
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BASE_DIR="$(dirname "$SCRIPT_DIR")"

# Paths to the default and active settings files
DEFAULT_SETTINGS="$BASE_DIR/.settings.default"
ACTIVE_SETTINGS="$BASE_DIR/.settings"

# Ensure the default settings file exists
if [[ ! -f "$DEFAULT_SETTINGS" ]]; then
  echo "Error: default settings file not found at $DEFAULT_SETTINGS" >&2
  exit 1
fi

# Replace the active settings with the default
cp "$DEFAULT_SETTINGS" "$ACTIVE_SETTINGS"

echo "Settings have been reset to default."

# bqbatch

## Purpose

This module provides the batch insert capability to BigQuery, for use by other
modules.

## Justification

This module solves the following problem: BigQuery writes need to be aggregated
into batches.  It may require processing multiple PubSub topics in order to
assemble a batch of reasonable size.  This module, then, provides a queue
that can be written to (each target table has its own queue), and the records
are aggregated and written as needed.

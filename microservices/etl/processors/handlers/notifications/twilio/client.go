package twilio

import (
	"context"
	"os"

	"github.com/twilio/twilio-go"
)

// Store the Twilio client in context
const twilioClientKey string = "twilio_client"

// NewClient reads TWILIO_API_KEY and TWILIO_API_SECRET from the environment
// and returns an initialized *twilio.RestClient, or an error if credentials are missing
func NewClient(ctx context.Context) (*twilio.RestClient, error) {
	// Get apiKey from env
	apiKey := os.Getenv("TWILIO_API_KEY")
	if apiKey == "" {
		return nil, ErrMissingAPIKey
	}

	// Get apiSecret from env
	apiSecret := os.Getenv("TWILIO_API_SECRET")
	if apiSecret == "" {
		return nil, ErrMissingAPISecret
	}

	// Create Twilio client with API key and secret
	client := twilio.NewRestClientWithParams(twilio.ClientParams{
		Username: apiKey,
		Password: apiSecret,
	})

	return client, nil
}

// WithClient stores the *twilio.RestClient in a context under a private key
func WithClient(ctx context.Context, client *twilio.RestClient) context.Context {
	return context.WithValue(ctx, twilioClientKey, client)
}

// FromContext retrieves the *twilio.RestClient from the context
func FromContext(ctx context.Context) *twilio.RestClient {
	if client, ok := ctx.Value(twilioClientKey).(*twilio.RestClient); ok {
		return client
	}
	return nil
}

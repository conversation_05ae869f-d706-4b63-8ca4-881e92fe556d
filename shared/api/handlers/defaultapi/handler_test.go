package defaultapi

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestHandler_CreatesSuccessResponse(t *testing.T) {
	req := httptest.NewRequest(http.MethodGet, "/", nil)
	rr := httptest.NewRecorder()

	// Invoke the handler
	<PERSON><PERSON>(rr, req)

	// 1) HTTP status
	assert.Equal(t, http.StatusOK, rr.Code, "should return 200 OK")

	// 2) Content-Type header
	assert.Equal(t, "application/json", rr.<PERSON>er().Get("Content-Type"), "should set JSON content type")

	// 3) Body is valid JSON
	var resp struct {
		Status  string      `json:"status"`
		Data    interface{} `json:"data"`
		Message string      `json:"message"`
		Code    int         `json:"code"`
	}
	err := json.Unmarshal(rr.Body.Bytes(), &resp)
	assert.NoError(t, err, "response body should be valid JSON")

	// 4) JSON payload fields
	assert.Equal(t, "success", resp.Status, "status field")
	assert.Equal(t, "", resp.Data, "data field")
	assert.Equal(t, "Request Succeeded", resp.Message, "message field")
	assert.Equal(t, http.StatusOK, resp.Code, "code field")
}

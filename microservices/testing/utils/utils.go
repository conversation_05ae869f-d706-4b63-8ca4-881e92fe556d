package utils

import (
	"context"
	"errors"
	"io"
	"net/http"
	"strings"
	"time"

	"cloud.google.com/go/bigquery"

	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/logger"
	"synapse-its.com/shared/schema_mgmt"
)

// AwaitBroker repeatedly checks the health endpoint until it reports healthy or the timeout is reached.
func AwaitBroker(ctx context.Context, interval time.Duration) error {
	// Utilize healthz service to determine if broker is ready
	url := "http://broker:8081/readyz"
	client := &http.Client{Timeout: 5 * time.Second}

	for {
		select {
		case <-ctx.Done():
			return errors.New("health check timed out")
		default:
			resp, err := client.Get(url)
			if err != nil {
				logger.Debugf("Error checking health endpoint: %v\n", err)
			} else {
				body, _ := io.ReadAll(resp.Body)
				resp.Body.Close()

				if resp.StatusCode == http.StatusOK {
					logger.Debugf("Service is healthy: %s\n", string(body))
					return nil
				}
				logger.Debugf("Service unhealthy (status: %d), retrying...\n", resp.StatusCode)
			}
			time.Sleep(interval)
		}
	}
}

// SetupBigquery() is a helper function to correctly configure the BigQuery
// client and apply schema migrations.
// It is modeled after the coordinator's setup function, but is simplified for
// testing purposes.
func SetupBigQuery(bq *connect.BigQueryExecutor, schemaName string, version string) error {
	// Create a dataset (if it doesn't exist).
	datasetID := bq.Config.DBName
	dataset := bq.Client.Dataset(datasetID)
	if err := dataset.Create(bq.Ctx, &bigquery.DatasetMetadata{Location: "us-central1"}); err != nil {
		if !strings.Contains(err.Error(), "Already Exists") && !strings.Contains(err.Error(), "already created") {
			return err
		}
	}

	return schema_mgmt.ApplyMigrations(&schema_mgmt.BigQueryMigrationExecutor{
		Client: bq,
	}, schemaName, version)
}

// SetupPostgres() is a helper function to correctly configure the Postgres
// client and apply schema migrations.
// It is modeled after the coordinator's setup function, but is simplified for
// testing purposes.
func SetupPostgres(pg *connect.PostgresExecutor, schemaName string, version string) error {
	return schema_mgmt.ApplyMigrations(&schema_mgmt.PostgresMigrationExecutor{
		Client: pg,
	}, schemaName, version)
}

package healthz

import "context"

// FakeHealthzServer implements healthz.HealthzServer for testing.
type FakeHealthzServer struct {
	ListenCalled      bool
	ShutdownCalled    bool
	SetBootCalled     bool
	SetReadyCalled    bool
	SetNotReadyCalled bool
	SetCustomCalled   bool

	CustomFn func() error

	ListenErr   error
	ShutdownErr error
}

func (f *FakeHealthzServer) ListenAndServe() error {
	f.ListenCalled = true
	return f.ListenErr
}

func (f *FakeHealthzServer) Shutdown(ctx context.Context) error {
	f.ShutdownCalled = true
	return f.ShutdownErr
}

func (f *FakeHealthzServer) SetBootComplete() {
	f.SetBootCalled = true
}

func (f *FakeHealthzServer) SetReady() {
	f.SetReadyCalled = true
}

func (f *FakeHealthzServer) SetNotReady() {
	f.SetNotReadyCalled = true
}

func (f *FakeHealthzServer) SetCustomReadinessCheck(fn func() error) {
	f.SetCustomCalled = true
	f.CustomFn = fn
}

package pubsub

import (
	"context"
	"sync"

	"synapse-its.com/shared/logger"

	"cloud.google.com/go/pubsub"
	"synapse-its.com/shared/connect"
)

// --------------------------------------------------
// FakePubsubClient
// --------------------------------------------------

type FakePubsubClient struct {
	mu            sync.Mutex
	topics        map[string]*FakePubsubTopic
	subscriptions map[string]*FakePubsubSubscription
	// Optional global receive error override
	ReceiveError error
}

// NewFakePubsubClient builds an empty in‐memory Pub/Sub emulator.
func NewFakePubsubClient() *FakePubsubClient {
	return &FakePubsubClient{
		topics:        make(map[string]*FakePubsubTopic),
		subscriptions: make(map[string]*FakePubsubSubscription),
	}
}

// WrapClient for your PsClient interface
func (fc *FakePubsubClient) Topic(name string) connect.PsTopic {
	if fc == nil {
		return nil
	}
	fc.mu.Lock()
	defer fc.mu.Unlock()
	t, ok := fc.topics[name]
	if !ok {
		if fc.topics == nil {
			logger.Errorf("fc.topics is nil")
		}
		t = &FakePubsubTopic{name: name}
		fc.topics[name] = t
	}
	return t
}

// CreateTopic simulates creating a topic.
func (fc *FakePubsubClient) CreateTopic(ctx context.Context, name string) (connect.PsTopic, error) {
	return fc.Topic(name), nil
}

// Subscription returns or creates a subscription handle.
func (fc *FakePubsubClient) Subscription(name string) connect.PsSubscription {
	fc.mu.Lock()
	defer fc.mu.Unlock()
	s, ok := fc.subscriptions[name]
	if !ok {
		// by default attach to a zero‐message subscription
		s = &FakePubsubSubscription{client: fc, name: name}
		fc.subscriptions[name] = s
	}
	return s
}

// CreateSubscription simulates binding a subscription to a topic.
func (fc *FakePubsubClient) CreateSubscription(ctx context.Context, name string, cfg connect.SubscriptionConfig) (connect.PsSubscription, error) {
	fc.mu.Lock()
	defer fc.mu.Unlock()
	realTopic := cfg.Topic.(*FakePubsubTopic)
	sub := &FakePubsubSubscription{
		name:   name,
		topic:  realTopic,
		client: fc,
	}
	fc.subscriptions[name] = sub
	return sub, nil
}

func (fc *FakePubsubClient) Close() error { return nil }

// --------------------------------------------------
// FakePubsubtopic
// --------------------------------------------------

type FakePubsubTopic struct {
	mu       sync.Mutex
	name     string
	messages []*pubsub.Message
}

func (ft *FakePubsubTopic) GetMessages() []*pubsub.Message {
	ft.mu.Lock()
	defer ft.mu.Unlock()
	return append([]*pubsub.Message(nil), ft.messages...)
}

func (ft *FakePubsubTopic) Publish(ctx context.Context, m *pubsub.Message) connect.PsPublishResult {
	ft.mu.Lock()
	ft.messages = append(ft.messages, m)
	ft.mu.Unlock()
	return &FakePublishResult{messageID: "fake-id"}
}

func (ft *FakePubsubTopic) Exists(ctx context.Context) (bool, error) {
	return true, nil
}

// --------------------------------------------------
// FakePublishResult
// --------------------------------------------------

type FakePublishResult struct {
	messageID string
	Err       error
}

func (r *FakePublishResult) Get(ctx context.Context) (string, error) {
	if r.Err != nil {
		return "", r.Err
	}
	return r.messageID, nil
}

// --------------------------------------------------
// FakePubsubSubscription
// --------------------------------------------------

type FakePubsubSubscription struct {
	mu       sync.Mutex
	name     string
	topic    *FakePubsubTopic
	messages []*pubsub.Message
	client   *FakePubsubClient
}

func (fs *FakePubsubSubscription) Receive(ctx context.Context, f func(context.Context, *pubsub.Message)) error {
	if fs.client != nil && fs.client.ReceiveError != nil {
		return fs.client.ReceiveError
	}
	// deliver all buffered messages
	fs.mu.Lock()
	msgs := append([]*pubsub.Message(nil), fs.topic.messages...)
	fs.mu.Unlock()

	for _, m := range msgs {
		f(ctx, m)
	}
	return nil
}

func (fs *FakePubsubSubscription) Exists(ctx context.Context) (bool, error) {
	// in tests you can toggle this or return fs.name == "someSpecialSub" etc.
	return true, nil
}

func (fs *FakePubsubSubscription) Close() error {
	return nil
}

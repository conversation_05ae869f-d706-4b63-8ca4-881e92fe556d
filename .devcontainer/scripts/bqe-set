#!/usr/bin/env bash
# Description: Set a configuration variable in .settings
#   bqe set <key> <value>

set -euo pipefail

# find this script’s directory, then go up one for the .settings file
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SETTINGS_FILE="$SCRIPT_DIR/../.settings"

# ensure the file exists
mkdir -p "$(dirname "$SETTINGS_FILE")"
touch "$SETTINGS_FILE"

# must have exactly two args: key and value
if [[ $# -ne 2 ]]; then
  echo "Usage: bqe set <key> <value>" >&2
  exit 1
fi

key=$1
value=$2

# update if present, otherwise append
if grep -q "^$key=" "$SETTINGS_FILE"; then
  sed -i "s|^$key=.*|$key=$value|" "$SETTINGS_FILE"
else
  echo "$key=$value" >> "$SETTINGS_FILE"
fi

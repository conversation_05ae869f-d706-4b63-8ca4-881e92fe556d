package device

import (
	"fmt"
	"strconv"
	"strings"
	"time"

	"synapse-its.com/shared/devices/edi/helper"
)

// Convert a slice of pgDeviceInfo to a slice of dataPayload.
func convertPgDeviceInfos(dbInfo *[]pgDeviceInfo) *[]dataPayload {
	// If the input pointer is nil, return nil
	if dbInfo == nil {
		return nil
	}
	infos := *dbInfo
	out := make([]dataPayload, 0, len(infos))
	for _, d := range infos {
		dp := dataPayload{
			DeviceID:         d.Id,
			DeviceIdentifier: d.DeviceIdentifier,
			Location: location{
				Latitude:  d.Latitude,
				Longitude: d.Longitude,
			},
			Status: deviceStatus{
				State:                "", // not available
				HeartbeatReceivedUTC: "", // not available
				LogUploadedUTC:       d.DateUploadedUTC.Format(time.RFC3339Nano),
				LastFaultReason:      d.FaultStatus,
				LastFaultUploadedUTC: d.MonitorTime.Format(time.RFC3339Nano),
				FaultedChannelStatus: channelStatus{
					ChannelRed:    parsePgBoolArray(d.ChannelRedStatus),
					ChannelYellow: parsePgBoolArray(d.ChannelYellowStatus),
					ChannelGreen:  parsePgBoolArray(d.ChannelGreenStatus),
				},
			},
			Metadata: deviceMetadata{
				Manufacturer:            "", // not available
				Model:                   "", // not available
				UserAssignedDeviceID:    fmt.Sprint(d.MonitorId),
				UserAssignedDeviceName:  d.MonitorName,
				ApplicationVersion:      "", // not available
				FirmwareType:            "", // not available
				FirmwareVersion:         "", // not available
				CommVersion:             "", // not available
				RmsEngineFirmwareType:   strconv.Itoa(d.EngineRevision),
				RmsEngineFirmwareVerson: strconv.Itoa(d.EngineVersion),
				IPAddress:               d.IPAddress,
				IPort:                   d.Port,
			},
		}
		out = append(out, dp)
	}
	return &out
}

func addRedisToPayload(dp *dataPayload, header *helper.HeaderRecord, status *helper.RmsStatusRecord) {
	dp.Metadata.Model = fmt.Sprint(header.Model)
	dp.Metadata.FirmwareType = strconv.FormatInt(header.FirmwareRevision, 10)
	dp.Metadata.FirmwareVersion = strconv.FormatInt(header.FirmwareVersion, 10)
	dp.Metadata.CommVersion = strconv.FormatInt(header.CommVersion, 10)

	dp.Status.HeartbeatReceivedUTC = status.MonitorTime.UTC().Format(time.RFC3339Nano)
	dp.Status.State = status.Fault // Current state
}

func parsePgBoolArray(raw []uint8) []bool {
	// raw is something like ['{','}'] or ['{','t',',','f','}']
	s := strings.Trim(string(raw), "{}")
	if s == "" {
		return []bool{}
	}
	parts := strings.Split(s, ",")
	out := make([]bool, len(parts))
	for i, p := range parts {
		switch p {
		case "t", "true", "T", "TRUE":
			out[i] = true
		case "f", "false", "F", "FALSE":
			out[i] = false
		default:
			return nil
		}
	}
	return out
}

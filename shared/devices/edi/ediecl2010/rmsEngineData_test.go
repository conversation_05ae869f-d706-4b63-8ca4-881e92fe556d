package ediecl2010

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/pubsubdata"
)

// buildRMSEngineMsg creates a message with the specified version and revision
func buildRMSEngineMsg(version, revision byte) []byte {
	msg := make([]byte, RMSEngineRecordCount*RMSEngineDataLength)

	// Set version record header
	msg[0] = 0x01    // Message type
	msg[1] = 0x02    // Device address
	msg[2] = 0x03    // Command code
	msg[3] = 0x00    // Response status
	msg[4] = 0x00    // Reserved
	msg[5] = 0x00    // Reserved
	msg[6] = 0x00    // Reserved
	msg[7] = 0x00    // Reserved
	msg[8] = version // Version
	// Calculate version record checksum
	var sum byte
	for i := 0; i < RMSEngineDataLength-1; i++ {
		sum += msg[i]
	}
	msg[RMSEngineDataLength-1] = ^sum

	// Set revision record header (same as version)
	copy(msg[RMSEngineRevisionRecordStartOffset:RMSEngineRevisionRecordStartOffset+HeaderLength],
		msg[RMSEngineVersionRecordStartOffset:RMSEngineVersionRecordStartOffset+HeaderLength])
	msg[RMSEngineRevisionOffset] = revision // Revision
	// Calculate revision record checksum
	sum = 0
	for i := RMSEngineRevisionRecordStartOffset; i < RMSEngineRevisionRecordStartOffset+RMSEngineDataLength-1; i++ {
		sum += msg[i]
	}
	msg[RMSEngineRevisionRecordStartOffset+RMSEngineDataLength-1] = ^sum

	return msg
}

func TestRMSEngineData(t *testing.T) {
	tests := []struct {
		name           string
		byteMsg        []byte
		httpHeader     *pubsubdata.HeaderDetails
		header         *helper.HeaderRecord
		expectedError  error
		validateRecord func(t *testing.T, rmsEngine *helper.RmsEngineRecord)
	}{
		{
			name: "invalid message length",
			byteMsg: func() []byte {
				// Create a message that's too short
				msg := make([]byte, RMSEngineRecordCount*RMSEngineDataLength-1)
				return msg
			}(),
			httpHeader: &pubsubdata.HeaderDetails{
				GatewayTimezone: "UTC",
			},
			header: &helper.HeaderRecord{
				Model:       helper.Ecl2010,
				CommVersion: 0x27,
			},
			expectedError: helper.ErrMsgByteLen,
		},
		{
			name: "invalid checksum in version record",
			byteMsg: func() []byte {
				msg := buildRMSEngineMsg(0x01, 0x02)
				msg[RMSEngineDataLength-1] = ^msg[RMSEngineDataLength-1] // Flip bits of checksum will always cause an invalid checksum
				return msg
			}(),
			httpHeader: &pubsubdata.HeaderDetails{
				GatewayTimezone: "UTC",
			},
			header: &helper.HeaderRecord{
				Model:       helper.Ecl2010,
				CommVersion: 0x27,
			},
			expectedError: helper.ErrMsgByteChecksum,
		},
		{
			name: "invalid checksum in revision record",
			byteMsg: func() []byte {
				msg := buildRMSEngineMsg(0x01, 0x02)
				msg[RMSEngineRevisionRecordStartOffset+RMSEngineDataLength-1] = ^msg[RMSEngineRevisionRecordStartOffset+RMSEngineDataLength-1] // Flip bits of checksum will always cause an invalid checksum
				return msg
			}(),
			httpHeader: &pubsubdata.HeaderDetails{
				GatewayTimezone: "UTC",
			},
			header: &helper.HeaderRecord{
				Model:       helper.Ecl2010,
				CommVersion: 0x27,
			},
			expectedError: helper.ErrMsgByteChecksum,
		},
		{
			name: "mismatched headers",
			byteMsg: func() []byte {
				msg := buildRMSEngineMsg(0x01, 0x02)
				// Modify revision record header to be different
				msg[RMSEngineRevisionRecordStartOffset] = 0x02 // Different message type
				// Recalculate revision record checksum
				var sum byte
				for i := RMSEngineRevisionRecordStartOffset; i < RMSEngineRevisionRecordStartOffset+RMSEngineDataLength-1; i++ {
					sum += msg[i]
				}
				msg[RMSEngineRevisionRecordStartOffset+RMSEngineDataLength-1] = ^sum
				return msg
			}(),
			httpHeader: &pubsubdata.HeaderDetails{
				GatewayTimezone: "UTC",
			},
			header: &helper.HeaderRecord{
				Model:       helper.Ecl2010,
				CommVersion: 0x27,
			},
			expectedError: helper.ErrRMSEngineDataHeaderMismatch,
		},
		{
			name:    "valid message",
			byteMsg: buildRMSEngineMsg(0x01, 0x02),
			httpHeader: &pubsubdata.HeaderDetails{
				GatewayTimezone: "UTC",
			},
			header: &helper.HeaderRecord{
				Model:       helper.Ecl2010,
				CommVersion: 0x27,
			},
			expectedError: nil,
			validateRecord: func(t *testing.T, rmsEngine *helper.RmsEngineRecord) {
				assert.NotNil(t, rmsEngine)
				assert.Equal(t, DeviceModel, rmsEngine.DeviceModel)
				assert.Equal(t, int64(1), rmsEngine.EngineVersion)
				assert.Equal(t, int64(2), rmsEngine.EngineRevision)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			device := EDIECL2010{}
			rmsEngine, err := device.RMSEngineData(tt.httpHeader, tt.byteMsg, tt.header)

			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.ErrorIs(t, err, tt.expectedError)
				assert.Nil(t, rmsEngine)
			} else {
				assert.NoError(t, err)
				if tt.validateRecord != nil {
					tt.validateRecord(t, rmsEngine)
				}
			}
		})
	}
}

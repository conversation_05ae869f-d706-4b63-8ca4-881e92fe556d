package organization

import "time"

// Structure of Organization
type Organization struct {
	Id                     int        `json:"-" db:"id"`
	OrganizationIdentifier string     `json:"organizationidentifier" db:"organizationidentifier"`
	APIKey                 string     `json:"apikey" db:"apikey"`
	Description            string     `json:"description" db:"description"`
	CreatedAt              time.Time  `json:"createdat" db:"createdat"`
	UpdatedAt              time.Time  `json:"updatedat" db:"updatedat"`
	DeletedAt              *time.Time `json:"-" db:"deletedat"`
	IsDeleted              bool       `json:"-" db:"isdeleted"`
}

// The request body for creating a new organization
type CreateAnhUpdateOrganizationRequest struct {
	Description string `json:"description" validate:"required,min=1"`
}

// The data model for the API response
type OrganizationResponse struct {
	OrganizationIdentifier string    `json:"organizationidentifier"`
	APIKey                 string    `json:"apikey"`
	Description            string    `json:"description"`
	CreatedAt              time.Time `json:"createdat"`
	UpdatedAt              time.Time `json:"updatedat"`
}

// Converts an Organization model to an OrganizationResponse
func (o *Organization) ToResponse() OrganizationResponse {
	return OrganizationResponse{
		OrganizationIdentifier: o.OrganizationIdentifier,
		Description:            o.Description,
		APIKey:                 o.APIKey,
		CreatedAt:              o.CreatedAt,
		UpdatedAt:              o.UpdatedAt,
	}
}

# syntax=docker/dockerfile:1.4


# ┌─────────────────────────────────────────────────────┐
# │  Dockerfile.dev (for development: bind-mount build) │
# └─────────────────────────────────────────────────────┘

FROM ubuntu:24.04 AS wsl

# 1) Install runtime dependencies for bigquery-emulator (e.g. clang, system libs, etc.)
RUN apt-get update && apt-get install -y \
      ca-certificates \
      libssl-dev \
      clang \
      gzip \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Entrypoint: unzip and run the binary that's bind-mounted into /app
ENTRYPOINT ["./unzipAndRun.sh"]




#################################################
# Stage A: build go-zetasql into an archive (.a)
#################################################
FROM golang:1.24-bookworm AS zetasql-builder

RUN apt-get update \
 && apt-get install -y --no-install-recommends clang ccache

# CGO for ZetaSQL + set up caches
ENV CGO_ENABLED=1 \
    CXX=clang++ \
    GOCACHE=/root/.cache/go-build \
    CCACHE_DIR=/root/.ccache

WORKDIR /go-zetasql

# 1) Copy in only the module files, so Docker can cache 'go mod tidy'
COPY go-zetasql/go.mod go-zetasql/go.sum ./

# Mount your named volumes for module & build cache
RUN --mount=type=cache,target=/go/pkg/mod \
    --mount=type=cache,target=/root/.cache/go-build \
    go mod tidy

# 2) Copy the rest of the code and compile
COPY go-zetasql ./
RUN --mount=type=cache,target=/go/pkg/mod \
    --mount=type=cache,target=/root/.cache/go-build \
    --mount=type=cache,target=/root/.ccache \
    go install -buildmode=archive .

#####################################################
# Stage B: build go-zetasqlite into its own archive
#####################################################
FROM golang:1.24-bookworm AS zetasqlite-builder

RUN apt-get update \
 && apt-get install -y --no-install-recommends clang ccache

ENV CGO_ENABLED=1 \
    CXX=clang++ \
    GOCACHE=/root/.cache/go-build \
    CCACHE_DIR=/root/.ccache

WORKDIR /go-zetasqlite

COPY go-zetasqlite/go.mod go-zetasqlite/go.sum ./
RUN --mount=type=cache,target=/go/pkg/mod \
    --mount=type=cache,target=/root/.cache/go-build \
    go mod tidy

COPY go-zetasqlite ./
RUN --mount=type=cache,target=/go/pkg/mod \
    --mount=type=cache,target=/root/.cache/go-build \
    --mount=type=cache,target=/root/.ccache \
    go install -buildmode=archive .

#################################################
# Stage C: build the emulator, linking those .a’s
#################################################
FROM golang:1.24-bookworm AS emulator

RUN apt-get update && apt-get install -y clang

ENV CGO_ENABLED=1 \
    CXX=clang++

WORKDIR /work
COPY bigquery-emulator/go.mod bigquery-emulator/go.sum ./
RUN --mount=type=cache,target=/go/pkg/mod \
    go mod tidy

COPY --from=zetasql-builder    /go/pkg /go/pkg
COPY --from=zetasqlite-builder /go/pkg /go/pkg

COPY go-zetasql /go-zetasql
COPY go-zetasqlite /go-zetasqlite

COPY bigquery-emulator ./
# Tell Go to pull in our prebuilt modules
RUN go mod edit \
      -replace github.com/goccy/go-zetasql=/go-zetasql \
      -replace github.com/goccy/go-zetasqlite=/go-zetasqlite && \
    go install -buildmode=exe ./cmd/bigquery-emulator

#################################################
# Final runtime image
#################################################
FROM debian:bookworm-slim AS hacked

COPY --from=emulator /go/bin/bigquery-emulator /usr/local/bin/
ENTRYPOINT ["bigquery-emulator"]



# This version was used when trying to hack the emulator so that DBeaver could
# connect to it.  It pulls the latest version of the emulator and applies a
# patch to it that resolves a merge conflict in the PR from GitHub.  It is not
# currently functional, but the patch mechanism works and so is being left here
# so that this can be revisited later.
# To be clear, this image does not use the subtree version that appears in the
# repo.

FROM ghcr.io/goccy/go-zetasql:latest as patched-emulator-not-working

# Optionally specify a branch or commit via the VERSION build arg (defaults to master)
ARG VERSION=main

WORKDIR /work

# Install git, curl, and patch so that we can clone the repository and apply patches
RUN apt-get update && apt-get install -y git curl && rm -rf /var/lib/apt/lists/*

# Clone the bigquery-emulator repository and check out the specified version
RUN git clone https://github.com/goccy/bigquery-emulator.git . \
  && git checkout 4676a5ce404fe22a690ccbdf5b10bfaf5cdc5158

# Patch it using a conflict-resolved version of
# https://github.com/goccy/bigquery-emulator/pull/278
COPY pr278.updated.patch .
RUN git apply ./pr278.updated.patch

# Replace dependency as needed and download modules
RUN go mod edit -replace github.com/goccy/go-zetasql=../go-zetasql
RUN go mod download

# Build the patched emulator
RUN make emulator/build

FROM debian:bullseye AS emulator-not-working

# Copy the built emulator binary from the builder stage
COPY --from=0 /work/bigquery-emulator /bin/bigquery-emulator
COPY --from=0 /work/ssl/* /ssl

WORKDIR /work

ENTRYPOINT ["/bin/bigquery-emulator"]

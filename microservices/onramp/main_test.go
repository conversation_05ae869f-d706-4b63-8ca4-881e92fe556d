package main

import (
	"bytes"
	"context"
	"errors"
	"net/http"
	"os"
	"reflect"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"

	"synapse-its.com/shared/bqbatch"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/healthz"
	"synapse-its.com/shared/logger"
	"synapse-its.com/shared/mocks"
)

// fakeServer implements our Server interface for testing.
type fakeServer struct {
	listenCalled   bool
	shutdownCalled bool
	serveErr       error
	downErr        error
}

func (f *fakeServer) ListenAndServe() error {
	f.listenCalled = true
	return f.serveErr
}

func (f *fakeServer) Shutdown(_ context.Context) error {
	f.shutdownCalled = true
	return f.downErr
}

func TestRun(t *testing.T) {
	// Backup globals
	origLogger := logger.Logger
	origHealthzNewServer := healthzNewServer
	origTimeSleep := timeSleep
	origLoggerFatalf := loggerFatalf
	defer func() {
		logger.Logger = origLogger
		healthzNewServer = origHealthzNewServer
		timeSleep = origTimeSleep
		loggerFatalf = origLoggerFatalf
	}()

	tests := []struct {
		name               string
		ctx                context.Context
		signalDelay        time.Duration
		addr               string
		batchErr           error
		healthzListenErr   error
		healthzShutdownErr error
		serveErr           error
		downErr            error
		expectBatchLogErr  bool
		expectHzLogErr     bool
		expectServeLogErr  bool
		expectDownLogErr   bool
	}{
		{
			name:        "graceful shutdown via signal",
			ctx:         context.Background(),
			signalDelay: 10 * time.Millisecond,
			addr:        ":8080",
		},
		{
			name:        "immediate context cancel",
			ctx:         func() context.Context { c, cancel := context.WithCancel(context.Background()); cancel(); return c }(),
			signalDelay: 0,
			addr:        ":9090",
		},
		{
			name:              "batcher shutdown error",
			ctx:               context.Background(),
			signalDelay:       10 * time.Millisecond,
			addr:              ":7070",
			batchErr:          errors.New("batch fail"),
			expectBatchLogErr: true,
		},
		{
			name:             "healthz listen error",
			ctx:              context.Background(),
			signalDelay:      10 * time.Millisecond,
			addr:             ":6061",
			healthzListenErr: errors.New("hz listen fail"),
			expectHzLogErr:   true,
		},
		{
			name:               "healthz shutdown error",
			ctx:                context.Background(),
			signalDelay:        10 * time.Millisecond,
			addr:               ":6060",
			healthzShutdownErr: errors.New("hz shutdown fail"),
			expectHzLogErr:     false, // No longer error on shutdown of hz
		},
		{
			name:              "server serve error",
			ctx:               context.Background(),
			signalDelay:       100 * time.Millisecond,
			addr:              ":5050",
			serveErr:          errors.New("serve fail"),
			expectServeLogErr: true,
		},
		{
			name:             "server shutdown error",
			ctx:              context.Background(),
			signalDelay:      10 * time.Millisecond,
			addr:             ":4040",
			downErr:          errors.New("down fail"),
			expectDownLogErr: true,
		},
	}

	for _, tc := range tests {
		tc := tc // capture range variable
		t.Run(tc.name, func(t *testing.T) {
			// buffer logger if errors expected
			var buf bytes.Buffer
			if tc.expectBatchLogErr || tc.expectHzLogErr || tc.expectServeLogErr || tc.expectDownLogErr {
				// redirect Fatalf to Errorf
				loggerFatalf = logger.Errorf
				encCfg := zap.NewDevelopmentEncoderConfig()
				encCfg.EncodeTime = zapcore.EpochTimeEncoder
				core := zapcore.NewCore(zapcore.NewConsoleEncoder(encCfg), zapcore.AddSync(&buf), zap.DebugLevel)
				logger.Logger = zap.New(core)
			}

			// override healthzShutdown
			fakeHZ := &mocks.FakeHealthzServer{
				ListenErr:   tc.healthzListenErr,
				ShutdownErr: tc.healthzShutdownErr,
			}
			healthzNewServer = func(port string) healthz.HealthzServer {
				return fakeHZ
			}

			// no actual sleep
			timeSleep = func(d time.Duration) {}

			// fake signal channel
			sigCh := make(chan os.Signal, 1)
			if tc.signalDelay > 0 {
				go func() {
					time.Sleep(tc.signalDelay)
					sigCh <- os.Interrupt
				}()
			}
			newSignals := func() <-chan os.Signal { return sigCh }

			// fake connections
			newConns := func(ctx context.Context) *connect.Connections {
				return mocks.FakeConns()
			}

			// fake batcher
			newBatch := func(_ connect.BigQueryExecutorInterface, _ connect.PsClient) bqbatch.Batcher {
				return mocks.FakeBatcherWithOptions(mocks.WithBatchShutdownError(tc.batchErr))
			}

			// fake server
			fsrv := &fakeServer{serveErr: tc.serveErr, downErr: tc.downErr}
			newServer := func(addr string, h http.Handler) Server {
				assert.Equal(t, tc.addr, addr)
				return fsrv
			}

			// call Run
			err := Run(tc.ctx, "", tc.addr, newConns, newBatch, newServer, newSignals)
			require.NoError(t, err)

			assert.True(t, fakeHZ.ListenCalled, "Healthz ListenAndServe should have been called")
			assert.True(t, fakeHZ.SetBootCalled, "Healthz SetBootComplete should have been called")
			assert.True(t, fakeHZ.SetReadyCalled, "Healthz SetReady should have been called")
			assert.True(t, fakeHZ.SetNotReadyCalled, "Healthz SetNotReady should have been called")
			assert.True(t, fakeHZ.ShutdownCalled, "Healthz Shutdown should have been called")
			// Not applicable... yet...
			// assert.True(t, fakeHZ.SetCustomCalled, "Healthz SetCustomReadinessCheck should have been called")

			// verify server calls
			assert.True(t, fsrv.listenCalled, "ListenAndServe should be called")
			assert.True(t, fsrv.shutdownCalled, "Shutdown should be called")

			// check logs
			out := buf.String()
			if tc.expectBatchLogErr {
				assert.Contains(t, out, "batch shutdown error: batch fail")
			}
			if tc.expectHzLogErr {
				assert.Contains(t, out, "healthz listen error: hz listen fail")
			}
			if tc.expectServeLogErr {
				assert.Contains(t, out, "HTTP server error: serve fail")
			}
			if tc.expectDownLogErr {
				assert.Contains(t, out, "Error shutting down HTTP server: down fail")
			}
		})
	}
}

func TestDefaultSignalChan(t *testing.T) {
	ch := DefaultSignalChan()
	require.NotNil(t, ch)
	v := reflect.ValueOf(ch)
	assert.Equal(t, reflect.Chan, v.Kind())
	assert.Equal(t, 1, v.Cap())
}

func TestDefaultServer(t *testing.T) {
	mux := http.NewServeMux()
	addr := ":4242"
	srv := DefaultServer(addr, mux)
	// underlying type
	hs, ok := srv.(*http.Server)
	require.True(t, ok)
	assert.Equal(t, addr, hs.Addr)
	assert.Equal(t, mux, hs.Handler)
}

package security

import (
	"encoding/base64"
	"os"
)

type jwtAsymmetricSecret struct {
	PrivateKey string `json:"private_key_base64_encoded"`
	PublicKey  string `json:"public_key_base64_encoded"`
}

var GetJWTAsymmetric = func() (privateKey string, publicKey string, err error) {
	privKey, err := base64.StdEncoding.DecodeString(os.Getenv("JWT_PRIVATE_KEY"))
	if err != nil {
		return "", "", err
	}

	pubKey, err := base64.StdEncoding.DecodeString(os.Getenv("JWT_PUBLIC_KEY"))
	if err != nil {
		return "", "", err
	}
	
	return string(privKey), string(pubKey), err
}

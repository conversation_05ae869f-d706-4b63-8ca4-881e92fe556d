<div class="oem-container">
  <div class="title-oem">
    <h1>OEM Onboard - Organizations</h1>
  </div>
  <div class="form-header">
    <div class="form-btn">
      <button nz-button nzType="primary" class="add-btn" (click)="openAddPopup()">Add New Row</button>
    </div>
    <div class="form-search">
      <nz-input-group nzSearch (nzOnSearch)="searchTable()" [nzAddOnAfter]="suffixIconButton">
        <input [(ngModel)]="searchTerm" (input)="searchTable()" (nzOnSearch)="searchTable()" type="text" nz-input
          placeholder="Filter Name" />
      </nz-input-group>
      <ng-template #suffixIconButton>
        <button nz-button nzType="primary" nzSearch><nz-icon nzType="search" /></button>
      </ng-template>
    </div>
  </div>
  <nz-table #basicTable nzBordered [nzData]="filteredList" nzShowSizeChanger nzShowPagination>
    <thead>
      <tr>
        <th nzWidth="25%">Name</th>
        <th nzWidth="25%">Identifier</th>
        <th nzWidth="25%">API Key</th>
        <th nzWidth="25%">Action</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let data of basicTable.data; let i = index">
        <td>{{data.name}}</td>
        <td>{{data.orgIden}}</td>
        <td>{{data.apiKey}}</td>
        <td class="btn-action">
          <button nz-button nzType="default" (click)="onClickSoftware()" class="btn-soft">Software Gateway</button>
          <button nz-button nzType="default" (click)="onClickUsres()" class="btn-soft">Users</button>
          <button nz-button nzType="primary" (click)="openEditPopup(i)">Edit</button>
        </td>
      </tr>
    </tbody>
  </nz-table>
  <app-edit-add [isVisible]="isModalVisible" [isEditMode]="isEditMode" [data]="currentOEM" (close)="closeModal()"
    (confirm)="handleModalSave($event)"></app-edit-add>
</div>
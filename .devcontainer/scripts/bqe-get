#!/usr/bin/env bash
# Description: Get a configuration variable from .settings
#   bqe get <key>

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SETTINGS_FILE="$SCRIPT_DIR/../.settings"

# Ensure settings file exists (initialize via bqe reset if missing)
if [[ ! -f "$SETTINGS_FILE" ]]; then
  # quietly reset to defaults
  bqe-reset > /dev/null 2>&1
fi

# Ensure exactly one argument is provided
if [[ $# -ne 1 ]]; then
  echo "Usage: bqe get <key>" >&2
  exit 1
fi

key=$1

# Print the value if present
grep -m1 "^$key=" "$SETTINGS_FILE" 2>/dev/null \
  | cut -d= -f2-

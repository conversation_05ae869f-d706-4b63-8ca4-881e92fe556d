package schema_mgmt

// This file contains mocks for the schema_mgmt unit tests.

import (
	"os"
)

// mockDirEntry implements os.DirEntry for tests.
// Name() returns the provided name, and IsDir() always returns true.
type mockDirEntry struct {
	name string
}

func (m mockDirEntry) Name() string               { return m.name }
func (m mockDirEntry) IsDir() bool                { return true }
func (m mockDirEntry) Type() os.FileMode          { return os.ModeDir }
func (m mockDirEntry) Info() (os.FileInfo, error) { return nil, nil }

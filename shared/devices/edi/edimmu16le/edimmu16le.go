package edimmu16le

import (
	"bytes"
	"fmt"
	"math"
	"strconv"

	"synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/pubsubdata"
)

type EDIMMU216LE struct{}

const deviceModel = "EDIMMU216LE"

func (device EDIMMU216LE) LogMonitorReset(httpHeader *pubsubdata.HeaderDetails, byteMsg []byte, header *helper.HeaderRecord) (allRecords *helper.LogMonitorResetRecords, err error) {
	const HeaderLength = 7
	const RecordSize = 7

	if len(byteMsg) < 7 {
		return nil, fmt.Errorf("%w byte length less then 7", helper.ErrMsgByteLen)
	}
	numberOfRecords := int(byteMsg[HeaderLength])
	startingByte := (HeaderLength + 1)
	length := HeaderLength + (numberOfRecords * RecordSize) + 2
	allRecords = new(helper.LogMonitorResetRecords)

	// Verify byte length
	if !helper.VerifyByteLen(byteMsg, length) {
		return nil, fmt.Errorf("%w %v should be %v", helper.ErrMsgByteLen, len(byteMsg), length)
	}

	// Validate the checksum of version
	err = helper.ValidateChecksum(byteMsg)
	if err != nil {
		return nil, fmt.Errorf("%w : %v", helper.ErrMsgByteChecksum, err)
	}

	allRecords.DeviceModel = deviceModel
	allRecords.RawMessage = byteMsg
	for i := 0; i < numberOfRecords; i++ {
		var record helper.LogMonitorResetRecord

		// pick out the raw byte
		resetTypeFromResponse := byteMsg[startingByte+6]

		// build a Go string and take its address
		var resetTypeText string
		switch resetTypeFromResponse {
		case 1:
			resetTypeText = "MONITOR MANUAL RESET EVENT #"
		case 2:
			resetTypeText = "MONITOR NON-LATCHED FAULT RESET EVENT #"
		case 4:
			resetTypeText = "MONITOR EXTERNAL RESET EVENT #"
		case 8:
			resetTypeText = "MONITOR REMOTE RESET EVENT #"
		default:
			resetTypeText = "MONITOR RESET EVENT #"
		}
		record.ResetType = resetTypeText

		// call your BCD->time.Time converter directly
		record.DateTime, err = helper.ConvertBCDBytesToDateTimeII(
			byteMsg[startingByte+4],
			byteMsg[startingByte+3],
			byteMsg[startingByte+5],
			byteMsg[startingByte+2],
			byteMsg[startingByte+1],
			byteMsg[startingByte+0],
			httpHeader.GatewayTimezone,
		)
		if err != nil {
			return nil, err
		}

		allRecords.Records = append(allRecords.Records, record)
		startingByte += RecordSize
	}

	return allRecords, nil
}

func (device EDIMMU216LE) LogPreviousFail(httpHeader *pubsubdata.HeaderDetails, byteMsg []byte, header *helper.HeaderRecord) (allRecords *helper.LogPreviousFailRecords, err error) {
	const HeaderLength = 7
	const RecordSize = 98

	// Rountines.bas - DisplayPFMMULog
	if len(byteMsg) < 7 {
		return nil, fmt.Errorf("%w byte length less then 7", helper.ErrMsgByteLen)
	}
	numberOfRecords := int(byteMsg[HeaderLength])
	startingByte := (HeaderLength + 1)
	length := HeaderLength + (numberOfRecords * RecordSize) + 2
	allRecords = new(helper.LogPreviousFailRecords)

	// Verify byte length
	if !helper.VerifyByteLen(byteMsg, length) {
		return nil, fmt.Errorf("%w %v should be %v", helper.ErrMsgByteLen, len(byteMsg), length)
	}

	// Validate the checksum of version
	err = helper.ValidateChecksum(byteMsg)
	if err != nil {
		return nil, fmt.Errorf("%w : %v", helper.ErrMsgByteChecksum, err)
	}
	allRecords.DeviceModel = deviceModel
	allRecords.RawMessage = byteMsg
	for loop := 0; loop < numberOfRecords; loop++ {
		var record helper.LogPreviousFailRecord

		fault := int(byteMsg[startingByte+0])

		faultStatus := int(byteMsg[startingByte+2])
		faultStatus = faultStatus*256 + int(byteMsg[startingByte+1])

		faultMsg, faultStatusFlag := GetMMUFaultText(fault, faultStatus, header)

		fcStatusG := int(byteMsg[startingByte+4])
		fcStatusG = fcStatusG*256 + int(byteMsg[startingByte+3])

		fcStatusY := int(byteMsg[startingByte+6])
		fcStatusY = fcStatusY*256 + int(byteMsg[startingByte+5])

		fcStatusR := int(byteMsg[startingByte+8])
		fcStatusR = fcStatusR*256 + int(byteMsg[startingByte+7])

		rpStatusG := int(byteMsg[startingByte+10])
		rpStatusG = rpStatusG*256 + int(byteMsg[startingByte+9])

		rpStatusY := int(byteMsg[startingByte+12])
		rpStatusY = rpStatusY*256 + int(byteMsg[startingByte+11])

		rpStatusR := int(byteMsg[startingByte+14])
		rpStatusR = rpStatusR*256 + int(byteMsg[startingByte+13])

		rpStatusW := int(byteMsg[startingByte+16])
		rpStatusW = rpStatusW*256 + int(byteMsg[startingByte+15])

		ggfonStatus := int(byteMsg[startingByte+18])
		ggfonStatus = ggfonStatus*256 + int(byteMsg[startingByte+17])

		green := int(byteMsg[startingByte+20])
		green = green*256 + int(byteMsg[startingByte+19])

		yellow := int(byteMsg[startingByte+22])
		yellow = yellow*256 + int(byteMsg[startingByte+21])

		red := int(byteMsg[startingByte+24])
		red = red*256 + int(byteMsg[startingByte+23])

		walk := int(byteMsg[startingByte+26])
		walk = walk*256 + int(byteMsg[startingByte+25])

		controlStatus := byteMsg[startingByte+27]
		type16Mode := helper.IsBitSet(uint32(controlStatus), 1)

		acLineFrequency := byteMsg[startingByte+28]

		// _24Vminus1 := byteMsg[startingByte+29]

		acLineVoltage := byteMsg[startingByte+30]

		temperature := byteMsg[startingByte+31]

		bcdSeconds := byteMsg[startingByte+32]
		bcdMinutes := byteMsg[startingByte+33]
		bcdHour := byteMsg[startingByte+34]
		bcdDate := byteMsg[startingByte+35]
		bcdMonth := byteMsg[startingByte+36]
		bcdYear := byteMsg[startingByte+37]

		theDateTime, err := helper.ConvertBCDBytesToDateTimeII(bcdMonth, bcdDate, bcdYear, bcdHour, bcdMinutes, bcdSeconds, httpHeader.GatewayTimezone)
		if err != nil {
			return nil, err
		}
		// spare := byteMsg[startingByte+38]

		redEnableRmsVoltage := byteMsg[startingByte+39]

		greenChannel16RmsVoltage := byteMsg[startingByte+40]
		greenChannel15RmsVoltage := byteMsg[startingByte+41]
		greenChannel14RmsVoltage := byteMsg[startingByte+42]
		greenChannel13RmsVoltage := byteMsg[startingByte+43]
		greenChannel12RmsVoltage := byteMsg[startingByte+44]
		greenChannel11RmsVoltage := byteMsg[startingByte+45]
		greenChannel10RmsVoltage := byteMsg[startingByte+46]
		greenChannel9RmsVoltage := byteMsg[startingByte+47]
		greenChannel8RmsVoltage := byteMsg[startingByte+48]
		greenChannel7RmsVoltage := byteMsg[startingByte+49]
		greenChannel6RmsVoltage := byteMsg[startingByte+50]
		greenChannel5RmsVoltage := byteMsg[startingByte+51]
		greenChannel4RmsVoltage := byteMsg[startingByte+52]
		greenChannel3RmsVoltage := byteMsg[startingByte+53]
		greenChannel2RmsVoltage := byteMsg[startingByte+54]
		greenChannel1RmsVoltage := byteMsg[startingByte+55]

		yellowChannel16RmsVoltage := byteMsg[startingByte+56]
		yellowChannel15RmsVoltage := byteMsg[startingByte+57]
		yellowChannel14RmsVoltage := byteMsg[startingByte+58]
		yellowChannel13RmsVoltage := byteMsg[startingByte+59]
		yellowChannel12RmsVoltage := byteMsg[startingByte+60]
		yellowChannel11RmsVoltage := byteMsg[startingByte+61]
		yellowChannel10RmsVoltage := byteMsg[startingByte+62]
		yellowChannel9RmsVoltage := byteMsg[startingByte+63]
		yellowChannel8RmsVoltage := byteMsg[startingByte+64]
		yellowChannel7RmsVoltage := byteMsg[startingByte+65]
		yellowChannel6RmsVoltage := byteMsg[startingByte+66]
		yellowChannel5RmsVoltage := byteMsg[startingByte+67]
		yellowChannel4RmsVoltage := byteMsg[startingByte+68]
		yellowChannel3RmsVoltage := byteMsg[startingByte+69]
		yellowChannel2RmsVoltage := byteMsg[startingByte+70]
		yellowChannel1RmsVoltage := byteMsg[startingByte+71]

		redChannel16RmsVoltage := byteMsg[startingByte+72]
		redChannel15RmsVoltage := byteMsg[startingByte+73]
		redChannel14RmsVoltage := byteMsg[startingByte+74]
		redChannel13RmsVoltage := byteMsg[startingByte+75]
		redChannel12RmsVoltage := byteMsg[startingByte+76]
		redChannel11RmsVoltage := byteMsg[startingByte+77]
		redChannel10RmsVoltage := byteMsg[startingByte+78]
		redChannel9RmsVoltage := byteMsg[startingByte+79]
		redChannel8RmsVoltage := byteMsg[startingByte+80]
		redChannel7RmsVoltage := byteMsg[startingByte+81]
		redChannel6RmsVoltage := byteMsg[startingByte+82]
		redChannel5RmsVoltage := byteMsg[startingByte+83]
		redChannel4RmsVoltage := byteMsg[startingByte+84]
		redChannel3RmsVoltage := byteMsg[startingByte+85]
		redChannel2RmsVoltage := byteMsg[startingByte+86]
		redChannel1RmsVoltage := byteMsg[startingByte+87]

		walkChannel12RmsVoltage := byteMsg[startingByte+88]
		walkChannel11RmsVoltage := byteMsg[startingByte+89]
		walkChannel10RmsVoltage := byteMsg[startingByte+90]
		walkChannel9RmsVoltage := byteMsg[startingByte+91]

		// fcEnableG := int(byteMsg[startingByte+93])
		// fcEnableG = fcEnableG*256 + int(byteMsg[startingByte+92])

		// fcEnableY := int(byteMsg[startingByte+95])
		// fcEnableY = fcEnableY*256 + int(byteMsg[startingByte+94])

		// fcEnableR := int(byteMsg[startingByte+97])
		// fcEnableR = fcEnableR*256 + int(byteMsg[startingByte+96])

		// display data in console

		record.DateTime = theDateTime
		record.Fault = faultMsg

		var j int
		if type16Mode {
			j = 15 // status loop
		} else {
			j = 11
		}
		if faultStatusFlag {
			for i := 0; i <= j; i++ {
				if helper.IsBitSet(uint32(faultStatus), i) {
					record.FaultStatus = append(record.FaultStatus, true)
				} else {
					record.FaultStatus = append(record.FaultStatus, false)
				}
			}
		}
		for i := 0; i <= j; i++ {
			if helper.IsBitSet(uint32(red), i) {
				record.ChannelRedStatus = append(record.ChannelRedStatus, true)
			} else {
				record.ChannelRedStatus = append(record.ChannelRedStatus, false)
			}
		}
		for i := 0; i <= j; i++ {
			if helper.IsBitSet(uint32(yellow), i) {
				record.ChannelYellowStatus = append(record.ChannelYellowStatus, true)
			} else {
				record.ChannelYellowStatus = append(record.ChannelYellowStatus, false)
			}
		}
		for i := 0; i <= j; i++ {
			if helper.IsBitSet(uint32(green), i) {
				record.ChannelGreenStatus = append(record.ChannelGreenStatus, true)
			} else {
				record.ChannelGreenStatus = append(record.ChannelGreenStatus, false)
			}
		}
		if !type16Mode {
			for i := 0; i <= j; i++ {
				if helper.IsBitSet(uint32(walk), i) {
					record.ChannelWalkStatus = append(record.ChannelWalkStatus, true)
				} else {
					record.ChannelWalkStatus = append(record.ChannelWalkStatus, false)
				}
			}
		}
		// R+Y Clearance Status  -- TODO
		if fault == 64 {
			for i := 0; i <= j; i++ {
				if helper.IsBitSet(uint32(ggfonStatus), i) {
					record.NextConflictingChannels = append(record.NextConflictingChannels, true)
				} else {
					record.NextConflictingChannels = append(record.NextConflictingChannels, false)
				}
			}
		}
		// Channel RMS voltages
		record.ChannelRedRmsVoltage = append(record.ChannelRedRmsVoltage, normalizeVoltages(int(redChannel1RmsVoltage), header))
		record.ChannelRedRmsVoltage = append(record.ChannelRedRmsVoltage, normalizeVoltages(int(redChannel2RmsVoltage), header))
		record.ChannelRedRmsVoltage = append(record.ChannelRedRmsVoltage, normalizeVoltages(int(redChannel3RmsVoltage), header))
		record.ChannelRedRmsVoltage = append(record.ChannelRedRmsVoltage, normalizeVoltages(int(redChannel4RmsVoltage), header))
		record.ChannelRedRmsVoltage = append(record.ChannelRedRmsVoltage, normalizeVoltages(int(redChannel5RmsVoltage), header))
		record.ChannelRedRmsVoltage = append(record.ChannelRedRmsVoltage, normalizeVoltages(int(redChannel6RmsVoltage), header))
		record.ChannelRedRmsVoltage = append(record.ChannelRedRmsVoltage, normalizeVoltages(int(redChannel7RmsVoltage), header))
		record.ChannelRedRmsVoltage = append(record.ChannelRedRmsVoltage, normalizeVoltages(int(redChannel8RmsVoltage), header))
		record.ChannelRedRmsVoltage = append(record.ChannelRedRmsVoltage, normalizeVoltages(int(redChannel9RmsVoltage), header))
		record.ChannelRedRmsVoltage = append(record.ChannelRedRmsVoltage, normalizeVoltages(int(redChannel10RmsVoltage), header))
		record.ChannelRedRmsVoltage = append(record.ChannelRedRmsVoltage, normalizeVoltages(int(redChannel11RmsVoltage), header))
		record.ChannelRedRmsVoltage = append(record.ChannelRedRmsVoltage, normalizeVoltages(int(redChannel12RmsVoltage), header))
		if j > 12 {
			record.ChannelRedRmsVoltage = append(record.ChannelRedRmsVoltage, normalizeVoltages(int(redChannel13RmsVoltage), header))
			record.ChannelRedRmsVoltage = append(record.ChannelRedRmsVoltage, normalizeVoltages(int(redChannel14RmsVoltage), header))
			record.ChannelRedRmsVoltage = append(record.ChannelRedRmsVoltage, normalizeVoltages(int(redChannel15RmsVoltage), header))
			record.ChannelRedRmsVoltage = append(record.ChannelRedRmsVoltage, normalizeVoltages(int(redChannel16RmsVoltage), header))
		}
		record.ChannelYellowRmsVoltage = append(record.ChannelYellowRmsVoltage, normalizeVoltages(int(yellowChannel1RmsVoltage), header))
		record.ChannelYellowRmsVoltage = append(record.ChannelYellowRmsVoltage, normalizeVoltages(int(yellowChannel2RmsVoltage), header))
		record.ChannelYellowRmsVoltage = append(record.ChannelYellowRmsVoltage, normalizeVoltages(int(yellowChannel3RmsVoltage), header))
		record.ChannelYellowRmsVoltage = append(record.ChannelYellowRmsVoltage, normalizeVoltages(int(yellowChannel4RmsVoltage), header))
		record.ChannelYellowRmsVoltage = append(record.ChannelYellowRmsVoltage, normalizeVoltages(int(yellowChannel5RmsVoltage), header))
		record.ChannelYellowRmsVoltage = append(record.ChannelYellowRmsVoltage, normalizeVoltages(int(yellowChannel6RmsVoltage), header))
		record.ChannelYellowRmsVoltage = append(record.ChannelYellowRmsVoltage, normalizeVoltages(int(yellowChannel7RmsVoltage), header))
		record.ChannelYellowRmsVoltage = append(record.ChannelYellowRmsVoltage, normalizeVoltages(int(yellowChannel8RmsVoltage), header))
		record.ChannelYellowRmsVoltage = append(record.ChannelYellowRmsVoltage, normalizeVoltages(int(yellowChannel9RmsVoltage), header))
		record.ChannelYellowRmsVoltage = append(record.ChannelYellowRmsVoltage, normalizeVoltages(int(yellowChannel10RmsVoltage), header))
		record.ChannelYellowRmsVoltage = append(record.ChannelYellowRmsVoltage, normalizeVoltages(int(yellowChannel11RmsVoltage), header))
		record.ChannelYellowRmsVoltage = append(record.ChannelYellowRmsVoltage, normalizeVoltages(int(yellowChannel12RmsVoltage), header))
		if j > 12 {
			record.ChannelYellowRmsVoltage = append(record.ChannelYellowRmsVoltage, normalizeVoltages(int(yellowChannel13RmsVoltage), header))
			record.ChannelYellowRmsVoltage = append(record.ChannelYellowRmsVoltage, normalizeVoltages(int(yellowChannel14RmsVoltage), header))
			record.ChannelYellowRmsVoltage = append(record.ChannelYellowRmsVoltage, normalizeVoltages(int(yellowChannel15RmsVoltage), header))
			record.ChannelYellowRmsVoltage = append(record.ChannelYellowRmsVoltage, normalizeVoltages(int(yellowChannel16RmsVoltage), header))
		}
		record.ChannelGreenRmsVoltage = append(record.ChannelGreenRmsVoltage, normalizeVoltages(int(greenChannel1RmsVoltage), header))
		record.ChannelGreenRmsVoltage = append(record.ChannelGreenRmsVoltage, normalizeVoltages(int(greenChannel2RmsVoltage), header))
		record.ChannelGreenRmsVoltage = append(record.ChannelGreenRmsVoltage, normalizeVoltages(int(greenChannel3RmsVoltage), header))
		record.ChannelGreenRmsVoltage = append(record.ChannelGreenRmsVoltage, normalizeVoltages(int(greenChannel4RmsVoltage), header))
		record.ChannelGreenRmsVoltage = append(record.ChannelGreenRmsVoltage, normalizeVoltages(int(greenChannel5RmsVoltage), header))
		record.ChannelGreenRmsVoltage = append(record.ChannelGreenRmsVoltage, normalizeVoltages(int(greenChannel6RmsVoltage), header))
		record.ChannelGreenRmsVoltage = append(record.ChannelGreenRmsVoltage, normalizeVoltages(int(greenChannel7RmsVoltage), header))
		record.ChannelGreenRmsVoltage = append(record.ChannelGreenRmsVoltage, normalizeVoltages(int(greenChannel8RmsVoltage), header))
		record.ChannelGreenRmsVoltage = append(record.ChannelGreenRmsVoltage, normalizeVoltages(int(greenChannel9RmsVoltage), header))
		record.ChannelGreenRmsVoltage = append(record.ChannelGreenRmsVoltage, normalizeVoltages(int(greenChannel10RmsVoltage), header))
		record.ChannelGreenRmsVoltage = append(record.ChannelGreenRmsVoltage, normalizeVoltages(int(greenChannel11RmsVoltage), header))
		record.ChannelGreenRmsVoltage = append(record.ChannelGreenRmsVoltage, normalizeVoltages(int(greenChannel12RmsVoltage), header))
		if j > 12 {
			record.ChannelGreenRmsVoltage = append(record.ChannelGreenRmsVoltage, normalizeVoltages(int(greenChannel13RmsVoltage), header))
			record.ChannelGreenRmsVoltage = append(record.ChannelGreenRmsVoltage, normalizeVoltages(int(greenChannel14RmsVoltage), header))
			record.ChannelGreenRmsVoltage = append(record.ChannelGreenRmsVoltage, normalizeVoltages(int(greenChannel15RmsVoltage), header))
			record.ChannelGreenRmsVoltage = append(record.ChannelGreenRmsVoltage, normalizeVoltages(int(greenChannel16RmsVoltage), header))
		}
		// walk voltages
		if !type16Mode {
			// walk voltages 1-4
			record.ChannelWalkRmsVoltage = append(record.ChannelWalkRmsVoltage, normalizeVoltages(int(greenChannel1RmsVoltage), header))
			record.ChannelWalkRmsVoltage = append(record.ChannelWalkRmsVoltage, normalizeVoltages(int(greenChannel2RmsVoltage), header))
			record.ChannelWalkRmsVoltage = append(record.ChannelWalkRmsVoltage, normalizeVoltages(int(greenChannel3RmsVoltage), header))
			record.ChannelWalkRmsVoltage = append(record.ChannelWalkRmsVoltage, normalizeVoltages(int(greenChannel4RmsVoltage), header))
			// walk voltages 5-8
			record.ChannelWalkRmsVoltage = append(record.ChannelWalkRmsVoltage, normalizeVoltages(int(yellowChannel1RmsVoltage), header))
			record.ChannelWalkRmsVoltage = append(record.ChannelWalkRmsVoltage, normalizeVoltages(int(yellowChannel2RmsVoltage), header))
			record.ChannelWalkRmsVoltage = append(record.ChannelWalkRmsVoltage, normalizeVoltages(int(yellowChannel3RmsVoltage), header))
			record.ChannelWalkRmsVoltage = append(record.ChannelWalkRmsVoltage, normalizeVoltages(int(yellowChannel4RmsVoltage), header))
			// walk voltages 9-12
			record.ChannelWalkRmsVoltage = append(record.ChannelWalkRmsVoltage, normalizeVoltages(int(walkChannel9RmsVoltage), header))
			record.ChannelWalkRmsVoltage = append(record.ChannelWalkRmsVoltage, normalizeVoltages(int(walkChannel10RmsVoltage), header))
			record.ChannelWalkRmsVoltage = append(record.ChannelWalkRmsVoltage, normalizeVoltages(int(walkChannel11RmsVoltage), header))
			record.ChannelWalkRmsVoltage = append(record.ChannelWalkRmsVoltage, normalizeVoltages(int(walkChannel12RmsVoltage), header))
		}

		// field check status
		walkFcStatus := 0
		if helper.IsBitSet(uint32(controlStatus), 2) {
			// SDLC TYPE-12 mode: map high fcStatusG bits -> walk bits
			if fcStatusG&0x800 != 0 {
				walkFcStatus |= 0x80
			} // fc bit11 -> walk bit7
			if fcStatusG&0x400 != 0 {
				walkFcStatus |= 0x20
			} // fc bit10 -> walk bit5
			if fcStatusG&0x200 != 0 {
				walkFcStatus |= 0x08
			} // fc bit9  -> walk bit3
			if fcStatusG&0x100 != 0 {
				walkFcStatus |= 0x02
			} // fc bit8  -> walk bit1
		}

		// only if any field-check word is non-zero do we even populate the slices:
		if fcStatusR != 0 || fcStatusY != 0 || fcStatusG != 0 {
			// 1) always build the three R/Y/G FC slices
			for i := 0; i <= j; i++ {
				record.ChannelRedFieldCheckStatus = append(record.ChannelRedFieldCheckStatus,
					helper.IsBitSet(uint32(fcStatusR), i))
				record.ChannelYellowFieldCheckStatus = append(record.ChannelYellowFieldCheckStatus,
					helper.IsBitSet(uint32(fcStatusY), i))
				record.ChannelGreenFieldCheckStatus = append(record.ChannelGreenFieldCheckStatus,
					helper.IsBitSet(uint32(fcStatusG), i))
			}

			// 2) then decide what to do with the walk-FC slice:
			if !helper.IsBitSet(uint32(controlStatus), 2) {
				// DEFAULT (no TYPE-12 bit): give them 12 false entries
				for i := 0; i <= j; i++ {
					record.ChannelWalkFieldCheckStatus = append(record.ChannelWalkFieldCheckStatus, false)
				}
			} else if walkFcStatus > 0 {
				// TYPE-12 *with* mapping bits: append exactly those mapped bits
				for i := 0; i <= j; i++ {
					record.ChannelWalkFieldCheckStatus = append(record.ChannelWalkFieldCheckStatus,
						helper.IsBitSet(uint32(walkFcStatus), i))
				}
			}
			// otherwise (TYPE-12 but no mapped bits) -> leave ChannelWalkFieldCheckStatus at len=0
		}

		// --- recurrent-pulse status --------------------------------------------

		if rpStatusR != 0 || rpStatusY != 0 || rpStatusG != 0 || rpStatusW != 0 {
			for i := 0; i <= j; i++ {
				record.ChannelRedRecurrentPulseStatus = append(record.ChannelRedRecurrentPulseStatus,
					helper.IsBitSet(uint32(rpStatusR), i))
				record.ChannelYellowRecurrentPulseStatus = append(record.ChannelYellowRecurrentPulseStatus,
					helper.IsBitSet(uint32(rpStatusY), i))
				record.ChannelGreenRecurrentPulseStatus = append(record.ChannelGreenRecurrentPulseStatus,
					helper.IsBitSet(uint32(rpStatusG), i))
				if !type16Mode {
					record.ChannelWalkRecurrentPulseStatus = append(record.ChannelWalkRecurrentPulseStatus,
						helper.IsBitSet(uint32(rpStatusW), i))
				}
			}
		}

		// now do the recurrent-pulse block, correctly populating the *recurrent-pulse* walk slice:
		if rpStatusR != 0 || rpStatusY != 0 || rpStatusG != 0 || rpStatusW != 0 {
			for i := 0; i <= j; i++ {
				record.ChannelRedRecurrentPulseStatus = append(
					record.ChannelRedRecurrentPulseStatus,
					helper.IsBitSet(uint32(rpStatusR), i),
				)
				record.ChannelYellowRecurrentPulseStatus = append(
					record.ChannelYellowRecurrentPulseStatus,
					helper.IsBitSet(uint32(rpStatusY), i),
				)
				record.ChannelGreenRecurrentPulseStatus = append(
					record.ChannelGreenRecurrentPulseStatus,
					helper.IsBitSet(uint32(rpStatusG), i),
				)
				if !type16Mode {
					record.ChannelWalkRecurrentPulseStatus = append(
						record.ChannelWalkRecurrentPulseStatus,
						helper.IsBitSet(uint32(rpStatusW), i),
					)
				}
			}
		}

		//
		// ac line voltage and frequency
		if header.Volt220 {
			record.AcLine = fmt.Sprintf("%d Vrms @ %dHz", int(acLineVoltage)*2, acLineFrequency)
		} else {
			record.AcLine = fmt.Sprintf("%d Vrms @ %dHz", int(acLineVoltage), acLineFrequency)
		}
		// re, ls bit status
		var redEnable string
		if controlStatus&0x80 > 0 {
			redEnable = "Active"
		} else {
			redEnable = "Off"
		}
		if header.Volt220 {
			record.RedEnable = fmt.Sprintf("%s (%d Vrms)", redEnable, int(redEnableRmsVoltage)*2)
		} else {
			record.RedEnable = fmt.Sprintf("%s (%d Vrms)", redEnable, int(redEnableRmsVoltage))
		}
		if type16Mode {
			if controlStatus&0x40 > 0 {
				record.LsFlashBit = true
			} else {
				record.LsFlashBit = false
			}
		}
		//
		// temperature
		record.Temperature = int64(temperature) - 40
		allRecords.Records = append(allRecords.Records, record)
		startingByte += RecordSize // skip to byte of next record
	}

	return allRecords, nil
}

func (device EDIMMU216LE) LogACLineEvent(httpHeader *pubsubdata.HeaderDetails, byteMsg []byte, header *helper.HeaderRecord) (allRecords *helper.LogACLineEventRecords, err error) {
	const HeaderLength = 7
	const RecordSize = 8

	if len(byteMsg) < 7 {
		return nil, fmt.Errorf("%w byte length less then 7", helper.ErrMsgByteLen)
	}
	numberOfRecords := int(byteMsg[HeaderLength])
	startingByte := (HeaderLength + 1)
	length := HeaderLength + (numberOfRecords * RecordSize) + 2
	allRecords = new(helper.LogACLineEventRecords)

	// Verify byte length
	if !helper.VerifyByteLen(byteMsg, length) {
		return nil, fmt.Errorf("%w %v should be %v", helper.ErrMsgByteLen, len(byteMsg), length)
	}

	// Validate the checksum of version
	err = helper.ValidateChecksum(byteMsg)
	if err != nil {
		return nil, fmt.Errorf("%w : %v", helper.ErrMsgByteChecksum, err)
	}

	allRecords.DeviceModel = deviceModel
	allRecords.RawMessage = byteMsg
	// process the response
	if header.MainsDC {
		allRecords.VoltageType = 2
	} else {
		allRecords.VoltageType = 1
	}

	for i := 0; i < numberOfRecords; i++ {
		var record helper.LogACLineEventRecord
		eventType := byteMsg[startingByte+0]
		linevoltage := byteMsg[startingByte+1]

		record.EventType = helper.GetACLineEventType(helper.MonitorModel(header.Model), header.MainsDC, int64(linevoltage), header.PowerDownLevel, eventType)
		record.DateTime, err = helper.ConvertBCDBytesToDateTimeII(byteMsg[startingByte+6], byteMsg[startingByte+5], byteMsg[startingByte+7], byteMsg[startingByte+4], byteMsg[startingByte+3], byteMsg[startingByte+2], httpHeader.GatewayTimezone)
		if err != nil {
			return nil, err
		}
		if int64(linevoltage) < header.BlackoutLevel {
			record.LineVoltageRms = 0
		} else {
			record.LineVoltageRms = int64(linevoltage)
		}

		allRecords.Records = append(allRecords.Records, record)

		startingByte += RecordSize // skip to byte of next record
	}

	return allRecords, nil
}

func (device EDIMMU216LE) LogFaultSignalSequence(httpHeader *pubsubdata.HeaderDetails, byteMsg []byte, header *helper.HeaderRecord) (allRecords *helper.FaultSignalSequenceRecords, err error) {
	const headerLength = 7
	const traceLengthMMU2 = 12
	const RECORD_INDICATOR = 10

	if len(byteMsg) < 10 {
		return nil, fmt.Errorf("%w byte length less then 10", helper.ErrMsgByteLen)
	}
	numberOfRecords := int64(byteMsg[9])
	length := headerLength + 2 + (numberOfRecords * traceLengthMMU2) + 2
	allRecords = new(helper.FaultSignalSequenceRecords)

	// Verify byte length
	if !helper.VerifyByteLen(byteMsg, int(length)) {
		return nil, fmt.Errorf("%w %v should be %v", helper.ErrMsgByteLen, len(byteMsg), length)
	}

	// Validate the checksum of version
	err = helper.ValidateChecksum(byteMsg)
	if err != nil {
		return nil, fmt.Errorf("%w : %v", helper.ErrMsgByteChecksum, err)
	}

	// skip past the header and parse the trace
	faultType := byteMsg[8]
	faultText, _ := GetMMUFaultText(int(faultType), -1, header)

	allRecords.FaultType = faultText
	allRecords.RawMessage = byteMsg
	allRecords.DeviceModel = deviceModel

	// check the timestamps and manipulate them in case of roll-over
	// we may need to manipulate the timestamp -- why? because the timestamp is implemented
	// as a 2-byte value; which rolls over at 65530 (VB6 code frmTrace.SetTimeStamp)

	const traceMax int64 = 65530 // this is the max timestamp - when the timestamp crosses over, you need to add this back.
	startPos := 10

	for j := 0; j < int(numberOfRecords); j++ {
		tracebytes := byteMsg[startPos : startPos+12]
		tb := traceRecordParser(tracebytes, header)
		tb.BufferRawBytes = tracebytes
		// since we are reading the logs from the failed event, backwards, we must consider the log timestamp having rolled-over
		if tb.Timestamp > traceMax {
			tb.Timestamp = traceMax
		}
		allRecords.Records = append(allRecords.Records, *tb)
		startPos += 12
	}

	// adjust time stamps
	helper.NormalizeTimestamps(allRecords) // double-check this to make sure the data is modified in the structure
	allRecords = helper.Performcalcs(allRecords)

	return allRecords, nil
}

func (device EDIMMU216LE) LogConfiguration(httpHeader *pubsubdata.HeaderDetails, byteMsg []byte, header *helper.HeaderRecord) (allRecords *helper.ConfigurationChangeLogRecords, err error) {
	const HeaderLength = 7
	const RecordSize = 64

	numberOfRecords := int(byteMsg[HeaderLength])
	startingByte := (HeaderLength + 1)
	length := HeaderLength + (numberOfRecords * RecordSize) + 2
	allRecords = new(helper.ConfigurationChangeLogRecords)

	// Verify byte length
	if !helper.VerifyByteLen(byteMsg, length) {
		return nil, fmt.Errorf("%w %v should be %v", helper.ErrMsgByteLen, len(byteMsg), length)
	}

	// Validate the checksum of version
	err = helper.ValidateChecksum(byteMsg)
	if err != nil {
		return nil, fmt.Errorf("%w : %v", helper.ErrMsgByteChecksum, err)
	}
	allRecords.DeviceModel = deviceModel
	allRecords.RawMessage = byteMsg
	var cfr helper.ConfigurationChangeLogRecord

	for loop := 0; loop < numberOfRecords; loop++ {
		cfr = helper.ConfigurationChangeLogRecord{}

		conflictMapCh1 := int((byteMsg[startingByte+1]))
		conflictMapCh1 = conflictMapCh1*256 + int((byteMsg[startingByte+0]))

		conflictMapCh2 := int((byteMsg[startingByte+3]))
		conflictMapCh2 = conflictMapCh2*256 + int((byteMsg[startingByte+2]))

		conflictMapCh3 := int((byteMsg[startingByte+5]))
		conflictMapCh3 = conflictMapCh3*256 + int((byteMsg[startingByte+4]))

		conflictMapCh4 := int((byteMsg[startingByte+7]))
		conflictMapCh4 = conflictMapCh4*256 + int((byteMsg[startingByte+6]))

		conflictMapCh5 := int((byteMsg[startingByte+9]))
		conflictMapCh5 = conflictMapCh5*256 + int((byteMsg[startingByte+8]))

		conflictMapCh6 := int((byteMsg[startingByte+11]))
		conflictMapCh6 = conflictMapCh6*256 + int((byteMsg[startingByte+10]))

		conflictMapCh7 := int((byteMsg[startingByte+13]))
		conflictMapCh7 = conflictMapCh7*256 + int((byteMsg[startingByte+12]))

		conflictMapCh8 := int((byteMsg[startingByte+15]))
		conflictMapCh8 = conflictMapCh8*256 + int((byteMsg[startingByte+14]))

		conflictMapCh9 := int((byteMsg[startingByte+17]))
		conflictMapCh9 = conflictMapCh9*256 + int((byteMsg[startingByte+16]))

		conflictMapCh10 := int((byteMsg[startingByte+19]))
		conflictMapCh10 = conflictMapCh10*256 + int((byteMsg[startingByte+18]))

		conflictMapCh11 := int((byteMsg[startingByte+21]))
		conflictMapCh11 = conflictMapCh11*256 + int((byteMsg[startingByte+20]))

		conflictMapCh12 := int((byteMsg[startingByte+23]))
		conflictMapCh12 = conflictMapCh12*256 + int((byteMsg[startingByte+22]))

		conflictMapCh13 := int((byteMsg[startingByte+25]))
		conflictMapCh13 = conflictMapCh13*256 + int((byteMsg[startingByte+24]))

		conflictMapCh14 := int((byteMsg[startingByte+27]))
		conflictMapCh14 = conflictMapCh14*256 + int((byteMsg[startingByte+26]))

		conflictMapCh15 := int((byteMsg[startingByte+29]))
		conflictMapCh15 = conflictMapCh15*256 + int((byteMsg[startingByte+28]))

		mycd := int((byteMsg[startingByte+31]))
		mycd = mycd*256 + int((byteMsg[startingByte+30]))

		myrcd := int((byteMsg[startingByte+33]))
		myrcd = myrcd*256 + int((byteMsg[startingByte+32]))

		fieldCheckG := int((byteMsg[startingByte+35]))
		fieldCheckG = fieldCheckG*256 + int((byteMsg[startingByte+34]))

		fieldCheckY := int((byteMsg[startingByte+37]))
		fieldCheckY = fieldCheckY*256 + int((byteMsg[startingByte+36]))

		fieldCheckR := int((byteMsg[startingByte+39]))
		fieldCheckR = fieldCheckR*256 + int((byteMsg[startingByte+38]))

		dualEnableYR := int((byteMsg[startingByte+41]))
		dualEnableYR = dualEnableYR*256 + int((byteMsg[startingByte+40]))

		dualEnableGR := int((byteMsg[startingByte+43]))
		dualEnableGR = dualEnableGR*256 + int((byteMsg[startingByte+42]))

		dualEnableGY := int((byteMsg[startingByte+45]))
		dualEnableGY = dualEnableGY*256 + int((byteMsg[startingByte+44]))

		redFailEnable := int((byteMsg[startingByte+47]))
		redFailEnable = redFailEnable*256 + int((byteMsg[startingByte+46]))

		vmMinFlash := byteMsg[startingByte+48]
		options1 := byteMsg[startingByte+49]
		options2 := byteMsg[startingByte+50]
		fyaEnable1 := byteMsg[startingByte+51]
		fyaEnable2 := byteMsg[startingByte+52]
		selectJumpers1 := byteMsg[startingByte+53]
		// selectJumpers2 := byteMsg[startingByte+54]

		bcdSeconds := byteMsg[startingByte+55]
		bcdMinutes := byteMsg[startingByte+56]
		bcdHour := byteMsg[startingByte+57]
		bcdDate := byteMsg[startingByte+58]
		bcdMonth := byteMsg[startingByte+59]
		bcdYear := byteMsg[startingByte+60]
		theDateTime, err := helper.ConvertBCDBytesToDateTimeII(bcdMonth, bcdDate, bcdYear, bcdHour, bcdMinutes, bcdSeconds, httpHeader.GatewayTimezone)
		if err != nil {
			return nil, err
		}
		configChangeSource := byteMsg[startingByte+61]

		crc := int(byteMsg[startingByte+63])<<8 + int(byteMsg[startingByte+62])

		cfr.DateTime = theDateTime

		// process channel 1 permissives 1st
		primaryChannel := 1
		primaryMask := 2
		mask := primaryMask
		for i := primaryChannel + 1; i <= 16; i++ {
			if conflictMapCh1&mask == 0 {
				cfr.Ch01Permissives = append(cfr.Ch01Permissives, strconv.Itoa(i))
			}
			mask *= 2
		}
		// process channel 2 permissives 1st
		primaryChannel = 2
		primaryMask *= 2
		mask = primaryMask
		for i := primaryChannel + 1; i <= 16; i++ {
			if conflictMapCh2&mask == 0 {
				cfr.Ch02Permissives = append(cfr.Ch02Permissives, strconv.Itoa(i))
			}
			mask *= 2
		}
		// process channel 3 permissives 1st
		primaryChannel = 3
		primaryMask *= 2
		mask = primaryMask
		for i := primaryChannel + 1; i <= 16; i++ {
			if conflictMapCh3&mask == 0 {
				cfr.Ch03Permissives = append(cfr.Ch03Permissives, strconv.Itoa(i))
			}
			mask *= 2
		}
		// process channel 4 permissives 1st
		primaryChannel = 4
		primaryMask *= 2
		mask = primaryMask
		for i := primaryChannel + 1; i <= 16; i++ {
			if conflictMapCh4&mask == 0 {
				cfr.Ch04Permissives = append(cfr.Ch04Permissives, strconv.Itoa(i))
			}
			mask *= 2
		}
		// process channel 5 permissives 1st
		primaryChannel = 5
		primaryMask *= 2
		mask = primaryMask
		for i := primaryChannel + 1; i <= 16; i++ {
			if conflictMapCh5&mask == 0 {
				cfr.Ch05Permissives = append(cfr.Ch05Permissives, strconv.Itoa(i))
			}
			mask *= 2
		}
		// process channel 6 permissives 1st
		primaryChannel = 6
		primaryMask *= 2
		mask = primaryMask
		for i := primaryChannel + 1; i <= 16; i++ {
			if conflictMapCh6&mask == 0 {
				cfr.Ch06Permissives = append(cfr.Ch06Permissives, strconv.Itoa(i))
			}
			mask *= 2
		}
		// process channel 7 permissives 1st
		primaryChannel = 7
		primaryMask *= 2
		mask = primaryMask
		for i := primaryChannel + 1; i <= 16; i++ {
			if conflictMapCh7&mask == 0 {
				cfr.Ch07Permissives = append(cfr.Ch07Permissives, strconv.Itoa(i))
			}
			mask *= 2
		}
		// process channel 8 permissives 1st
		primaryChannel = 8
		primaryMask *= 2
		mask = primaryMask
		for i := primaryChannel + 1; i <= 16; i++ {
			if conflictMapCh8&mask == 0 {
				cfr.Ch08Permissives = append(cfr.Ch08Permissives, strconv.Itoa(i))
			}
			mask *= 2
		}
		// process channel 9 permissives 1st
		primaryChannel = 9
		primaryMask *= 2
		mask = primaryMask
		for i := primaryChannel + 1; i <= 16; i++ {
			if conflictMapCh9&mask == 0 {
				cfr.Ch09Permissives = append(cfr.Ch09Permissives, strconv.Itoa(i))
			}
			mask *= 2
		}
		// process channel 10 permissives 1st
		primaryChannel = 10
		primaryMask *= 2
		mask = primaryMask
		for i := primaryChannel + 1; i <= 16; i++ {
			if conflictMapCh10&mask == 0 {
				cfr.Ch10Permissives = append(cfr.Ch10Permissives, strconv.Itoa(i))
			}
			mask *= 2
		}
		// process channel 11 permissives 1st
		primaryChannel = 11
		primaryMask *= 2
		mask = primaryMask
		for i := primaryChannel + 1; i <= 16; i++ {
			if conflictMapCh11&mask == 0 {
				cfr.Ch11Permissives = append(cfr.Ch11Permissives, strconv.Itoa(i))
			}
			mask *= 2
		}
		// process channel 12 permissives 1st
		primaryChannel = 12
		primaryMask = primaryMask * 2
		mask = primaryMask
		for i := primaryChannel + 1; i <= 16; i++ {
			if conflictMapCh12&mask == 0 {
				cfr.Ch12Permissives = append(cfr.Ch12Permissives, strconv.Itoa(i))
			}
			mask *= 2
		}
		// process channel 13 permissives 1st
		primaryChannel = 13
		primaryMask = primaryMask * 2
		mask = primaryMask
		for i := primaryChannel + 1; i <= 16; i++ {
			if conflictMapCh13&mask == 0 {
				cfr.Ch13Permissives = append(cfr.Ch13Permissives, strconv.Itoa(i))
			}
			mask *= 2
		}
		// process channel 14 permissives 1st
		primaryChannel = 14
		primaryMask = primaryMask * 2
		mask = primaryMask
		for i := primaryChannel + 1; i <= 16; i++ {
			if conflictMapCh14&mask == 0 {
				cfr.Ch14Permissives = append(cfr.Ch14Permissives, strconv.Itoa(i))
			}
			mask *= 2
		}
		// process channel 15 permissives 1st
		primaryChannel = 15
		primaryMask = primaryMask * 2
		mask = primaryMask
		for i := primaryChannel + 1; i <= 16; i++ {
			if conflictMapCh15&mask == 0 {
				cfr.Ch15Permissives = append(cfr.Ch15Permissives, strconv.Itoa(i))
			}
			mask *= 2
		}
		//
		// MYCD Jumpers
		mask = 1
		for i := 1; i <= 16; i++ {
			cfr.MinimumYellowClearanceEnable = append(cfr.MinimumYellowClearanceEnable, mycd&mask == 0)
			cfr.MinimumYellowRedClearanceEnable = append(cfr.MinimumYellowRedClearanceEnable, myrcd&mask == 0)
			cfr.FieldCheckEnableRed = append(cfr.FieldCheckEnableRed, fieldCheckR&mask > 0)
			cfr.FieldCheckEnableYellow = append(cfr.FieldCheckEnableYellow, fieldCheckY&mask > 0)
			cfr.FieldCheckEnableGreen = append(cfr.FieldCheckEnableGreen, fieldCheckG&mask > 0)
			// Dual Map
			cfr.GreenRedDualEnable = append(cfr.GreenRedDualEnable, dualEnableGR&mask > 0)
			cfr.YellowRedDualEnable = append(cfr.YellowRedDualEnable, dualEnableYR&mask > 0)
			cfr.GreenYellowDualEnable = append(cfr.GreenYellowDualEnable, dualEnableGY&mask > 0)
			// red enable
			cfr.RedFailEnable = append(cfr.RedFailEnable, redFailEnable&mask > 0)
			mask *= 2
		}
		// options1 display
		cfr.RecurrentPulse = options1&0x40 == 0
		cfr.WatchdogEnableSwitch = options1&0x20 > 0
		cfr.WalkEnableTs1 = options1&0x10 > 0
		if options1&0x1 > 0 {
			cfr.X24VIiInputThreshold = "12 Vdc"
		} else {
			cfr.X24VIiInputThreshold = "24 Vdc"
		}
		cfr.LogCvmFaults = options1&0x4 == 0
		cfr.ProgramCardMemory = options1&0x2 > 0
		cfr.LEDguardThresholds = options1&0x80 > 0
		// options2 display
		cfr.ForceType_16Mode = options2&0x2 > 0
		cfr.Type_12WithSdlcMode = options2&0x4 > 0
		cfr.VmCvm_24V_3XdayLatch = options2&0x1 > 0
		// min flash jumpers, 24v latch, cvm latch
		temp := math.Max(6, float64(int(vmMinFlash&0xf)+1))
		cfr.MinimumFlashTime = fmt.Sprintf("%d seconds", int(temp))
		cfr.CvmLatchEnable = vmMinFlash&0x20 > 0
		cfr.X24VLatchEnable = vmMinFlash&0x10 > 0
		cfr.X24VoltInhibit = selectJumpers1&0x4 > 0
		cfr.Port_1Disable = selectJumpers1&0x2 > 0
		if selectJumpers1&0x1 > 0 {
			cfr.TypeMode = "16"
		} else {
			cfr.TypeMode = "12"
		}
		// flashing yellow arrow
		const fya_ry_lo = 0x10  // fya_enable1: 1=RY on 1,3,5,7 / 0=RY is 9:12 or 13:16
		const fya_msb_hi = 0x20 // fya_enable1: Mode=0 9:12 / Mode=1 13:16
		const fya_srcY = 0x40   // fya_enable1: Mode=0 source Green / Mode=1 source Yellow
		const fya_fYa_lo = 0x80 // fya_enable1: 0=fYa is 9:12 or 13:16 / 1=fYa on 1,3,5,7
		const trap_mode = 0x10  // fya_enable2: Mode=0 none / Mode=1 detect trap
		const fya_flash = 0x20  // fya_enable2: Mode=0 stuck FYA no fault / Mode=1 stuck FYA fault
		const fya_remap = 0x40  // fya_enable2: Mode=0 none / Mode=1 remap Ped Y to loner

		var FlashingYellowArrows string
		if header.CommVersion >= 0x38 {
			var nema_FyaMode int
			if fyaEnable2&fya_remap > 0 {
				switch fyaEnable1 & 0xf0 {
				case fya_srcY + fya_fYa_lo + fya_ry_lo:
					nema_FyaMode = 4
					FlashingYellowArrows = "Mode E"
				case fya_msb_hi + fya_srcY + fya_fYa_lo + fya_ry_lo:
					nema_FyaMode = 5
					FlashingYellowArrows = "Mode F"
				case fya_srcY + fya_ry_lo:
					nema_FyaMode = 8
					FlashingYellowArrows = "Mode I"
				case fya_msb_hi + fya_srcY + fya_ry_lo:
					nema_FyaMode = 9
					FlashingYellowArrows = "Mode J"
				default:
					nema_FyaMode = 0
					FlashingYellowArrows = "Mode <error>"
				}
			} else {
				switch fyaEnable1 & 0xf0 {
				case 0:
					nema_FyaMode = 0
					FlashingYellowArrows = "Mode A"
				case fya_msb_hi:
					nema_FyaMode = 1
					FlashingYellowArrows = "Mode B"
				case fya_fYa_lo + fya_ry_lo:
					nema_FyaMode = 2
					FlashingYellowArrows = "Mode C"
				case fya_msb_hi + fya_fYa_lo + fya_ry_lo:
					nema_FyaMode = 3
					FlashingYellowArrows = "Mode D"
				case fya_srcY + fya_ry_lo:
					nema_FyaMode = 6
					FlashingYellowArrows = "Mode G"
				case fya_msb_hi + fya_srcY + fya_ry_lo:
					nema_FyaMode = 7
					FlashingYellowArrows = "Mode H"
				case fya_ry_lo:
					nema_FyaMode = 10
					FlashingYellowArrows = "Mode K"
				case fya_msb_hi + fya_ry_lo:
					nema_FyaMode = 11
					FlashingYellowArrows = "Mode L"
				default:
					nema_FyaMode = 0
					FlashingYellowArrows = "Mode <error>"
				}
			}
			if fyaEnable1&0xf > 0 {
				switch nema_FyaMode {
				case 0, 2, 4, 6, 8, 10:
					if fyaEnable1&0x1 > 0 {
						FlashingYellowArrows += ", Channel Pairs: 1-9"
					}
					if fyaEnable1&0x2 > 0 {
						FlashingYellowArrows += ", Channel Pairs: 3-10"
					}
					if fyaEnable1&0x4 > 0 {
						FlashingYellowArrows += ", Channel Pairs: 5-11"
					}
					if fyaEnable1&0x8 > 0 {
						FlashingYellowArrows += ", Channel Pairs: 7-12"
					}
				case 1, 3, 5, 7, 9, 11:
					if fyaEnable1&0x1 > 0 {
						FlashingYellowArrows += ", Channel Pairs: 1-13"
					}
					if fyaEnable1&0x2 > 0 {
						FlashingYellowArrows += ", Channel Pairs: 3-14"
					}
					if fyaEnable1&0x4 > 0 {
						FlashingYellowArrows += ", Channel Pairs: 3-15"
					}
					if fyaEnable1&0x8 > 0 {
						FlashingYellowArrows += ", Channel Pairs: 7-16"
					}
				}
			} else {
				FlashingYellowArrows += ", Channel Pairs: <none>"
			}
			//
			if fyaEnable2&0xf > 0 {
				switch nema_FyaMode {
				case 0, 1:
					if fyaEnable2&0x1 > 0 {
						cfr.FyaRedAndYellowEnable = "Channels: 1"
					}
					if fyaEnable2&0x2 > 0 {
						cfr.FyaRedAndYellowEnable = "Channels: 3"
					}
					if fyaEnable2&0x4 > 0 {
						cfr.FyaRedAndYellowEnable = "Channels: 5"
					}
					if fyaEnable2&0x8 > 0 {
						cfr.FyaRedAndYellowEnable = "Channels: 7"
					}
				case 2, 4, 8, 10:
					if fyaEnable2&0x1 > 0 {
						cfr.FyaRedAndYellowEnable = "Channels: 9"
					}
					if fyaEnable2&0x2 > 0 {
						cfr.FyaRedAndYellowEnable = "Channels: 10"
					}
					if fyaEnable2&0x4 > 0 {
						cfr.FyaRedAndYellowEnable = "Channels: 11"
					}
					if fyaEnable2&0x8 > 0 {
						cfr.FyaRedAndYellowEnable = "Channels: 12"
					}
				case 3, 5, 9, 11:
					if fyaEnable2&0x1 > 0 {
						cfr.FyaRedAndYellowEnable = "Channels: 13"
					}
					if fyaEnable2&0x2 > 0 {
						cfr.FyaRedAndYellowEnable = "Channels: 14"
					}
					if fyaEnable2&0x4 > 0 {
						cfr.FyaRedAndYellowEnable = "Channels: 15"
					}
					if fyaEnable2&0x8 > 0 {
						cfr.FyaRedAndYellowEnable = "Channels: 16"
					}
				case 6:
					if header.FirmwareRevision > 0x73 {
						if fyaEnable2&0x1 > 0 {
							cfr.FyaRedAndGreenDisable = "Channels: 9"
						}
						if fyaEnable2&0x2 > 0 {
							cfr.FyaRedAndGreenDisable = "Channels: 10"
						}
						if fyaEnable2&0x4 > 0 {
							cfr.FyaRedAndGreenDisable = "Channels: 11"
						}
						if fyaEnable2&0x8 > 0 {
							cfr.FyaRedAndGreenDisable = "Channels: 12"
						}
					} else {
						cfr.FyaRedAndYellowEnable = "Channels: <none>"
					}
				case 7:
					if header.FirmwareRevision > 0x73 {
						if fyaEnable2&0x1 > 0 {
							cfr.FyaRedAndGreenDisable = "Channels: 13"
						}
						if fyaEnable2&0x2 > 0 {
							cfr.FyaRedAndGreenDisable = "Channels: 14"
						}
						if fyaEnable2&0x4 > 0 {
							cfr.FyaRedAndGreenDisable = "Channels: 15"
						}
						if fyaEnable2&0x8 > 0 {
							cfr.FyaRedAndGreenDisable = "Channels: 16"
						}
					} else {
						cfr.FyaRedAndYellowEnable = "Channels: <none>"
					}
				}
			} else {
				switch nema_FyaMode {
				case 6, 7:
					if header.FirmwareRevision > 0x73 {
						cfr.FyaRedAndGreenDisable = "Channels: <none>"
					} else {
						cfr.FyaRedAndYellowEnable = "Channels: <none>"
					}
				default:
					cfr.FyaRedAndYellowEnable = "Channels: <none>"
				}
			}
			cfr.FyaYellowTrapDetection = fyaEnable2&trap_mode > 0
			cfr.FyaFlashRateDetection = fyaEnable2&fya_flash > 0
		} else {
			// legacy EDI FYA
			if selectJumpers1&0x78 > 0 {
				if selectJumpers1&0x1 > 0 { // type 16
					if selectJumpers1&0x8 > 0 {
						FlashingYellowArrows = "1-13 "
					}
					if selectJumpers1&0x10 > 0 {
						FlashingYellowArrows = "3-14 "
					}
					if selectJumpers1&0x20 > 0 {
						FlashingYellowArrows = "5-15 "
					}
					if selectJumpers1&0x40 > 0 {
						FlashingYellowArrows = "7-16 "
					}
				} else { // type 12
					if selectJumpers1&0x8 > 0 {
						FlashingYellowArrows = "1-9 "
					}
					if selectJumpers1&0x10 > 0 {
						FlashingYellowArrows = "3-10 "
					}
					if selectJumpers1&0x20 > 0 {
						FlashingYellowArrows = "5-11 "
					}
					if selectJumpers1&0x40 > 0 {
						FlashingYellowArrows = "7-12 "
					}
				}
				if selectJumpers1&0x80 > 0 {
					FlashingYellowArrows += "(Mode=FYAC)"
				} else {
					FlashingYellowArrows += "(Mode=FYA)"
				}
			} else {
				FlashingYellowArrows = "<none>"
			}
		}
		cfr.FlashingYellowArrows = append(cfr.FlashingYellowArrows, FlashingYellowArrows)
		// PLT5 Arrow Suppression
		if options2&0x8 > 0 {
			cfr.Pplt5Suppression = "PPLT5 Ball Suppression = "
		} else {
			cfr.Pplt5Suppression = "PPLT5 Arrow Suppression = "
		}
		if options2&0xf0 > 0 {
			if selectJumpers1&0x1 > 0 {
				if header.FirmwareVersion > 0x28 {
					if selectJumpers1&0x10 > 0 {
						cfr.Pplt5Suppression += "1-9"
					}
					if selectJumpers1&0x20 > 0 {
						cfr.Pplt5Suppression += "3-10"
					}
					if selectJumpers1&0x40 > 0 {
						cfr.Pplt5Suppression += "5-11"
					}
					if selectJumpers1&0x80 > 0 {
						cfr.Pplt5Suppression += "7-12"
					}
				} else {
					if selectJumpers1&0x10 > 0 {
						cfr.Pplt5Suppression += "1-13"
					}
					if selectJumpers1&0x20 > 0 {
						cfr.Pplt5Suppression += "3-14"
					}
					if selectJumpers1&0x40 > 0 {
						cfr.Pplt5Suppression += "5-15"
					}
					if selectJumpers1&0x80 > 0 {
						cfr.Pplt5Suppression += "7-16"
					}
				}
			} else {
				if selectJumpers1&0x10 > 0 {
					cfr.Pplt5Suppression += "1-9"
				}
				if selectJumpers1&0x20 > 0 {
					cfr.Pplt5Suppression += "3-10"
				}
				if selectJumpers1&0x40 > 0 {
					cfr.Pplt5Suppression += "5-11"
				}
				if selectJumpers1&0x80 > 0 {
					cfr.Pplt5Suppression += "7-12"
				}
			}
		} else {
			cfr.Pplt5Suppression += "<none>"
		}
		// change source
		var source string
		switch configChangeSource {
		case 1:
			source = "Front Panel Entry"
		case 2:
			source = "ECcom Download"
		case 3:
			source = "Program Card Entry"
		case 4:
			source = "Front Panel 'Set Default' Entry"
		default:
			source = ""
		}
		cfr.ChangeSource = source
		// crc value
		cfr.CheckValue = strconv.Itoa(crc)
		allRecords.Record = append(allRecords.Record, cfr)
		startingByte += RecordSize // skip to byte of next record
	}

	return allRecords, nil
}

func (device EDIMMU216LE) MonitorIDandName(httpHeader *pubsubdata.HeaderDetails, byteMsg []byte, header *helper.HeaderRecord) (monitor *helper.MonitorNameAndId, err error) {
	const HeaderLength = 7
	const MonitorNameLength = 30
	const MONITORIDls_BYTE_INDICATOR = 5
	const MONITORIDms_BYTE_INDICATOR = 6

	length := HeaderLength + MonitorNameLength + 1
	monitor = new(helper.MonitorNameAndId)

	// Verify byte length
	if !helper.VerifyByteLen(byteMsg, length) {
		return nil, fmt.Errorf("%w %v should be %v", helper.ErrMsgByteLen, len(byteMsg), length)
	}

	// Validate the checksum of version
	err = helper.ValidateChecksum(byteMsg)
	if err != nil {
		return nil, fmt.Errorf("%w : %v", helper.ErrMsgByteChecksum, err)
	}
	monitor.DeviceModel = deviceModel
	// process the response
	monitor.MonitorId = int64(helper.ConvertLSandMStoUnint16(byteMsg[MONITORIDls_BYTE_INDICATOR], byteMsg[MONITORIDms_BYTE_INDICATOR]))
	monitor.MonitorName = helper.GetMonitorName(byteMsg[HeaderLength:(length - 1)])

	return monitor, nil
}

func (device EDIMMU216LE) RMSEngineData(httpHeader *pubsubdata.HeaderDetails, byteMsg []byte, header *helper.HeaderRecord) (rmsEngineDetail *helper.RmsEngineRecord, err error) {
	const expectedLength = 20

	rmsEngineDetail = new(helper.RmsEngineRecord)

	// Verify byte length
	if !helper.VerifyByteLen(byteMsg, expectedLength) {
		return nil, fmt.Errorf("%w %v should be %v", helper.ErrMsgByteLen, len(byteMsg), expectedLength)
	}

	// Validate the checksum of version
	err = helper.ValidateChecksum(byteMsg[0:10])
	if err != nil {
		return nil, fmt.Errorf("%w 0-9 : %v", helper.ErrMsgByteChecksum, err)
	}

	// Validate the checksum of revison
	err = helper.ValidateChecksum(byteMsg[10:20])
	if err != nil {
		return nil, fmt.Errorf("%w 10-19 : %v", helper.ErrMsgByteChecksum, err)
	}

	// 1-6:   7-byte version request header
	// byte 0 is the command and that wont match and is not included
	versionHeader := byteMsg[1:7]
	// 11-16: 7-byte revision request header
	// byte 0 is the command and that wont match and is not included
	revisionHeader := byteMsg[11:17]

	// Validate the headers match
	if !bytes.Equal(versionHeader, revisionHeader) {
		return nil, fmt.Errorf("%w", helper.ErrRMSEngineDataHeaderMismatch)
	}

	rmsEngineDetail.DeviceModel = deviceModel

	// process response for rms engine
	rmsEngineDetail.EngineVersion = int64(byteMsg[8])
	rmsEngineDetail.EngineRevision = int64(byteMsg[18])

	return rmsEngineDetail, nil
}

func (device EDIMMU216LE) RMSStatus(httpHeader *pubsubdata.HeaderDetails, byteMsg []byte, header *helper.HeaderRecord) (rmsStatusRecord *helper.RmsStatusRecord, err error) {
	const expectedLength = 106
	rmsStatusRecord = new(helper.RmsStatusRecord)

	// Verify byte length
	if !helper.VerifyByteLen(byteMsg, expectedLength) {
		return nil, fmt.Errorf("%w %v should be %v", helper.ErrMsgByteLen, len(byteMsg), expectedLength)
	}

	// Validate the checksum
	err = helper.ValidateChecksum(byteMsg)
	if err != nil {
		return nil, fmt.Errorf("%w : %v", helper.ErrMsgByteChecksum, err)
	}

	// get the monitor time
	rmsStatusRecord.MonitorTime, err = helper.ConvertBCDBytesToDateTimeII(byteMsg[43], byteMsg[42], byteMsg[44], byteMsg[41], byteMsg[40], byteMsg[39], httpHeader.GatewayTimezone)
	if err != nil {
		return nil, err
	}

	// inspect the datetime of the monitor -- the data should be sensical; not the overall date reported,
	// but the parts that comprise the date time should be sensical
	rmsStatusRecord.DeviceModel = deviceModel

	// hydrate IsFaulted
	fault := byteMsg[7]
	if fault == 0x00 {
		rmsStatusRecord.IsFaulted = false
		rmsStatusRecord.Fault = "No Fault"
	} else {
		rmsStatusRecord.IsFaulted = true

		// hydrate the fault reason
		faultStatus := uint32(byteMsg[8])
		faultStatus = faultStatus*256 + uint32(byteMsg[9])

		rmsStatusRecord.Fault, rmsStatusRecord.FaultStatus = getFault(fault, faultStatus, int(header.CommVersion))
	}

	// hydrate the Channel Statuses
	greenStatus := uint32(byteMsg[27])
	greenStatus = greenStatus*256 + uint32(byteMsg[26])

	yellowStatus := uint32(byteMsg[29])
	yellowStatus = yellowStatus*256 + uint32(byteMsg[28])

	redStatus := uint32(byteMsg[31])
	redStatus = redStatus*256 + uint32(byteMsg[30])

	for i := 0; i < int(header.MaxChannels); i++ {
		if helper.IsBitSet(greenStatus, i) {
			rmsStatusRecord.ChannelGreenStatus = append(rmsStatusRecord.ChannelGreenStatus, true)
		} else {
			rmsStatusRecord.ChannelGreenStatus = append(rmsStatusRecord.ChannelGreenStatus, false)
		}

		if helper.IsBitSet(yellowStatus, i) {
			rmsStatusRecord.ChannelYellowStatus = append(rmsStatusRecord.ChannelYellowStatus, true)
		} else {
			rmsStatusRecord.ChannelYellowStatus = append(rmsStatusRecord.ChannelYellowStatus, false)
		}

		if helper.IsBitSet(redStatus, i) {
			rmsStatusRecord.ChannelRedStatus = append(rmsStatusRecord.ChannelRedStatus, true)
		} else {
			rmsStatusRecord.ChannelRedStatus = append(rmsStatusRecord.ChannelRedStatus, false)
		}
	}

	// hydrate the temperature
	rmsStatusRecord.Temperature = int64(byteMsg[38]) - 40 // convert to Fahrenheit

	// hydrate the voltages
	var greenVoltages, yellowVoltages, redVoltages [16]int64
	greenVoltages[0] = int64(byteMsg[62])
	greenVoltages[1] = int64(byteMsg[61])
	greenVoltages[2] = int64(byteMsg[60])
	greenVoltages[3] = int64(byteMsg[59])
	greenVoltages[4] = int64(byteMsg[58])
	greenVoltages[5] = int64(byteMsg[57])
	greenVoltages[6] = int64(byteMsg[56])
	greenVoltages[7] = int64(byteMsg[55])
	greenVoltages[8] = int64(byteMsg[54])
	greenVoltages[9] = int64(byteMsg[53])
	greenVoltages[10] = int64(byteMsg[52])
	greenVoltages[11] = int64(byteMsg[51])
	greenVoltages[12] = int64(byteMsg[50])
	greenVoltages[13] = int64(byteMsg[49])
	greenVoltages[14] = int64(byteMsg[48])
	greenVoltages[15] = int64(byteMsg[47])

	yellowVoltages[0] = int64(byteMsg[78])
	yellowVoltages[1] = int64(byteMsg[77])
	yellowVoltages[2] = int64(byteMsg[76])
	yellowVoltages[3] = int64(byteMsg[75])
	yellowVoltages[4] = int64(byteMsg[74])
	yellowVoltages[5] = int64(byteMsg[73])
	yellowVoltages[6] = int64(byteMsg[72])
	yellowVoltages[7] = int64(byteMsg[71])
	yellowVoltages[8] = int64(byteMsg[70])
	yellowVoltages[9] = int64(byteMsg[69])
	yellowVoltages[10] = int64(byteMsg[68])
	yellowVoltages[11] = int64(byteMsg[67])
	yellowVoltages[12] = int64(byteMsg[66])
	yellowVoltages[13] = int64(byteMsg[65])
	yellowVoltages[14] = int64(byteMsg[64])
	yellowVoltages[15] = int64(byteMsg[63])

	redVoltages[0] = int64(byteMsg[94])
	redVoltages[1] = int64(byteMsg[93])
	redVoltages[2] = int64(byteMsg[92])
	redVoltages[3] = int64(byteMsg[91])
	redVoltages[4] = int64(byteMsg[90])
	redVoltages[5] = int64(byteMsg[89])
	redVoltages[6] = int64(byteMsg[88])
	redVoltages[7] = int64(byteMsg[87])
	redVoltages[8] = int64(byteMsg[86])
	redVoltages[9] = int64(byteMsg[85])
	redVoltages[10] = int64(byteMsg[84])
	redVoltages[11] = int64(byteMsg[83])
	redVoltages[12] = int64(byteMsg[82])
	redVoltages[13] = int64(byteMsg[81])
	redVoltages[14] = int64(byteMsg[80])
	redVoltages[15] = int64(byteMsg[79])

	for i := 0; i < 16; i++ {
		if header.Volt220 {
			greenVoltages[i] *= 2
			yellowVoltages[i] *= 2
			redVoltages[i] *= 2
		}

		rmsStatusRecord.VoltagesGreen = append(rmsStatusRecord.VoltagesGreen, greenVoltages[i])
		rmsStatusRecord.VoltagesYellow = append(rmsStatusRecord.VoltagesYellow, yellowVoltages[i])
		rmsStatusRecord.VoltagesRed = append(rmsStatusRecord.VoltagesRed, redVoltages[i])
	}

	return rmsStatusRecord, nil
}

func GetMMUFaultText(fault int, faultStatus int, headerDetail *helper.HeaderRecord) (faultString string, showFaultStatus bool) {
	showFaultStatus = true

	switch fault {
	case 1:
		faultString = "CVM Fault"
		showFaultStatus = false
	case 2:
		faultString = "24V-2 Fault"
		showFaultStatus = false
	case 3:
		faultString = "CVM & 24V-2 Fault"
		showFaultStatus = false
	case 4:
		faultString = "24V-1 Fault"
		showFaultStatus = false
	case 5:
		faultString = "CVM & 24V-1 Fault"
		showFaultStatus = false
	case 6:
		faultString = "24V-1 & 24V-2 Fault"
		showFaultStatus = false
	case 7:
		faultString = "CVM & 24V-1 & 24V-2 Fault"
		showFaultStatus = false
	case 8:
		faultString = "External Watchdog Fault"
		showFaultStatus = false
	case 24:
		faultString = "Program Card Ajar Fault"
		showFaultStatus = false
	case 32:
		faultString = "Conflict Fault"
	case 40:
		faultString = "Red Fail Fault"
	case 48:
		faultString = "Clearance (Short Yellow) Fault"
	case 56:
		faultString = "Clearance (Skipped Yellow) Fault"
	case 64:
		faultString = "Clearance (Yellow + Red) Fault"
	case 72:
		faultString = "Port 1 Fault"
		showFaultStatus = false
	case 80:
		faultString = "Diagnostic Fault"
		switch faultStatus {
		case 33:
			faultString += " (RMS-Engine A/D error)"
		case 40:
			faultString += " (RMS-Engine comm error)"
		case 50:
			faultString += " (EEprom error)"
		case 200:
			faultString += " (Program Card serial path)"
		case 29:
			faultString += " (Switch serial path)"
		case 25:
			faultString += " (24V logic serial path)"
		case 1:
			faultString += " (Program Card memory not found)"
		case 2:
			faultString += " (Program Card memory data error)"
		case 3:
			faultString += " (Program Card memory match error)"
		}
		showFaultStatus = false
	case 88:
		faultString = "Dual Indication Fault"
	case 96:
		faultString = "Field Check Fault"
	case 104:
		faultString = "Type Fault"
		showFaultStatus = false
	case 112:
		faultString = "Local Flash Active"
		showFaultStatus = false
	case 120:
		faultString = "Configuration Change Fault"
		if headerDetail.CommVersion > 0x32 {
			faultString += fmt.Sprintf(" (%d)", faultStatus)
		}
		showFaultStatus = false
	case 136:
		faultString = "Recurrent Pulse Conflict Fault"
	case 144:
		faultString = "Recurrent Pulse Red Fail Fault"
	case 152:
		faultString = "Recurrent Pulse Dual Indication Fault"
	case 160:
		faultString = "48 Vdc Power Supply Fault"
	case 168:
		faultString = "FYA Flash Rate Fault"
	case 176:
		faultString = "Conflict Fault, FYA Yellow Trap"
	case 184:
		faultString = "Recurrent Pulse Conflict Fault, FYA Yellow Trap"
	default:
		faultString = "undefined fault type error"
	}

	return faultString, showFaultStatus
}

func normalizeVoltages(preNormalizedVoltage int, headerDetail *helper.HeaderRecord) (normalizedVoltage int32) {
	normalizedVoltage = int32(preNormalizedVoltage)

	if headerDetail.Volt220 {
		normalizedVoltage *= 2
	}
	if headerDetail.VoltDC {
		normalizedVoltage = normalizedVoltage / 4
	}

	return normalizedVoltage
}

// parse the trace record bytes returning a hydrated trace record parser struct
// this functions takes in a 12 byte slice and deconstructs it into it's corresponding json message
func traceRecordParser(traceBytes []byte, headerDetail *helper.HeaderRecord) (tb *helper.TraceBuffer) {
	const expectedTraceBytesLength = 12 // for mmu2 - each record is 12 bytes
	tb = new(helper.TraceBuffer)
	if len(traceBytes) != expectedTraceBytesLength {
		return nil
	}

	tb.Timestamp = (256 * int64(traceBytes[0])) + int64(traceBytes[1])

	greenStatus := (256 * int64(traceBytes[3])) + int64(traceBytes[2])
	yellowStatus := (256 * int64(traceBytes[5])) + int64(traceBytes[4])
	redStatus := (256 * int64(traceBytes[7])) + int64(traceBytes[6])
	walkStatus := (256*int64(traceBytes[9]) + int64(traceBytes[8]))

	for i := 0; i < int(headerDetail.MaxChannels); i++ {
		tb.Greens = append(tb.Greens, helper.IsBitSet(uint32(greenStatus), i))
		tb.Yellows = append(tb.Yellows, helper.IsBitSet(uint32(yellowStatus), i))
		tb.Reds = append(tb.Reds, helper.IsBitSet(uint32(redStatus), i))
		tb.Walks = append(tb.Walks, helper.IsBitSet(uint32(walkStatus), i))
	}

	tb.EE_SF_RE = traceBytes[10]&0x80 > 0

	tb.AcVoltage = int(traceBytes[11])

	return tb
}

func getFault(fault byte, faultStatus uint32, commVer int) (faultValue string, faultStatusValue string) {
	switch fault {
	case 1:
		faultValue = "CVM Fault"
	case 2:
		faultValue = "24V-2 Fault"
	case 3:
		faultValue = "CVM & 24V-2 Fault"
	case 4:
		faultValue = "24V-1 Fault"
	case 5:
		faultValue = "CVM & 24V-1 Fault"
	case 6:
		faultValue = "24V-1 & 24V-2 Fault"
	case 7:
		faultValue = "CVM & 24V-1 & 24V-2 Fault"
	case 1 * 8:
		faultValue = "External Watchdog Fault"
	case 2 * 8:
		faultValue = "reserved"
	case 3 * 8:
		faultValue = "Program Card Ajar"
	case 4 * 8:
		faultValue = "Conflict Fault"
	case 5 * 8:
		faultValue = "Red Fail Fault"
	case 6 * 8:
		faultValue = "Clearance (Short Yellow) Fault"
	case 7 * 8:
		faultValue = "Clearance (Skipped Yellow) Fault"
	case 8 * 8:
		faultValue = "Red + Yellow Clearance Fault"
	case 9 * 8:
		faultValue = "Port 1 Fail"
	case 10 * 8:
		faultValue = "Diagnostic Fault"
		switch faultStatus {
		case 200:
			faultStatusValue = "(Program card serial path error)"
		case 50:
			faultStatusValue = "(EEprom error)"
		case 40:
			faultStatusValue = "(RMS-Engine comm error)"
		case 33:
			faultStatusValue = "(RMS-Engine data error)"
		case 29:
			faultStatusValue = "(Switch serial path)"
		case 25:
			faultStatusValue = "(24V logic serial path)"
		case 22:
			faultStatusValue = "(trap error)"
		case 1:
			faultStatusValue = "(program card memory not found)"
		case 2:
			faultStatusValue = "(program card memory data error)"
		case 3:
			faultStatusValue = "(program card memory match error)"
		default:
			faultStatusValue = ""
		}
	case 11 * 8:
		faultValue = "Dual Indication Fault"
	case 12 * 8:
		faultValue = "Field Check Fault"
	case 13 * 8:
		faultValue = "Type Change Fault"
	case 14 * 8:
		faultValue = "Local Flash Active"
	case 15 * 8:
		faultValue = "Configuration Change Fault"
		if commVer > 0x32 {
			faultStatusValue = fmt.Sprintf("Check Value = %d", faultStatus)
		}
	case 16 * 8:
		faultValue = "AC Line Low Voltage"
	case 17 * 8:
		faultValue = "Recurrent Pulse Conflict Fault"
	case 18 * 8:
		faultValue = "Recurrent Pulse Red Fail Fault"
	case 19 * 8:
		faultValue = "Recurrent Pulse Dual Indication Fault"
	case 20 * 8:
		faultValue = "48 Vdc Power Supply Fault"
	case 21 * 8:
		faultValue = "FYA Flash Rate Fault"
	case 22 * 8:
		faultValue = "Conflict Fault"
		faultStatusValue = "FYA Yellow Trap"
	case 23 * 8:
		faultValue = "Recurrent Pulse Conflict Fault"
		faultStatusValue = "FYA Yellow Trap"
	default:
		faultValue = "Undefined Fault Type Error"
	}
	return faultValue, faultStatusValue
}

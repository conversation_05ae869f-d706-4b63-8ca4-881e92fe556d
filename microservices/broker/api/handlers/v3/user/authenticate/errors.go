// Package authenticate contains a list of errors that are used by this package.
package authenticate

import (
	"errors"
)

// Error variables for the authenticate package
var (
	// API errors
	ErrAPIVersionNotSupported = errors.New("api version not supported")
	ErrAPIUnauthorized        = errors.New("unauthorized")

	// Database errors
	ErrNoPostgresConnection = errors.New("no Postgres connection found in context")
	ErrDatabaseQuery        = errors.New("database query error")

	// Token errors
	ErrTokenNotFound    = errors.New("token not found or user disabled")
	ErrTokenExpired     = errors.New("token has expired")
	ErrTokenDeletion    = errors.New("error deleting token")
	ErrTokenExpiryCheck = errors.New("error checking token expiration")
	ErrTokenPersistence = errors.New("error persisting token")

	// Role errors
	ErrRoleValidation = errors.New("error validating role")
	ErrInvalidRole    = errors.New("invalid role ID")

	// Access errors
	ErrWebAccessNotEnabled = errors.New("web access not enabled for user")
	ErrWebAccessDenied     = errors.New("user not permitted for web access")

	// JWT errors
	ErrJWTCreation = errors.New("error creating JWT token")

	// User errors
	ErrInvalidCredentials = errors.New("invalid username or password")
	ErrLoginTimeUpdate    = errors.New("error updating login time")
	ErrUserPermissions    = errors.New("error getting user permissions")
	ErrNonceGeneration    = errors.New("error generating nonce")
)

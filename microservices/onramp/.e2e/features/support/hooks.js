const {
  setDefaultTimeout,
  Before,
  After
} = require('@cucumber/cucumber');
const { Builder } = require('selenium-webdriver');
const chrome = require('selenium-webdriver/chrome');

// To get the logging:
const { logging } = require('selenium-webdriver');
const prefs = new logging.Preferences();
prefs.setLevel(logging.Type.BROWSER, logging.Level.ALL);


// 1) Extend Cucumber’s step timeout from 5s to 60s
setDefaultTimeout(60 * 1000);

Before(async function () {
  const options = new chrome.Options()
    .addArguments(
      '--headless',
      '--disable-gpu',
      '--no-sandbox',
      '--window-size=1280,800'
    );

  // Build a single driver instance and attach to `this`
  this.driver = await new Builder()
    .forBrowser('chrome')
    .usingServer(`http://${process.env.SELENIUM_HOST}:${process.env.SELENIUM_PORT}`)
    .setChromeOptions(options)
    .setLoggingPrefs(prefs) // Turn on logging
    .build();

  // Optional: set implicit/pageLoad timeouts
  await this.driver.manage().setTimeouts({
    implicit: 15000,
    pageLoad: 30000,
    script: 30000
  });
});

After(async function () {
  if (this.driver) {
    await this.driver.quit();
  }
});

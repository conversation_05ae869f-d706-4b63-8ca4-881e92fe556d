package authenticate

import (
	"database/sql"

	"github.com/stretchr/testify/mock"
)

// MockDatabaseExecutor mocks the DatabaseExecutor interface
type MockDatabaseExecutor struct {
	mock.Mock
}

// QueryRowStruct mocks the QueryRowStruct method
func (m *MockDatabaseExecutor) QueryRowStruct(dest interface{}, query string, args ...interface{}) error {
	callArgs := m.Called(dest, query, args)
	return callArgs.Error(0)
}

// Exec mocks the Exec method
func (m *MockDatabaseExecutor) Exec(query string, args ...interface{}) (sql.Result, error) {
	callArgs := m.Called(query, args)
	return callArgs.Get(0).(sql.Result), callArgs.Error(1)
}

// Close mocks the Close method
func (m *MockDatabaseExecutor) Close() error {
	return nil
}

// ExecMultiple mocks the ExecMultiple method
func (m *MockDatabaseExecutor) ExecMultiple(queries string) error {
	return nil
}

// QueryGeneric mocks the QueryGeneric method
func (m *MockDatabaseExecutor) QueryGeneric(query string, args ...interface{}) ([]map[string]interface{}, error) {
	return nil, nil
}

// QueryGenericSlice mocks the QueryGenericSlice method
func (m *MockDatabaseExecutor) QueryGenericSlice(dest interface{}, query string, args ...interface{}) error {
	return nil
}

// QueryRow mocks the QueryRow method
func (m *MockDatabaseExecutor) QueryRow(query string, args ...interface{}) (map[string]interface{}, error) {
	return nil, nil
}

// EscapeIdentifier mocks the EscapeIdentifier method
func (m *MockDatabaseExecutor) EscapeIdentifier(identifier string) string {
	return identifier
}

// ReplaceNamespace mocks the ReplaceNamespace method
func (m *MockDatabaseExecutor) ReplaceNamespace(query string) string {
	return query
}

// MockSQLResult mocks sql.Result
type MockSQLResult struct {
	mock.Mock
}

// LastInsertId mocks the LastInsertId method
func (m *MockSQLResult) LastInsertId() (int64, error) {
	args := m.Called()
	return args.Get(0).(int64), args.Error(1)
}

// RowsAffected mocks the RowsAffected method
func (m *MockSQLResult) RowsAffected() (int64, error) {
	args := m.Called()
	return args.Get(0).(int64), args.Error(1)
}

// fakeResult implements sql.Result for testing.
type fakeResult struct{}

// LastInsertId returns a dummy last insert ID.
func (r fakeResult) LastInsertId() (int64, error) { return 0, nil }

// RowsAffected returns a dummy number of affected rows.
func (r fakeResult) RowsAffected() (int64, error) { return 1, nil }

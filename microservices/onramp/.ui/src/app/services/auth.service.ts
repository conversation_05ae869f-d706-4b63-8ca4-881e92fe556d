// src/app/services/auth.service.ts
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

export interface UserProfile {
  sub: string;
  email: string;
  name?: string;
}

@Injectable({ providedIn: 'root' })
export class AuthService {
  constructor(private http: HttpClient) { }

  /** 
   * Hit the Go API’s /api/profile endpoint.
   * Returns 200 + UserProfile if logged in,
   * or errors (401) if not.
   */
  getProfile(): Observable<UserProfile> {
    return this.http.get<UserProfile>('/protected/profile');
  }

  /** Kick off a login by redirecting to your Go server. */
  login(): void {
    window.location.href = '/login';
  }

  /** Kick off a logout by hitting your Go logout route. */
  logout(): void {
    window.location.href = '/logout';
  }
}

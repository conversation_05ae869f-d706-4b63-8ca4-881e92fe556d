#!/usr/bin/env bash
# Description: List tables in the dataset, scoped to the namespace or all
#   bqe ls [-a|--all]
#   bqe ls
#     List only tables in the current namespace.
#   bqe ls -a|--all
#     List all tables, regardless of namespace.

set -euo pipefail

# 1) detect -a/--all
all=false
if [[ "${1:-}" == "-a" || "${1:-}" == "--all" ]]; then
  all=true
fi

# Get the dataset name from the environment or configuration
dataset=$(bqe-dataset)
if [[ -z "$dataset" ]]; then
  echo "Error: no dataset set. Run 'bqe dataset' to list or 'bqe dataset <name>' to set one." >&2
  exit 1
fi

# 2) if --all, just dump everything (preserves prefix + alignment)
if $all; then
  bq ls "$dataset"
  exit 0
fi

# 3) otherwise we need a namespace
ns="$(bqe-get namespace)"
if [[ -z "$ns" ]]; then
  echo "Error: no namespace set. Run 'bqe namespace' to list or 'bqe namespace <name>' to set one." >&2
  exit 1
fi

# 4) build a “pad” of spaces the same length as “<ns>__”
prefix="${ns}__"
pad="$(printf '%*s' "${#prefix}" "")"

bq ls "$dataset" | sed -n \
  -e '1,2p' \
  -e '/^[[:space:]]*'"$prefix"'/ {
    # 1) capture indent, 2) consume prefix, 3) capture table name, 4) capture the rest
    s/^\([[:space:]]*\)'"$prefix"'\([^ ]*\)\(.*\)/\1\2'"$pad"'\3/
    p
  }'

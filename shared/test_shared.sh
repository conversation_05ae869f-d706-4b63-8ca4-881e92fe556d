#!/bin/bash

# Determine the directory where this script resides.
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

# Loop over every immediate subdirectory relative to the script directory.
for dir in "$SCRIPT_DIR"/*/; do
  # Check if a go.mod file exists in the subdirectory.
  if [ -f "${dir}go.mod" ]; then
    #echo "Running tests in ${dir}"
    (cd "${dir}" && go test -coverprofile=coverage.out ./...)
  fi
done

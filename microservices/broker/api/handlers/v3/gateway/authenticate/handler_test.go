package authenticate

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	security "synapse-its.com/shared/api/security"
	softwareGateway "synapse-its.com/shared/api/softwaregateway"
	connect "synapse-its.com/shared/connect"
	mocks "synapse-its.com/shared/mocks"
)

// fakeResult implements sql.Result for testing.
type fakeResult struct{}

func (r fakeResult) LastInsertId() (int64, error) { return 0, nil }
func (r fakeResult) RowsAffected() (int64, error) { return 1, nil }

// --- Store Original Functions to Enable Override ---
var (
	origParseRequest       = parseRequest
	origGetGatewayInfo     = getGatewayInfo
	origValidateToken      = validateToken
	origGetGatewayConfig   = getGatewayConfig
	origGetFirebaseConfig  = getFirebaseConfig
	origGetDeviceSettings  = getDeviceSettings
	origSetNewGatewayToken = setNewGatewayToken
	origGetJWTAsymmetric   = security.GetJWTAsymmetric
)

// restoreOverrides resets all package‑level function variables after each test.
func restoreOverrides() {
	parseRequest = origParseRequest
	getGatewayInfo = origGetGatewayInfo
	validateToken = origValidateToken
	getGatewayConfig = origGetGatewayConfig
	getFirebaseConfig = origGetFirebaseConfig
	getDeviceSettings = origGetDeviceSettings
	setNewGatewayToken = origSetNewGatewayToken
	security.GetJWTAsymmetric = origGetJWTAsymmetric
}

// --- Tests for Handler ---

func TestHandler_ParseRequestError(t *testing.T) {
	defer restoreOverrides()
	parseRequest = func(r *http.Request) (string, string, error) {
		return "", "", errors.New("parse error")
	}

	req, err := http.NewRequest("POST", "/authenticate", bytes.NewBuffer([]byte(`{"token":"abc"}`)))
	if err != nil {
		t.Fatal(err)
	}
	req.Header.Set("gateway-device-id", "gw-123")
	req.Header.Set("message-type", "authenticate")
	req = req.WithContext(connect.WithConnections(context.Background(), mocks.FakeConns()))

	rr := httptest.NewRecorder()
	Handler(rr, req)
	if rr.Code != http.StatusUnauthorized {
		t.Errorf("Expected HTTP %d Unauthorized due to parse error, got %d", http.StatusUnauthorized, rr.Code)
	}
}

// Test when getGatewayInfo returns an error.
func TestHandler_GetGatewayInfoError(t *testing.T) {
	defer restoreOverrides()
	parseRequest = func(r *http.Request) (string, string, error) {
		return "gw-missing", "abc", nil
	}
	getGatewayInfo = func(pg connect.DatabaseExecutor, gatewayId string) (*dbGatewayInfo, error) {
		return &dbGatewayInfo{}, errors.New("gateway info not found")
	}

	req, err := http.NewRequest("POST", "/authenticate", bytes.NewBuffer([]byte(`{"token":"abc"}`)))
	if err != nil {
		t.Fatal(err)
	}
	req.Header.Set("gateway-device-id", "gw-missing")
	req.Header.Set("message-type", "authenticate")
	req = req.WithContext(connect.WithConnections(context.Background(), mocks.FakeConns()))

	rr := httptest.NewRecorder()
	Handler(rr, req)
	if rr.Code != http.StatusUnauthorized {
		t.Errorf("Expected HTTP %d Unauthorized when getGatewayInfo fails, got %d", http.StatusUnauthorized, rr.Code)
	}
}

// Test when validateToken fails (i.e. token mismatch).
func TestHandler_InvalidToken(t *testing.T) {
	defer restoreOverrides()
	parseRequest = func(r *http.Request) (string, string, error) {
		return "gw-123", "wrong", nil
	}
	getGatewayInfo = func(pg connect.DatabaseExecutor, gatewayId string) (*dbGatewayInfo, error) {
		return &dbGatewayInfo{
			OrganizationIdentifier: "org1", APIKey: "key", Token: "abc", Id: 123,
		}, nil
	}
	validateToken = func(dbToken string, rqToken string) error {
		return errors.New("token mismatch")
	}

	req, err := http.NewRequest("POST", "/authenticate", bytes.NewBuffer([]byte(`{"token":"wrong"}`)))
	if err != nil {
		t.Fatal(err)
	}
	req.Header.Set("gateway-device-id", "gw-123")
	req.Header.Set("message-type", "authenticate")
	req = req.WithContext(connect.WithConnections(context.Background(), mocks.FakeConns()))

	rr := httptest.NewRecorder()
	Handler(rr, req)
	if rr.Code != http.StatusUnauthorized {
		t.Errorf("Expected HTTP %d Unauthorized for token mismatch, got %d", http.StatusUnauthorized, rr.Code)
	}
}

// Test when getGatewayConfig returns an error.
func TestHandler_GetGatewayConfigError(t *testing.T) {
	defer restoreOverrides()
	parseRequest = func(r *http.Request) (string, string, error) {
		return "gw-123", "abc", nil
	}
	getGatewayInfo = func(pg connect.DatabaseExecutor, gatewayId string) (*dbGatewayInfo, error) {
		return &dbGatewayInfo{
			OrganizationIdentifier: "org1", APIKey: "key", Token: "abc", Id: 123,
		}, nil
	}
	validateToken = func(dbToken, rqToken string) error { return nil }
	getGatewayConfig = func(pg connect.DatabaseExecutor, softwareGatewayId int64) (*softwareGateway.GatewaySettings, error) {
		return &softwareGateway.GatewaySettings{}, errors.New("gateway config error")
	}

	req, err := http.NewRequest("POST", "/authenticate", bytes.NewBuffer([]byte(`{"token":"abc"}`)))
	if err != nil {
		t.Fatal(err)
	}
	req.Header.Set("gateway-device-id", "gw-123")
	req.Header.Set("message-type", "authenticate")
	req = req.WithContext(connect.WithConnections(context.Background(), mocks.FakeConns()))

	rr := httptest.NewRecorder()
	Handler(rr, req)
	if rr.Code != http.StatusInternalServerError {
		t.Errorf("Expected HTTP %d InternalError due to gateway config error, got %d", http.StatusInternalServerError, rr.Code)
	}
}

// Test when getFirebaseConfig returns an error.
func TestHandler_GetFirebaseConfigError(t *testing.T) {
	defer restoreOverrides()
	parseRequest = func(r *http.Request) (string, string, error) {
		return "gw-123", "abc", nil
	}
	getGatewayInfo = func(pg connect.DatabaseExecutor, gatewayId string) (*dbGatewayInfo, error) {
		return &dbGatewayInfo{
			OrganizationIdentifier: "org1", APIKey: "key", Token: "abc", Id: 123,
		}, nil
	}
	validateToken = func(dbToken, rqToken string) error { return nil }
	getGatewayConfig = func(pg connect.DatabaseExecutor, softwareGatewayId int64) (*softwareGateway.GatewaySettings, error) {
		return &softwareGateway.GatewaySettings{}, nil
	}
	getFirebaseConfig = func(pg connect.DatabaseExecutor) (*string, error) {
		return nil, errors.New("firebase config error")
	}

	req, err := http.NewRequest("POST", "/authenticate", bytes.NewBuffer([]byte(`{"token":"abc"}`)))
	if err != nil {
		t.Fatal(err)
	}
	req.Header.Set("gateway-device-id", "gw-123")
	req.Header.Set("message-type", "authenticate")
	req = req.WithContext(connect.WithConnections(context.Background(), mocks.FakeConns()))

	rr := httptest.NewRecorder()
	Handler(rr, req)
	if rr.Code != http.StatusInternalServerError {
		t.Errorf("Expected HTTP %d InternalError due to firebase config error, got %d", http.StatusInternalServerError, rr.Code)
	}
}

// Test when GetJWTAsymmetric returns an error.
func TestHandler_GetJWTError(t *testing.T) {
	defer restoreOverrides()
	parseRequest = func(r *http.Request) (string, string, error) {
		return "gw-123", "abc", nil
	}
	getGatewayInfo = func(pg connect.DatabaseExecutor, gatewayId string) (*dbGatewayInfo, error) {
		return &dbGatewayInfo{
			OrganizationIdentifier: "org1", APIKey: "key", Token: "abc", Id: 123,
		}, nil
	}
	validateToken = func(dbToken, rqToken string) error { return nil }
	getGatewayConfig = func(pg connect.DatabaseExecutor, softwareGatewayId int64) (*softwareGateway.GatewaySettings, error) {
		return &softwareGateway.GatewaySettings{}, nil
	}
	getFirebaseConfig = func(pg connect.DatabaseExecutor) (*string, error) {
		config := "firebase-config"
		return &config, nil
	}
	// Override GetJWTAsymmetric to return an error.
	security.GetJWTAsymmetric = func() (string, string, error) {
		return "", "", errors.New("JWT error")
	}

	req, err := http.NewRequest("POST", "/authenticate", bytes.NewBuffer([]byte(`{"token":"abc"}`)))
	if err != nil {
		t.Fatal(err)
	}
	req.Header.Set("gateway-device-id", "gw-123")
	req.Header.Set("message-type", "authenticate")
	req = req.WithContext(connect.WithConnections(context.Background(), mocks.FakeConns()))

	rr := httptest.NewRecorder()
	Handler(rr, req)
	if rr.Code != http.StatusInternalServerError {
		t.Errorf("Expected HTTP %d InternalError due to GetJWTAsymmetric error, got %d", http.StatusInternalServerError, rr.Code)
	}
}

// Test when getDeviceSettings returns an error.
func TestHandler_GetDeviceSettingsError(t *testing.T) {
	defer restoreOverrides()
	parseRequest = func(r *http.Request) (string, string, error) {
		return "gw-123", "abc", nil
	}
	getGatewayInfo = func(pg connect.DatabaseExecutor, gatewayId string) (*dbGatewayInfo, error) {
		return &dbGatewayInfo{
			OrganizationIdentifier: "org1", APIKey: "key", Token: "abc", Id: 123,
		}, nil
	}
	validateToken = func(dbToken, rqToken string) error { return nil }
	getGatewayConfig = func(pg connect.DatabaseExecutor, softwareGatewayId int64) (*softwareGateway.GatewaySettings, error) {
		return &softwareGateway.GatewaySettings{}, nil
	}
	getFirebaseConfig = func(pg connect.DatabaseExecutor) (*string, error) {
		config := "firebase-config"
		return &config, nil
	}
	security.GetJWTAsymmetric = func() (string, string, error) {
		return "private", "public", nil
	}
	getDeviceSettings = func(pg connect.DatabaseExecutor, dbSoftwareGatewayId int64) (*[]softwareGateway.DeviceSettings, error) {
		return nil, errors.New("device settings error")
	}

	req, err := http.NewRequest("POST", "/authenticate", bytes.NewBuffer([]byte(`{"token":"abc"}`)))
	if err != nil {
		t.Fatal(err)
	}
	req.Header.Set("gateway-device-id", "gw-123")
	req.Header.Set("message-type", "authenticate")
	req = req.WithContext(connect.WithConnections(context.Background(), mocks.FakeConns()))

	rr := httptest.NewRecorder()
	Handler(rr, req)
	if rr.Code != http.StatusInternalServerError {
		t.Errorf("Expected HTTP %d InternalError due to device settings error, got %d", http.StatusInternalServerError, rr.Code)
	}
}

// Test when setNewGatewayToken returns an error.
func TestHandler_SetNewGatewayTokenError(t *testing.T) {
	defer restoreOverrides()
	parseRequest = func(r *http.Request) (string, string, error) {
		return "gw-123", "abc", nil
	}
	getGatewayInfo = func(pg connect.DatabaseExecutor, gatewayId string) (*dbGatewayInfo, error) {
		return &dbGatewayInfo{
			OrganizationIdentifier: "org1", APIKey: "key", Token: "abc", Id: 123,
		}, nil
	}
	validateToken = func(dbToken, rqToken string) error { return nil }
	getGatewayConfig = func(pg connect.DatabaseExecutor, softwareGatewayId int64) (*softwareGateway.GatewaySettings, error) {
		return &softwareGateway.GatewaySettings{}, nil
	}
	getFirebaseConfig = func(pg connect.DatabaseExecutor) (*string, error) {
		config := "firebase-config"
		return &config, nil
	}
	security.GetJWTAsymmetric = func() (string, string, error) {
		return "private", "public", nil
	}
	getDeviceSettings = func(pg connect.DatabaseExecutor, dbSoftwareGatewayId int64) (*[]softwareGateway.DeviceSettings, error) {
		return &[]softwareGateway.DeviceSettings{
			{Device_ID: "dev1"},
		}, nil
	}
	setNewGatewayToken = func(pg connect.DatabaseExecutor, gatewayId string) (*softwareGateway.CloudSettings, error) {
		return nil, errors.New("set token error")
	}

	req, err := http.NewRequest("POST", "/authenticate", bytes.NewBuffer([]byte(`{"token":"abc"}`)))
	if err != nil {
		t.Fatal(err)
	}
	req.Header.Set("gateway-device-id", "gw-123")
	req.Header.Set("message-type", "authenticate")
	req = req.WithContext(connect.WithConnections(context.Background(), mocks.FakeConns()))

	rr := httptest.NewRecorder()
	Handler(rr, req)
	if rr.Code != http.StatusInternalServerError {
		t.Errorf("Expected HTTP %d InternalError due to setNewGatewayToken error, got %d", http.StatusInternalServerError, rr.Code)
	}
}

// Test when the Postgres connection is nil.
func TestHandler_GetConnectionsError(t *testing.T) {
	defer restoreOverrides()
	parseRequest = func(r *http.Request) (string, string, error) {
		return "gw-123", "abc", nil
	}

	req, err := http.NewRequest("POST", "/authenticate", bytes.NewBuffer([]byte(`{"token":"abc"}`)))
	if err != nil {
		t.Fatal(err)
	}
	req.Header.Set("gateway-device-id", "gw-123")
	req.Header.Set("message-type", "authenticate")

	// Don't set connections

	rr := httptest.NewRecorder()
	Handler(rr, req)
	if rr.Code != http.StatusInternalServerError {
		t.Errorf("Expected HTTP %d InternalError due to nil Postgres, got %d", http.StatusInternalServerError, rr.Code)
	}
}

// Test when the Postgres connection is nil.
func TestHandler_NilPostgres(t *testing.T) {
	defer restoreOverrides()
	parseRequest = func(r *http.Request) (string, string, error) {
		return "gw-123", "abc", nil
	}

	req, err := http.NewRequest("POST", "/authenticate", bytes.NewBuffer([]byte(`{"token":"abc"}`)))
	if err != nil {
		t.Fatal(err)
	}
	req.Header.Set("gateway-device-id", "gw-123")
	req.Header.Set("message-type", "authenticate")

	// Create a connections object with Postgres set to nil.
	conns := &connect.Connections{
		Postgres: nil,
	}
	req = req.WithContext(connect.WithConnections(context.Background(), conns))

	rr := httptest.NewRecorder()
	Handler(rr, req)
	if rr.Code != http.StatusInternalServerError {
		t.Errorf("Expected HTTP %d InternalError due to nil Postgres, got %d", http.StatusInternalServerError, rr.Code)
	}
}

// Test the full successful flow.
func TestHandler_Success(t *testing.T) {
	defer restoreOverrides()
	// Override all dependencies to simulate a successful authentication.
	parseRequest = func(r *http.Request) (string, string, error) {
		return "gw-123", "abc", nil
	}
	getGatewayInfo = func(pg connect.DatabaseExecutor, gatewayId string) (*dbGatewayInfo, error) {
		return &dbGatewayInfo{
			OrganizationIdentifier: "org1", APIKey: "key", Token: "abc", Id: 123,
		}, nil
	}
	validateToken = func(dbToken, rqToken string) error { return nil }
	getGatewayConfig = func(pg connect.DatabaseExecutor, softwareGatewayId int64) (*softwareGateway.GatewaySettings, error) {
		// For testing, assume a gateway setting with a dummy value.
		gs := &softwareGateway.GatewaySettings{}
		gs.ApplicationVersion = "value" // adjust this field as necessary
		return gs, nil
	}
	getFirebaseConfig = func(pg connect.DatabaseExecutor) (*string, error) {
		config := "firebase-config"
		return &config, nil
	}
	security.GetJWTAsymmetric = func() (string, string, error) {
		return "private", "public", nil
	}
	getDeviceSettings = func(pg connect.DatabaseExecutor, dbSoftwareGatewayId int64) (*[]softwareGateway.DeviceSettings, error) {
		return &[]softwareGateway.DeviceSettings{
			{Device_ID: "dev1"},
		}, nil
	}
	setNewGatewayToken = func(pg connect.DatabaseExecutor, gatewayId string) (*softwareGateway.CloudSettings, error) {
		return &softwareGateway.CloudSettings{Token: "new-token"}, nil
	}

	req, err := http.NewRequest("POST", "/authenticate", bytes.NewBuffer([]byte(`{"token":"abc"}`)))
	if err != nil {
		t.Fatal(err)
	}
	req.Header.Set("gateway-device-id", "gw-123")
	req.Header.Set("message-type", "authenticate")
	req = req.WithContext(connect.WithConnections(context.Background(), mocks.FakeConns()))

	rr := httptest.NewRecorder()
	Handler(rr, req)
	if rr.Code != http.StatusOK {
		t.Errorf("Expected HTTP %d OK on success, got %d", http.StatusOK, rr.Code)
	}

	// The response envelope is created using CreateSuccessResponse.
	// Define a response type matching that envelope.
	type SuccessResponse struct {
		Status  string                         `json:"status"`
		Data    softwareGateway.GlobalSettings `json:"data"`
		Message string                         `json:"message"`
		Code    int                            `json:"code"`
	}

	var resp SuccessResponse
	if err := json.Unmarshal(rr.Body.Bytes(), &resp); err != nil {
		t.Fatalf("Failed to unmarshal response: %v", err)
	}
	if resp.Status != "success" {
		t.Errorf("Expected status 'success', got %s", resp.Status)
	}
	if resp.Message != "Request Succeeded" {
		t.Errorf("Expected message 'Request Succeeded', got %s", resp.Message)
	}
	if resp.Code != http.StatusOK {
		t.Errorf("Expected code %d, got %d", http.StatusOK, resp.Code)
	}
	// Verify the global settings in the response.
	gs := resp.Data
	if gs.OrganizationId != "org1" {
		t.Errorf("Expected OrganizationId 'org1', got %s", gs.OrganizationId)
	}
	if gs.PublicKey != "public" {
		t.Errorf("Expected PublicKey 'public', got %s", gs.PublicKey)
	}
	if gs.AWS.Token != "new-token" {
		t.Errorf("Expected CloudSettings Token 'new-token', got %s", gs.AWS.Token)
	}
	if len(gs.Devices) != 1 {
		t.Errorf("Expected 1 device setting, got %d", len(gs.Devices))
	}
}

// --- Tests for parseRequest ---

func TestParseRequest_MissingGatewayDeviceID(t *testing.T) {
	body := `{"token": "abc123"}`
	req := httptest.NewRequest("POST", "/", strings.NewReader(body))
	req.Header.Set("message-type", "authenticate")
	_, _, err := parseRequest(req)
	if err == nil || err.Error() != "header gateway-device-id is not present" {
		t.Errorf("Expected error for missing gateway-device-id, got: %v", err)
	}
}

func TestParseRequest_MissingMessageType(t *testing.T) {
	body := `{"token": "abc123"}`
	req := httptest.NewRequest("POST", "/", strings.NewReader(body))
	req.Header.Set("gateway-device-id", "gateway-001")
	_, _, err := parseRequest(req)
	if err == nil || err.Error() != "header message-type is not present" {
		t.Errorf("Expected error for missing message-type, got: %v", err)
	}
}

func TestParseRequest_InvalidMessageType(t *testing.T) {
	body := `{"token": "abc123"}`
	req := httptest.NewRequest("POST", "/", strings.NewReader(body))
	req.Header.Set("gateway-device-id", "gateway-001")
	req.Header.Set("message-type", "not-authenticate")
	_, _, err := parseRequest(req)
	if err == nil || err.Error() != "header message-type != authenticate" {
		t.Errorf("Expected error for invalid message-type, got: %v", err)
	}
}

func TestParseRequest_InvalidJSONBody(t *testing.T) {
	body := `not a json`
	req := httptest.NewRequest("POST", "/", strings.NewReader(body))
	req.Header.Set("gateway-device-id", "gateway-001")
	req.Header.Set("message-type", "authenticate")
	_, _, err := parseRequest(req)
	if err == nil {
		t.Error("Expected error for invalid JSON body, got nil")
	}
}

func TestParseRequest_MissingTokenInBody(t *testing.T) {
	body := `{"notToken": "abc"}`
	req := httptest.NewRequest("POST", "/", strings.NewReader(body))
	req.Header.Set("gateway-device-id", "gateway-001")
	req.Header.Set("message-type", "authenticate")
	_, _, err := parseRequest(req)
	if err == nil || err.Error() != "token does not exist in the body" {
		t.Errorf("Expected error for missing token in body, got: %v", err)
	}
}

func TestParseRequest_Success(t *testing.T) {
	const tokenVal = "abc123"
	body := `{"token": "` + tokenVal + `"}`
	req := httptest.NewRequest("POST", "/", strings.NewReader(body))
	req.Header.Set("gateway-device-id", "gateway-001")
	req.Header.Set("message-type", "authenticate")
	gatewayID, token, err := parseRequest(req)
	if err != nil {
		t.Errorf("Expected no error, got: %v", err)
	}
	if gatewayID != "gateway-001" {
		t.Errorf("Expected gateway-device-id %q, got %q", "gateway-001", gatewayID)
	}
	if token != tokenVal {
		t.Errorf("Expected token %q, got %q", tokenVal, token)
	}
}

// --- Test for validateToken ---

func TestValidateToken_NonEmptyMismatch(t *testing.T) {
	err := validateToken("dbToken", "requestToken")
	if err == nil || !strings.Contains(err.Error(), "token presented by gateway does not match") {
		t.Errorf("Expected token mismatch error, got: %v", err)
	}
}

func TestValidateToken_DBTokenEmpty(t *testing.T) {
	err := validateToken("", "anything")
	if err != nil {
		t.Errorf("Expected no error when dbToken is empty, got: %v", err)
	}
}

func TestValidateToken_Match(t *testing.T) {
	err := validateToken("sameToken", "sameToken")
	if err != nil {
		t.Errorf("Expected no error when tokens match, got: %v", err)
	}
}

// --- Tests for getGatewayInfo ---

func TestGetGatewayInfo_Success(t *testing.T) {
	gatewayID := "gateway-001"
	queryResults := &dbGatewayInfo{
		Id:                     123,
		OrganizationIdentifier: "org-001",
		APIKey:                 "api-key-value",
		Token:                  "dbtoken",
	}
	fakeDB := &mocks.FakeDBExecutor{
		QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
			db, ok := dest.(*dbGatewayInfo)
			if !ok {
				return errors.New("dest must be of type *dbGatewayInfo")
			}
			*db = *queryResults
			return nil
		},
	}

	gatewayInfo, err := getGatewayInfo(fakeDB, gatewayID)
	if err != nil {
		t.Fatalf("Expected no error, got: %v", err)
	}
	if gatewayInfo.Id != 123 {
		t.Errorf("Expected id 123, got %v", gatewayInfo.Id)
	}
	if gatewayInfo.OrganizationIdentifier != "org-001" {
		t.Errorf("Expected org 'org-001', got %v", gatewayInfo.OrganizationIdentifier)
	}
	if gatewayInfo.APIKey != "api-key-value" {
		t.Errorf("Expected apiKey 'api-key-value', got %v", gatewayInfo.APIKey)
	}
	if gatewayInfo.Token != "dbtoken" {
		t.Errorf("Expected dbToken 'dbtoken', got %v", gatewayInfo.Token)
	}
}

func TestGetGatewayInfo_NoRows(t *testing.T) {
	gatewayID := "unknown-gateway"
	fakeDB := &mocks.FakeDBExecutor{
		QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
			return sql.ErrNoRows
		},
	}

	_, err := getGatewayInfo(fakeDB, gatewayID)
	if err == nil || !strings.Contains(err.Error(), "was not found") {
		t.Errorf("Expected error for missing gateway, got: %v", err)
	}
}

func TestGetGatewayInfo_GenericQueryError(t *testing.T) {
	gatewayID := "unknown-gateway"
	fakeDB := &mocks.FakeDBExecutor{
		QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
			return errors.New("Generic error non-sql.norows error")
		},
	}

	_, err := getGatewayInfo(fakeDB, gatewayID)
	if err == nil || !strings.Contains(err.Error(), "Generic error") {
		t.Errorf("Expected error for Generic error, got: %v", err)
	}
}

// ---------- Tests for getGatewayConfig ----------

func TestGetGatewayConfig_AllFields(t *testing.T) {
	queryResults := &dbConfig{
		Value: `{
			"application_version": "1.0.0",
			"record_http_requests_to_folder": true,
			"device_state_send_frequency_seconds": 60,
			"channel_state_send_frequency_milliseconds": 1000,
			"software_update_check_frequency_seconds": 120,
			"gateway_performance_stats_output_frequency_seconds": 30,
			"edi_device_processing_retries": 3,
			"edi_device_persist_connection": true,
			"rest_api_device_endpoint": "http://localhost:8080",
			"log_filename": "gateway.log",
			"log_file_max_size_mb": 10,
			"log_max_backups": 5,
			"log_max_age_in_days": 7,
			"log_compress_backups": true,
			"config_change_check_frequency_seconds": 300,
			"send_gateway_logs_to_cloud": true,
			"send_gateway_performance_stats_to_cloud": false,
			"log_level": "info",
			"ws_active": true,
			"ws_port": "9000",
			"ws_endpoint": "/ws",
			"ws_max_connections": 100,
			"ws_send_frequency_milliseconds": 50,
			"ws_heartbeat_send_frequency_milliseconds": 5000,
			"threshold_device_error_seconds": 10
		}`,
	}
	softwareGatewayID := int64(456)
	fakeDB := &mocks.FakeDBExecutor{
		QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
			db, ok := dest.(*dbConfig)
			if !ok {
				return errors.New("dest must be of type *dbConfig")
			}
			*db = *queryResults
			return nil
		},
	}

	settings, err := getGatewayConfig(fakeDB, softwareGatewayID)
	if err != nil {
		t.Fatalf("Expected no error, got: %v", err)
	}

	if settings.ApplicationVersion != "1.0.0" {
		t.Errorf("Expected ApplicationVersion '1.0.0', got %q", settings.ApplicationVersion)
	}
	if settings.RecordHttpRequestsToLogFolder != true {
		t.Errorf("Expected RecordHttpRequestsToLogFolder true, got %v", settings.RecordHttpRequestsToLogFolder)
	}
	if settings.DeviceStateSendFrequencySeconds != 60 {
		t.Errorf("Expected DeviceStateSendFrequencySeconds 60, got %d", settings.DeviceStateSendFrequencySeconds)
	}
	if settings.ChannelStateSendFrequencyMilliseconds != 1000 {
		t.Errorf("Expected ChannelStateSendFrequencyMilliseconds 1000, got %d", settings.ChannelStateSendFrequencyMilliseconds)
	}
	if settings.SoftwareUpdateCheckFrequencySeconds != 120 {
		t.Errorf("Expected SoftwareUpdateCheckFrequencySeconds 120, got %d", settings.SoftwareUpdateCheckFrequencySeconds)
	}
	if settings.GatewayPerformanceStatsOutputFrequencySeconds != 30 {
		t.Errorf("Expected GatewayPerformanceStatsOutputFrequencySeconds 30, got %d", settings.GatewayPerformanceStatsOutputFrequencySeconds)
	}
	if settings.EdiDeviceProcessingRetries != 3 {
		t.Errorf("Expected EdiDeviceProcessingRetries 3, got %d", settings.EdiDeviceProcessingRetries)
	}
	if settings.EdiDevicePersistConnection != true {
		t.Errorf("Expected EdiDevicePersistConnection true, got %v", settings.EdiDevicePersistConnection)
	}
	if settings.RestAPIDeviceEndpoint != "http://localhost:8080" {
		t.Errorf("Expected RestAPIDeviceEndpoint 'http://localhost:8080', got %q", settings.RestAPIDeviceEndpoint)
	}
	if settings.LogFilename != "gateway.log" {
		t.Errorf("Expected LogFilename 'gateway.log', got %q", settings.LogFilename)
	}
	if settings.LogFileMaxSizeMB != 10 {
		t.Errorf("Expected LogFileMaxSizeMB 10, got %d", settings.LogFileMaxSizeMB)
	}
	if settings.LogMaxBackups != 5 {
		t.Errorf("Expected LogMaxBackups 5, got %d", settings.LogMaxBackups)
	}
	if settings.LogMaxAgeInDays != 7 {
		t.Errorf("Expected LogMaxAgeInDays 7, got %d", settings.LogMaxAgeInDays)
	}
	if settings.LogCompressBackups != true {
		t.Errorf("Expected LogCompressBackups true, got %v", settings.LogCompressBackups)
	}
	if settings.ConfigChangeCheckFrequencySeconds != 300 {
		t.Errorf("Expected ConfigChangeCheckFrequencySeconds 300, got %d", settings.ConfigChangeCheckFrequencySeconds)
	}
	if settings.SendGatewayLogsToCloud != true {
		t.Errorf("Expected SendGatewayLogsToCloud true, got %v", settings.SendGatewayLogsToCloud)
	}
	if settings.SendGatewayPerformanceStatsToCloud != false {
		t.Errorf("Expected SendGatewayPerformanceStatsToCloud false, got %v", settings.SendGatewayPerformanceStatsToCloud)
	}
	if settings.LogLevel != "info" {
		t.Errorf("Expected LogLevel 'info', got %q", settings.LogLevel)
	}
	if settings.WebSocketActive != true {
		t.Errorf("Expected WebSocketActive true, got %v", settings.WebSocketActive)
	}
	if settings.WebSocketPort != "9000" {
		t.Errorf("Expected WebSocketPort '9000', got %q", settings.WebSocketPort)
	}
	if settings.WebSocketEndPoint != "/ws" {
		t.Errorf("Expected WebSocketEndPoint '/ws', got %q", settings.WebSocketEndPoint)
	}
	if settings.WebSocketMaxConnections != 100 {
		t.Errorf("Expected WebSocketMaxConnections 100, got %d", settings.WebSocketMaxConnections)
	}
	if settings.WebSocketSendFrequencyMilliseconds != 50 {
		t.Errorf("Expected WebSocketSendFrequencyMilliseconds 50, got %d", settings.WebSocketSendFrequencyMilliseconds)
	}
	if settings.WebSocketHeartbeatSendFrequencyMilliseconds != 5000 {
		t.Errorf("Expected WebSocketHeartbeatSendFrequencyMilliseconds 5000, got %d", settings.WebSocketHeartbeatSendFrequencyMilliseconds)
	}
	if settings.ThresholdDeviceErrorSeconds != 10 {
		t.Errorf("Expected ThresholdDeviceErrorSeconds 10, got %d", settings.ThresholdDeviceErrorSeconds)
	}
}

func TestGetGatewayConfig_InvalidJSON(t *testing.T) {
	queryResults := &dbConfig{
		Value: `not a json`,
	}
	softwareGatewayID := int64(456)
	fakeDB := &mocks.FakeDBExecutor{
		QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
			db, ok := dest.(*dbConfig)
			if !ok {
				return errors.New("dest must be of type *dbConfig")
			}
			*db = *queryResults
			return nil
		},
	}

	_, err := getGatewayConfig(fakeDB, softwareGatewayID)
	if err == nil {
		t.Error("Expected error when JSON is invalid, got nil")
	}
}

func TestGetGatewayConfig_NoRows(t *testing.T) {
	softwareGatewayID := int64(456)
	fakeDB := &mocks.FakeDBExecutor{
		QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
			return sql.ErrNoRows
		},
	}

	_, err := getGatewayConfig(fakeDB, softwareGatewayID)
	if err == nil || !strings.Contains(err.Error(), "was not found") {
		t.Errorf("Expected error for missing gateway, got: %v", err)
	}
}

func TestGetGatewayConfig_GenericQueryError(t *testing.T) {
	softwareGatewayID := int64(456)
	fakeDB := &mocks.FakeDBExecutor{
		QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
			return errors.New("Generic error")
		},
	}

	_, err := getGatewayConfig(fakeDB, softwareGatewayID)
	if err == nil || !strings.Contains(err.Error(), "Generic error") {
		t.Errorf("Expected error for Generic error, got: %v", err)
	}
}

// ---------- Tests for getFirebaseConfig ----------

func TestGetFirebaseConfig_Success(t *testing.T) {
	queryResults := &dbConfig{
		Value: `firebase-config-string`,
	}
	fakeDB := &mocks.FakeDBExecutor{
		QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
			db, ok := dest.(*dbConfig)
			if !ok {
				return errors.New("dest must be of type *dbConfig")
			}
			*db = *queryResults
			return nil
		},
	}

	config, err := getFirebaseConfig(fakeDB)
	if err != nil {
		t.Fatalf("Expected no error, got: %v", err)
	}
	if *config != queryResults.Value {
		t.Errorf("Expected config %q, got %q", queryResults.Value, *config)
	}
}

func TestGetFirebaseConfig_NoRows(t *testing.T) {
	fakeDB := &mocks.FakeDBExecutor{
		QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
			// Simulate no rows.
			return sql.ErrNoRows
		},
	}

	_, err := getFirebaseConfig(fakeDB)
	if err == nil || !strings.Contains(err.Error(), "was not found") {
		t.Errorf("Expected error for missing gateway, got: %v", err)
	}
}

func TestGetFirebaseConfig_GenericQueryError(t *testing.T) {
	fakeDB := &mocks.FakeDBExecutor{
		QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
			// Simulate no rows.
			return errors.New("Generic error")
		},
	}

	_, err := getFirebaseConfig(fakeDB)
	if err == nil || !strings.Contains(err.Error(), "Generic error") {
		t.Errorf("Expected error for Generic error, got: %v", err)
	}
}

// ---------- Tests for getDeviceSettings ----------

func TestGetDeviceSettings_Success(t *testing.T) {
	// Use keys matching the DeviceSettings JSON tags.
	dbDeviceSettings := []softwareGateway.DeviceSettings{
		{
			Device_ID:          "dev-001",
			Latitude:           "12.34",
			Longitude:          "56.78",
			IP_Address:         "***********",
			Port:               "8080",
			FlushConnection_MS: "100",
			EnableRealtime:     "true",
		},
	}
	fakeDB := &mocks.FakeDBExecutor{
		QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
			instrSlice, ok := dest.(*[]softwareGateway.DeviceSettings)
			if !ok {
				return errors.New("dest is not of type *[]softwareGateway.DeviceSettings")
			}
			*instrSlice = dbDeviceSettings
			return nil
		},
	}

	deviceSettings, err := getDeviceSettings(fakeDB, 789)
	if err != nil {
		t.Fatalf("Expected no error, got: %v", err)
	}
	if len(*deviceSettings) != 1 {
		t.Errorf("Expected 1 device configuration, got %d", len(*deviceSettings))
	}
	ds := (*deviceSettings)[0]
	if ds.Device_ID != "dev-001" {
		t.Errorf("Expected Device_ID 'dev-001', got %q", ds.Device_ID)
	}
	if ds.Latitude != "12.34" {
		t.Errorf("Expected Latitude '12.34', got %q", ds.Latitude)
	}
	if ds.Longitude != "56.78" {
		t.Errorf("Expected Longitude '56.78', got %q", ds.Longitude)
	}
	if ds.IP_Address != "***********" {
		t.Errorf("Expected IP_Address '***********', got %q", ds.IP_Address)
	}
	if ds.Port != "8080" {
		t.Errorf("Expected Port '8080', got %q", ds.Port)
	}
	if ds.FlushConnection_MS != "100" {
		t.Errorf("Expected FlushConnection_MS '100', got %q", ds.FlushConnection_MS)
	}
	if ds.EnableRealtime != "true" {
		t.Errorf("Expected EnableRealtime 'true', got %q", ds.EnableRealtime)
	}
}

func TestGetDeviceSettings_GenericQueryError(t *testing.T) {
	fakeDB := &mocks.FakeDBExecutor{
		QueryGenericSliceFunc: func(dest interface{}, query string, args ...interface{}) error {
			return errors.New("Generic error non-sql.norows error")
		},
	}

	_, err := getDeviceSettings(fakeDB, 789)
	if err == nil || !strings.Contains(err.Error(), "Generic error") {
		t.Errorf("Expected 'Generic error', got: %v", err)
	}
}

// ---------- Tests for setNewGatewayToken ----------

func TestSetNewGatewayToken_Success(t *testing.T) {
	gatewayID := "gateway-001"
	fakeDB := &mocks.FakeDBExecutor{
		ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
			if len(args) != 3 {
				return nil, errors.New("wrong number of args")
			}
			tokenArg, ok := args[0].(string)
			if !ok || tokenArg == "" {
				return nil, errors.New("token is empty")
			}
			// Validate the timestamp format.
			if _, err := time.Parse(time.DateTime, args[1].(string)); err != nil {
				return nil, errors.New("invalid timestamp")
			}
			if args[2] != gatewayID {
				return nil, errors.New("gateway identifier mismatch")
			}
			return fakeResult{}, nil
		},
	}

	cloudSettings, err := setNewGatewayToken(fakeDB, gatewayID)
	if err != nil {
		t.Fatalf("Expected no error, got: %v", err)
	}
	if cloudSettings.Token == "" {
		t.Errorf("Expected a non-empty new token, got: %v", cloudSettings.Token)
	}
}

func TestSetNewGatewayToken_ExecError(t *testing.T) {
	gatewayID := "gateway-001"
	fakeDB := &mocks.FakeDBExecutor{
		ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
			return nil, errors.New("exec error")
		},
	}

	cloudSettings, err := setNewGatewayToken(fakeDB, gatewayID)
	if err == nil {
		t.Error("Expected error due to Exec failure, got nil")
	}
	if cloudSettings != nil {
		if cloudSettings.Token != "" {
			t.Errorf("Expected empty token on error, got: %q", cloudSettings.Token)
		}
	}
}

func TestSetNewGatewayToken_GenTokenHexError(t *testing.T) {
	origRandRead := randRead
	defer func() { randRead = origRandRead }()
	readfail := errors.New("read fail")
	randRead = func(b []byte) (int, error) {
		return 0, readfail
	}
	gatewayID := "gateway-001"
	fakeDB := &mocks.FakeDBExecutor{
		ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
			return nil, nil
		},
	}

	_, err := setNewGatewayToken(fakeDB, gatewayID)
	if !errors.Is(err, readfail) {
		t.Error("Expected error due to randRead failure, got nil")
	}
}

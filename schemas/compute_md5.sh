#!/bin/bash
# process_updates.sh
#
# This script processes .sql files located in any "updates" folder under /schemas.
# For each .sql file found, it computes its MD5 checksum, looks for a sibling .md5 file,
# compares the checksums if the .md5 file exists, creates the file if it doesn't exist,
# or updates the file if the computed checksum doesn't match the stored value.

# Base directory for schemas.
BASE_DIR="/schemas"

# Find all .sql files in an "updates" directory under the base directory.
find "$BASE_DIR" -type f -path "*/updates/*.sql" | while read -r sqlfile; do
    echo "Processing $sqlfile..."

    # Compute the MD5 checksum of the .sql file.
    # On Linux, use 'md5sum'. On macOS, replace with: computed_md5=$(md5 -q "$sqlfile")
    computed_md5=$(md5sum "$sqlfile" | awk '{print $1}')

    # Determine the sibling .md5 file. (Replace .sql with .md5)
    md5file="${sqlfile%.sql}.md5"

    # If the .md5 file exists, compare the computed MD5 with its contents.
    if [ -f "$md5file" ]; then
        stored_md5=$(tr -d '[:space:]' < "$md5file")
        if [ "$computed_md5" = "$stored_md5" ]; then
            echo "  MD5 matches for $sqlfile. Skipping."
        else
            echo "  MD5 mismatch for $sqlfile. Updating $md5file."
            echo "$computed_md5" > "$md5file"
        fi
    else
        echo "  MD5 file $md5file does not exist. Creating it."
        echo "$computed_md5" > "$md5file"
    fi
done

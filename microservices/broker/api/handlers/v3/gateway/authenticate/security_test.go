package authenticate

import (
	"crypto/aes"
	"crypto/cipher"
	"encoding/base64"
	"encoding/hex"
	"errors"
	"strings"
	"testing"
)

// dummy<PERSON>lock implements cipher.Block for testing purposes.
type dummyBlock struct{}

func (d *dummyBlock) BlockSize() int          { return 16 }
func (d *dummyBlock) Encrypt(dst, src []byte) { copy(dst, src) }
func (d *dummyBlock) Decrypt(dst, src []byte) { copy(dst, src) }

// dummyAEAD implements cipher.AEAD for testing error paths and nonce size checks.
type dummyAEAD struct {
	nonceSize int
	openErr   error
}

func (d *dummyAEAD) NonceSize() int { return d.nonceSize }
func (d *dummyAEAD) Overhead() int  { return 0 }
func (d *dummyAEAD) Seal(dst, nonce, plaintext, additionalData []byte) []byte {
	return append(dst, plaintext...)
}

func (d *dummyAEAD) Open(dst, nonce, ciphertext, additionalData []byte) ([]byte, error) {
	if d.openErr != nil {
		return nil, d.openErr
	}
	return append(dst, ciphertext...), nil
}

func TestGenerateRandomTokenHex(t *testing.T) {
	// Backup and restore randRead
	origRandRead := randRead
	defer func() { randRead = origRandRead }()

	tests := []struct {
		name     string
		length   uint
		fakeRead func([]byte) (int, error)
		wantErr  bool
		wantLen  int
	}{
		{name: "rand error", length: 4, fakeRead: func(b []byte) (int, error) {
			return 0, errors.New("read fail")
		}, wantErr: true},
		{name: "happy path even length", length: 4, fakeRead: func(b []byte) (int, error) {
			for i := range b {
				b[i] = 0xAB
			}
			return len(b), nil
		}, wantErr: false, wantLen: 4},
		{name: "odd length", length: 5, fakeRead: func(b []byte) (int, error) {
			for i := range b {
				b[i] = 0x01
			}
			return len(b), nil
		}, wantErr: false, wantLen: 4},
		{name: "zero length", length: 0, fakeRead: func(b []byte) (int, error) {
			for i := range b {
				b[i] = 0x01
			}
			return len(b), nil
		}, wantErr: false, wantLen: 0},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			if tc.fakeRead != nil {
				randRead = tc.fakeRead
			} else {
				randRead = func(b []byte) (int, error) {
					for i := range b {
						b[i] = 0xFF
					}
					return len(b), nil
				}
			}

			got, err := generateRandomTokenHex(tc.length)
			if tc.wantErr {
				if err == nil {
					t.Fatalf("expected error, got none")
				}
				return
			}
			if err != nil {
				t.Fatalf("unexpected error: %v", err)
			}
			if len(got) != tc.wantLen {
				t.Errorf("got length %d, want %d", len(got), tc.wantLen)
			}
			if _, err := hex.DecodeString(got); err != nil {
				t.Errorf("output not valid hex: %v", err)
			}
		})
	}
}

func TestDecryptBase64EncodedString(t *testing.T) {
	// Backup and restore package-level vars
	origBase64Decode := base64Decode
	origAESNewCipher := aesNewCipher
	origCipherNewGCM := cipherNewGCM
	defer func() {
		base64Decode = origBase64Decode
		aesNewCipher = origAESNewCipher
		cipherNewGCM = origCipherNewGCM
	}()

	// Prepare happy path data
	key := []byte("example key 1234")
	block, _ := aes.NewCipher(key)
	gcmReal, _ := cipher.NewGCM(block)
	nonce := make([]byte, gcmReal.NonceSize())
	plaintext := []byte("hello, world")
	ciphertext := gcmReal.Seal(nil, nonce, plaintext, nil)
	realData := append(nonce, ciphertext...)
	realEncoded := base64.StdEncoding.EncodeToString(realData)
	realKey := string(key)

	tests := []struct {
		name             string
		encoded          string
		key              string
		fakeBase64Decode func(string) ([]byte, error)
		fakeAESNewCipher func([]byte) (cipher.Block, error)
		fakeCipherNewGCM func(cipher.Block) (cipher.AEAD, error)
		wantErr          bool
		want             string
		errContains      string
	}{
		{
			name:             "base64 decode error",
			fakeBase64Decode: func(s string) ([]byte, error) { return nil, errors.New("b64fail") },
			encoded:          "ignored",
			wantErr:          true,
			errContains:      "base64 decode: b64fail",
		},
		{
			name:             "cipher init error",
			fakeBase64Decode: func(s string) ([]byte, error) { return []byte("data"), nil },
			fakeAESNewCipher: func(key []byte) (cipher.Block, error) { return nil, errors.New("cipherfail") },
			encoded:          "ignored",
			wantErr:          true,
			errContains:      "creating cipher: cipherfail",
		},
		{
			name:             "GCM init error",
			fakeBase64Decode: func(s string) ([]byte, error) { return []byte("abcd"), nil },
			fakeAESNewCipher: func(key []byte) (cipher.Block, error) { return &dummyBlock{}, nil },
			fakeCipherNewGCM: func(b cipher.Block) (cipher.AEAD, error) { return nil, errors.New("gcmfail") },
			encoded:          "ignored",
			wantErr:          true,
			errContains:      "creating GCM: gcmfail",
		},
		{
			name:             "ciphertext too short",
			fakeBase64Decode: func(s string) ([]byte, error) { return make([]byte, 2), nil },
			fakeAESNewCipher: func(key []byte) (cipher.Block, error) { return &dummyBlock{}, nil },
			fakeCipherNewGCM: func(b cipher.Block) (cipher.AEAD, error) { return &dummyAEAD{nonceSize: 3}, nil },
			encoded:          "ignored",
			wantErr:          true,
			errContains:      "ciphertext too short",
		},
		{
			name:             "decryption error",
			fakeBase64Decode: func(s string) ([]byte, error) { return []byte{0, 0, 1}, nil },
			fakeAESNewCipher: func(key []byte) (cipher.Block, error) { return &dummyBlock{}, nil },
			fakeCipherNewGCM: func(b cipher.Block) (cipher.AEAD, error) {
				return &dummyAEAD{nonceSize: 2, openErr: errors.New("openfail")}, nil
			},
			encoded:     "ignored",
			wantErr:     true,
			errContains: "decrypting data: openfail",
		},
		{
			name:    "happy path",
			encoded: realEncoded,
			key:     realKey,
			wantErr: false,
			want:    string(plaintext),
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			// inject overrides
			if tc.fakeBase64Decode != nil {
				base64Decode = tc.fakeBase64Decode
			} else {
				base64Decode = base64.StdEncoding.DecodeString
			}
			if tc.fakeAESNewCipher != nil {
				aesNewCipher = tc.fakeAESNewCipher
			} else {
				aesNewCipher = aes.NewCipher
			}
			if tc.fakeCipherNewGCM != nil {
				cipherNewGCM = tc.fakeCipherNewGCM
			} else {
				cipherNewGCM = cipher.NewGCM
			}

			got, err := decryptBase64EncodedString(tc.encoded, tc.key)
			if tc.wantErr {
				if err == nil {
					t.Fatalf("expected error, got none")
				}
				if tc.errContains != "" && !strings.Contains(err.Error(), tc.errContains) {
					t.Errorf("error = %v, want contains %q", err, tc.errContains)
				}
				return
			}
			if err != nil {
				t.Fatalf("unexpected error: %v", err)
			}
			if got != tc.want {
				t.Errorf("got = %q, want %q", got, tc.want)
			}
		})
	}
}

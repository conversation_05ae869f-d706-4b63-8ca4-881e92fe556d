package middleware

import (
	"net/http"

	"synapse-its.com/shared/connect"
)

// Return a handler that sets the Connections as provided in the arguments.
func ConnectionsMiddleware(connections *connect.Connections) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			ctx := connect.WithConnections(r.Context(), connections)
			next.ServeHTTP(w, r.WithContext(ctx))
		})
	}
}

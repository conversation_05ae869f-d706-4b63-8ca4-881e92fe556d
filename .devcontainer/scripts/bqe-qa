#!/usr/bin/env bash
# Description: Query a table in the dataset
#   bqe qa <table_name>
set -euo pipefail

# check args
if [[ $# -ne 1 ]]; then
  echo "Usage: bqe qa <table_name>" >&2
  exit 1
fi

# get current namespace
ns=$(bqe-get namespace)
if [[ -z "$ns" ]]; then
  echo "Error: no namespace set. Run 'bqe ns' to list or 'bqe ns <name>' to set one." >&2
  exit 1
fi

# get current dataset
dataset=$(bqe-dataset)
if [[ -z "$dataset" ]]; then
  echo "Error: no dataset set. Run 'bqe ds' to list or 'bqe ds <name>' to set one." >&2
  exit 1
fi

# build fully qualified table name
table="${ns}__${1}"

# run the query
bq query --use_legacy_sql=false "SELECT * FROM \`${dataset}.${table}\`"

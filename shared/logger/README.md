# logger

---

## Overview

The `logger` package initializes a global `*zap.Logger` with JSON encoding, environment-driven log level, Google Cloud–friendly field names, and automatic source location/stack traces for error logs.

---

## Package Layout

```
shared/
├── logger/
│   └── logger.go
```

---

## Features

- **JSON Output**: production-style logs  
- **Configurable Log Level**: `LOG_LEVEL` env var (default `INFO`)  
- **Source Location**: file, line, function in `logging.googleapis.com/sourceLocation`  
- **Stack Traces**: appended for `Error`/`Fatal` methods  
- **Convenience Methods**: unformatted and `*f`-style logging  

---

## Usage

```go
import "synapse-its.com/shared/logger"
func main() {
    logger.Info("service starting")
    logger.Debug("db connected", zap.String("dsn", dsn))
    logger.Infof("listening on port %d", port)
    if err := doWork(); err != nil {
        logger.Error("operation failed", zap.Error(err))
    }
}
```

---

## API Reference

### Global Logger

```go
var Logger *zap.Logger
```

### Logging Methods

```go
func Debug(msg string, fields ...zap.Field)
func Info(msg string, fields ...zap.Field)
func Warn(msg string, fields ...zap.Field)
func Error(msg string, fields ...zap.Field)
func Fatal(msg string, fields ...zap.Field)

func Debugf(format string, args ...interface{})
func Infof(format string, args ...interface{})
func Warnf(format string, args ...interface{})
func Errorf(format string, args ...interface{})
func Fatalf(format string, args ...interface{})
``` 

---

## Internals

- **Init**: reads `LOG_LEVEL`, builds `zap.Config`  
- **Encoder Config**: RFC3339Nano time, lowercase level, short caller  
- **Source Location Field**: `runtime.Caller` wrapper  
- **Stack Trace**: `debug.Stack()` for error methods  

---

## Example

```json
{
  "time": "2025-04-30T18:00:00.123Z",
  "severity": "info",
  "message": "service starting",
  "logging.googleapis.com/sourceLocation": {
    "file": "/path/to/main.go",
    "line": 42,
    "function": "main.main"
  }
}
```

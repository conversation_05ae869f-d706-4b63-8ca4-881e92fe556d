package handlers

import (
	"encoding/json"
	"net/http"
)

// fooHandler returns a static list of names in JSON.
func FooHandler(w http.ResponseWriter, r *http.Request) {
	names := []map[string]string{
		{"orgIden": "org123", "apiKey": "key-abc-123", "name": "OEM Alpha"},
		{"orgIden": "org456", "apiKey": "key-def-456", "name": "OEM Beta"},
		{"orgIden": "org789", "apiKey": "key-ghi-789", "name": "OEM Gamma"},
	}
	w.<PERSON><PERSON>().Set("Content-Type", "application/json")
	_ = json.NewEncoder(w).Encode(names)
}

package dbexecutor

import (
	"database/sql"
	"fmt"
)

var ( // Error messages for FakeDBExecutor
	ErrFakeDBExecutorExecCallLimit              = fmt.<PERSON><PERSON>rf("Exec call limit reached")
	ErrFakeDBExecutorExecMultipleCallLimit      = fmt.Errorf("ExecMultiple call limit reached")
	ErrFakeDBExecutorQueryGenericCallLimit      = fmt.<PERSON><PERSON><PERSON>("QueryGeneric call limit reached")
	ErrFakeDBExecutorQueryGenericSliceCallLimit = fmt.<PERSON><PERSON><PERSON>("QueryGenericSlice call limit reached")
	ErrFakeDBExecutorQueryRowCallLimit          = fmt.Errorf("QueryRow call limit reached")
	ErrFakeDBExecutorQueryRowStructCallLimit    = fmt.E<PERSON>rf("QueryRowStruct call limit reached")
	ErrFakeDBExecutorCloseCallLimit             = fmt.<PERSON><PERSON><PERSON>("Close call limit reached")
)

type FakeDBExecutor struct {
	EnableFailAfter                bool
	ExecFunc                       func(query string, args ...interface{}) (sql.Result, error)
	ExecCallCount                  int
	ExecCallFailAfter              int
	ExecMultipleFunc               func(queries string) error
	ExecMultipleCallCount          int
	ExecMultipleCallFailAfter      int
	QueryGenericFunc               func(query string, args ...interface{}) ([]map[string]interface{}, error)
	QueryGenericCallCount          int
	QueryGenericCallFailAfter      int
	QueryGenericSliceFunc          func(dest interface{}, query string, args ...interface{}) error
	QueryGenericSliceCallCount     int
	QueryGenericSliceCallFailAfter int
	QueryRowFunc                   func(query string, args ...interface{}) (map[string]interface{}, error)
	QueryRowCallCount              int
	QueryRowCallFailAfter          int
	QueryRowStructFunc             func(dest interface{}, query string, args ...interface{}) error
	QueryRowStructCallCount        int
	QueryRowStructCallFailAfter    int
	EscapeIdentifierFunc           func(identifier string) string
	EscapeIdentifierCallCount      int
	ReplaceNamespaceFunc           func(query string) string
	ReplaceNamespaceCallCount      int
	CloseFunc                      func() error
	CloseCallCount                 int
	CloseCallFailAfter             int
}

func (f *FakeDBExecutor) Close() error {
	f.CloseCallCount++
	if f.CloseFunc != nil {
		return f.CloseFunc()
	}
	if f.EnableFailAfter && f.CloseCallFailAfter >= 0 && f.CloseCallCount > f.CloseCallFailAfter {
		return ErrFakeDBExecutorCloseCallLimit
	}
	return nil
}

func (f *FakeDBExecutor) QueryGeneric(query string, args ...interface{}) ([]map[string]interface{}, error) {
	f.QueryGenericCallCount++
	if f.QueryGenericFunc != nil {
		return f.QueryGenericFunc(query, args...)
	}
	if f.EnableFailAfter && f.QueryGenericCallFailAfter >= 0 && f.QueryGenericCallCount > f.QueryGenericCallFailAfter {
		return nil, ErrFakeDBExecutorQueryGenericCallLimit
	}
	return nil, nil
}

func (f *FakeDBExecutor) QueryGenericSlice(dest interface{}, query string, args ...interface{}) error {
	f.QueryGenericSliceCallCount++
	if f.QueryGenericSliceFunc != nil {
		return f.QueryGenericSliceFunc(dest, query, args...)
	}
	if f.EnableFailAfter && f.QueryGenericSliceCallFailAfter >= 0 && f.QueryGenericSliceCallCount > f.QueryGenericSliceCallFailAfter {
		return ErrFakeDBExecutorQueryGenericSliceCallLimit
	}
	return nil
}

// To satisfy the connect.DatabaseExecutor interface in both tests.
func (f *FakeDBExecutor) Exec(query string, args ...interface{}) (sql.Result, error) {
	f.ExecCallCount++
	if f.ExecFunc != nil {
		return f.ExecFunc(query, args...)
	}
	if f.EnableFailAfter && f.ExecCallFailAfter >= 0 && f.ExecCallCount > f.ExecCallFailAfter {
		return nil, ErrFakeDBExecutorExecCallLimit
	}
	return nil, nil
}

func (f *FakeDBExecutor) ExecMultiple(query string) error {
	f.ExecMultipleCallCount++
	if f.ExecMultipleFunc != nil {
		return f.ExecMultipleFunc(query)
	}
	if f.EnableFailAfter && f.ExecMultipleCallFailAfter >= 0 && f.ExecMultipleCallCount > f.ExecMultipleCallFailAfter {
		return ErrFakeDBExecutorExecMultipleCallLimit
	}
	return nil
}

func (f *FakeDBExecutor) QueryRow(query string, args ...interface{}) (map[string]interface{}, error) {
	f.QueryRowCallCount++
	if f.QueryRowFunc != nil {
		return f.QueryRowFunc(query, args...)
	}
	if f.EnableFailAfter && f.QueryRowCallFailAfter >= 0 && f.QueryRowCallCount > f.QueryRowCallFailAfter {
		return nil, ErrFakeDBExecutorQueryRowCallLimit
	}
	return nil, nil
}

func (f *FakeDBExecutor) QueryRowStruct(dest interface{}, query string, args ...interface{}) error {
	f.QueryRowStructCallCount++
	if f.QueryRowStructFunc != nil {
		return f.QueryRowStructFunc(dest, query, args...)
	}
	if f.EnableFailAfter && f.QueryRowStructCallFailAfter >= 0 && f.QueryRowStructCallCount > f.QueryRowStructCallFailAfter {
		return ErrFakeDBExecutorQueryRowStructCallLimit
	}
	return nil
}

func (f *FakeDBExecutor) EscapeIdentifier(identifier string) string {
	f.EscapeIdentifierCallCount++
	if f.EscapeIdentifierFunc != nil {
		return f.EscapeIdentifierFunc(identifier)
	}
	return ""
}

func (f *FakeDBExecutor) ReplaceNamespace(query string) string {
	f.ReplaceNamespaceCallCount++
	if f.ReplaceNamespaceFunc != nil {
		return f.ReplaceNamespaceFunc(query)
	}
	return ""
}

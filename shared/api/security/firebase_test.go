package security

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/base64"
	"encoding/json"
	"errors"
	"sync"
	"testing"

	"github.com/stretchr/testify/assert"
)

const testKey = "6MwP8wwU3i7olyg0kdHg2ngYVa4xU8Ug"

// mutex to allow for t.parallel safely
var hookMux sync.Mutex

// encrypt mirrors decryptBase64EncodedString by sealing plaintext.
func encrypt(plaintext []byte) (string, error) {
	key := []byte(testKey)
	block, err := aes.NewCipher(key)
	if err != nil {
		return "", err
	}
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", err
	}
	nonce := make([]byte, gcm.NonceSize())
	if _, err := rand.Read(nonce); err != nil {
		return "", err
	}
	ciphertext := gcm.Seal(nil, nonce, plaintext, nil)
	data := append(nonce, ciphertext...)
	return base64.StdEncoding.EncodeToString(data), nil
}

func TestDecryptBase64EncodedString(t *testing.T) {
	t.Parallel()
	assert := assert.New(t)

	t.Run("InvalidBase64", func(t *testing.T) {
		t.Parallel()
		hookMux.Lock()
		_, err := decryptBase64EncodedString("!!!not-base64!!!")
		hookMux.Unlock()
		assert.Error(err)
	})

	t.Run("ShortCiphertext", func(t *testing.T) {
		t.Parallel()
		encoded := base64.StdEncoding.EncodeToString([]byte{1, 2, 3, 4, 5})
		hookMux.Lock()
		_, err := decryptBase64EncodedString(encoded)
		hookMux.Unlock()
		assert.Error(err)
		assert.Contains(err.Error(), "ciphertext too short")
	})

	t.Run("TamperedCiphertext", func(t *testing.T) {
		t.Parallel()
		orig := []byte("hello world")
		encoded, err := encrypt(orig)
		assert.NoError(err)

		data, _ := base64.StdEncoding.DecodeString(encoded)
		data[len(data)-1] ^= 0xFF
		tampered := base64.StdEncoding.EncodeToString(data)
		hookMux.Lock()
		_, err = decryptBase64EncodedString(tampered)
		hookMux.Unlock()
		assert.Error(err)
	})

	t.Run("Success", func(t *testing.T) {
		t.Parallel()
		plaintext := []byte("the quick brown fox jumps over the lazy dog")
		encoded, err := encrypt(plaintext)
		assert.NoError(err)
		hookMux.Lock()
		out, err := decryptBase64EncodedString(encoded)
		hookMux.Unlock()
		assert.NoError(err)
		assert.Equal(string(plaintext), out)
	})

	t.Run("AESInitError", func(t *testing.T) {
		t.Parallel()
		hookMux.Lock()
		// swap in the failing hook
		aesNewCipher = func([]byte) (cipher.Block, error) {
			return nil, errors.New("cipher-init failed")
		}
		defer func() {
			aesNewCipher = aes.NewCipher
			hookMux.Unlock()
		}()

		// use any valid base64 so decode passes
		valid, _ := encrypt([]byte("foo"))
		_, err := decryptBase64EncodedString(valid)
		assert.Error(err)
	})

	t.Run("GCMInitError", func(t *testing.T) {
		t.Parallel()
		hookMux.Lock()
		gcmNew = func(cipher.Block) (cipher.AEAD, error) {
			return nil, errors.New("gcm-init failed")
		}
		defer func() {
			gcmNew = cipher.NewGCM
			hookMux.Unlock()
		}()

		valid, _ := encrypt([]byte("foo"))
		_, err := decryptBase64EncodedString(valid)
		assert.Error(err)
	})
}

func TestFirebaseAuthDecrypt(t *testing.T) {
	t.Parallel()
	assert := assert.New(t)

	orig := &GoogleSettings{
		Type:                     "type",
		ProjectId:                "proj",
		PrivateKeyID:             "keyid",
		PrivateKey:               "privkey",
		ClientEmail:              "<EMAIL>",
		ClientId:                 "clientid",
		AuthUri:                  "https://auth",
		TokenUri:                 "https://token",
		AuthProviderX509CcertUrl: "https://provider",
		ClientX509CertUrl:        "https://cert",
		UniverseDomain:           "domain",
	}
	jsonData, err := json.Marshal(orig)
	assert.NoError(err)

	t.Run("InvalidBase64", func(t *testing.T) {
		t.Parallel()
		hookMux.Lock()
		_, err := FirebaseAuthDecrypt("not-base64!!!")
		hookMux.Unlock()
		assert.Error(err)
	})

	t.Run("InvalidJSON", func(t *testing.T) {
		t.Parallel()
		encoded, err := encrypt([]byte("not a json"))
		assert.NoError(err)
		hookMux.Lock()
		_, err = FirebaseAuthDecrypt(encoded)
		hookMux.Unlock()
		assert.Error(err)
	})

	t.Run("Success", func(t *testing.T) {
		t.Parallel()
		encoded, err := encrypt(jsonData)
		assert.NoError(err)
		hookMux.Lock()
		res, err := FirebaseAuthDecrypt(encoded)
		hookMux.Unlock()
		assert.NoError(err)
		assert.Equal(orig, res)
	})
}

package edicmu2212

import (
	"errors"
	"testing"
	"time"

	"synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/pubsubdata"
)

func TestLogMonitorReset(t *testing.T) {
	device := EDICMU2212{}
	httpHeader := &pubsubdata.HeaderDetails{
		GatewayTimezone: "UTC",
	}
	header := &helper.HeaderRecord{}

	tests := []struct {
		name           string
		byteMsg        []byte
		httpHeader     *pubsubdata.HeaderDetails
		wantRecords    int
		wantErr        error
		validateRecord func(t *testing.T, record helper.LogMonitorResetRecord)
	}{
		{
			name:        "valid single record",
			byteMsg:     buildValidLogMonitorResetMsg(1),
			httpHeader:  httpHeader,
			wantRecords: 1,
			wantErr:     nil,
			validateRecord: func(t *testing.T, record helper.LogMonitorResetRecord) {
				if record.DateTime.IsZero() {
					t.<PERSON>rror("LogMonitorReset() record DateTime is zero")
				}
			},
		},
		{
			name:        "valid multiple records",
			byteMsg:     buildValidLogMonitorResetMsg(3),
			httpHeader:  httpHeader,
			wantRecords: 3,
			wantErr:     nil,
			validateRecord: func(t *testing.T, record helper.LogMonitorResetRecord) {
				if record.DateTime.IsZero() {
					t.Error("LogMonitorReset() record DateTime is zero")
				}
			},
		},
		{
			name:        "invalid message length",
			byteMsg:     []byte{0x01, 0x02}, // Too short
			httpHeader:  httpHeader,
			wantRecords: 0,
			wantErr:     helper.ErrMsgByteLen,
			validateRecord: func(t *testing.T, record helper.LogMonitorResetRecord) {
				// No validation needed for error case
			},
		},
		{
			name:        "zero records",
			byteMsg:     buildValidLogMonitorResetMsg(0),
			httpHeader:  httpHeader,
			wantRecords: 0,
			wantErr:     nil,
			validateRecord: func(t *testing.T, record helper.LogMonitorResetRecord) {
				// No validation needed for zero records
			},
		},
		{
			name:        "invalid checksum",
			byteMsg:     buildInvalidChecksumMsg(1),
			httpHeader:  httpHeader,
			wantRecords: 0,
			wantErr:     helper.ErrMsgByteChecksum,
			validateRecord: func(t *testing.T, record helper.LogMonitorResetRecord) {
				// No validation needed for error case
			},
		},
		{
			name:        "invalid BCD time format",
			byteMsg:     buildInvalidBCDTimeMsg(1),
			httpHeader:  httpHeader,
			wantRecords: 0,
			wantErr:     helper.ErrValidateDateTimePartsMon,
			validateRecord: func(t *testing.T, record helper.LogMonitorResetRecord) {
				// No validation needed for error case
			},
		},
		{
			name:        "max records",
			byteMsg:     buildValidLogMonitorResetMsg(255), // Maximum value for a byte
			httpHeader:  httpHeader,
			wantRecords: 255,
			wantErr:     nil,
			validateRecord: func(t *testing.T, record helper.LogMonitorResetRecord) {
				if record.DateTime.IsZero() {
					t.Error("LogMonitorReset() record DateTime is zero")
				}
			},
		},
		{
			name:        "different timezone",
			byteMsg:     buildValidLogMonitorResetMsg(1),
			httpHeader:  &pubsubdata.HeaderDetails{GatewayTimezone: "America/New_York"},
			wantRecords: 1,
			wantErr:     nil,
			validateRecord: func(t *testing.T, record helper.LogMonitorResetRecord) {
				if record.DateTime.IsZero() {
					t.Error("LogMonitorReset() record DateTime is zero")
				}
			},
		},
		{
			name:        "invalid timezone",
			byteMsg:     buildValidLogMonitorResetMsg(1),
			httpHeader:  &pubsubdata.HeaderDetails{GatewayTimezone: "Invalid/Timezone"},
			wantRecords: 1,
			wantErr:     nil,
			validateRecord: func(t *testing.T, record helper.LogMonitorResetRecord) {
				if record.DateTime.IsZero() {
					t.Error("LogMonitorReset() record DateTime is zero")
				}
			},
		},
		{
			name:        "edge case time values",
			byteMsg:     buildEdgeCaseTimeMsg(1),
			httpHeader:  httpHeader,
			wantRecords: 1,
			wantErr:     nil,
			validateRecord: func(t *testing.T, record helper.LogMonitorResetRecord) {
				if record.DateTime.IsZero() {
					t.Error("LogMonitorReset() record DateTime is zero")
				}
			},
		},
		{
			name:        "invalid header values",
			byteMsg:     buildInvalidHeaderMsg(),
			httpHeader:  httpHeader,
			wantRecords: 0,
			wantErr:     helper.ErrMsgByteLen,
			validateRecord: func(t *testing.T, record helper.LogMonitorResetRecord) {
				// No validation needed for error case
			},
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			t.Parallel()
			got, err := device.LogMonitorReset(test.httpHeader, test.byteMsg, header)
			if err != nil && test.wantErr == nil {
				t.Errorf("LogMonitorReset() unexpected error = %v", err)
				return
			}
			if err == nil && test.wantErr != nil {
				t.Errorf("LogMonitorReset() expected error = %v, got nil", test.wantErr)
				return
			}
			if err != nil && test.wantErr != nil {
				if !errors.Is(err, test.wantErr) {
					t.Errorf("LogMonitorReset() error = %v, wantErr %v", err, test.wantErr)
				}
				return
			}
			if got == nil && test.wantRecords > 0 {
				t.Error("LogMonitorReset() returned nil records when records were expected")
				return
			}
			if got != nil && len(got.Records) != test.wantRecords {
				t.Errorf("LogMonitorReset() got %d records, want %d", len(got.Records), test.wantRecords)
				return
			}
			if got != nil {
				for _, record := range got.Records {
					test.validateRecord(t, record)
				}
				// Check DeviceModel and RawMessage
				if got.DeviceModel != DeviceModel {
					t.Errorf("LogMonitorReset() DeviceModel = %v, want %v", got.DeviceModel, DeviceModel)
				}
				if got.RawMessage == nil {
					t.Error("LogMonitorReset() RawMessage is nil")
				} else if len(got.RawMessage) != len(test.byteMsg) {
					t.Errorf("LogMonitorReset() RawMessage length = %v, want %v", len(got.RawMessage), len(test.byteMsg))
				} else {
					for i := range test.byteMsg {
						if got.RawMessage[i] != test.byteMsg[i] {
							t.Errorf("LogMonitorReset() RawMessage[%d] = %v, want %v", i, got.RawMessage[i], test.byteMsg[i])
							break
						}
					}
				}
			}
		})
	}
}

func TestLogMonitorReset_ZeroRecords(t *testing.T) {
	t.Parallel()
	device := EDICMU2212{}
	httpHeader := &pubsubdata.HeaderDetails{GatewayTimezone: "UTC"}
	header := &helper.HeaderRecord{}
	msg := buildValidLogMonitorResetMsg(0)
	result, err := device.LogMonitorReset(httpHeader, msg, header)
	if err != nil {
		t.Errorf("unexpected error for zero records: %v", err)
	}
	if result == nil {
		t.Error("expected non-nil result for zero records")
	}
	if len(result.Records) != 0 {
		t.Errorf("expected zero records, got %d", len(result.Records))
	}
}

func TestLogMonitorReset_TruncatedMessage(t *testing.T) {
	device := EDICMU2212{}
	httpHeader := &pubsubdata.HeaderDetails{GatewayTimezone: "UTC"}
	header := &helper.HeaderRecord{}
	msg := buildValidLogMonitorResetMsg(1)
	msg = msg[:HeaderLength+1+LogMonitorResetRecordSize-1] // truncate to just before full record
	_, err := device.LogMonitorReset(httpHeader, msg, header)
	if err == nil {
		t.Error("expected error for truncated message")
	}
}

// buildValidLogMonitorResetMsg creates a valid log monitor reset message with the specified number of records
func buildValidLogMonitorResetMsg(numRecords int) []byte {
	// Calculate total length: HeaderLength + (numRecords * LogMonitorResetRecordSize) + HeaderOffset
	totalLength := HeaderLength + (numRecords * LogMonitorResetRecordSize) + HeaderOffset
	msg := make([]byte, totalLength)

	// Set header bytes (all zeros for test)
	for i := 0; i < HeaderLength; i++ {
		msg[i] = 0x00
	}

	// Set number of records
	msg[HeaderLength] = byte(numRecords)

	// Set current time in BCD format for each record
	now := time.Now()
	startingByte := HeaderLength + 1

	for i := 0; i < numRecords; i++ {
		// Convert time to BCD format
		msg[startingByte] = byte(now.Second()/10<<4 | now.Second()%10)     // Seconds
		msg[startingByte+1] = byte(now.Minute()/10<<4 | now.Minute()%10)   // Minutes
		msg[startingByte+2] = byte(now.Hour()/10<<4 | now.Hour()%10)       // Hours
		msg[startingByte+3] = byte(now.Day()/10<<4 | now.Day()%10)         // Day
		msg[startingByte+4] = byte(now.Month()/10<<4 | now.Month()%10)     // Month
		msg[startingByte+5] = byte((now.Year()%100)/10<<4 | now.Year()%10) // Year

		startingByte += LogMonitorResetRecordSize
	}

	// Add checksum
	msg[totalLength-1] = calculateChecksum(msg[:totalLength-1])

	return msg
}

// buildInvalidChecksumMsg creates a message with an invalid checksum
func buildInvalidChecksumMsg(numRecords int) []byte {
	msg := buildValidLogMonitorResetMsg(numRecords)
	msg[len(msg)-1] = calculateChecksum(msg[:len(msg)-1]) + 1 // Set invalid checksum
	return msg
}

// buildInvalidBCDTimeMsg creates a message with invalid BCD time format
func buildInvalidBCDTimeMsg(numRecords int) []byte {
	msg := buildValidLogMonitorResetMsg(numRecords)
	startingByte := HeaderLength + 1
	msg[startingByte+4] = 0x13 // Set invalid BCD month (13)
	msg[len(msg)-1] = calculateChecksum(msg[:len(msg)-1])
	return msg
}

// buildEdgeCaseTimeMsg creates a message with edge case time values
func buildEdgeCaseTimeMsg(numRecords int) []byte {
	// Calculate total length: HeaderLength + (numRecords * LogMonitorResetRecordSize) + HeaderOffset
	totalLength := HeaderLength + (numRecords * LogMonitorResetRecordSize) + HeaderOffset
	msg := make([]byte, totalLength)

	// Set header bytes (all zeros for test)
	for i := 0; i < HeaderLength; i++ {
		msg[i] = 0x00
	}

	// Set number of records
	msg[HeaderLength] = byte(numRecords)

	// Set edge case time values in BCD format
	startingByte := HeaderLength + 1

	// Edge case: 23:59:59 on December 31, 2099
	msg[startingByte] = 0x59   // Seconds: 59
	msg[startingByte+1] = 0x59 // Minutes: 59
	msg[startingByte+2] = 0x23 // Hours: 23
	msg[startingByte+3] = 0x31 // Day: 31
	msg[startingByte+4] = 0x12 // Month: 12
	msg[startingByte+5] = 0x99 // Year: 99

	// Add checksum
	msg[totalLength-1] = calculateChecksum(msg[:totalLength-1])

	return msg
}

// buildInvalidHeaderMsg creates a message with invalid header values
func buildInvalidHeaderMsg() []byte {
	// Create a message that's too short
	msg := make([]byte, HeaderLength-1)
	for i := range msg {
		msg[i] = 0x00
	}
	return msg
}

// calculateChecksum calculates the checksum for the message
func calculateChecksum(msg []byte) byte {
	var sum byte
	for _, b := range msg {
		sum += b
	}
	return ^sum // One's complement of sum
}

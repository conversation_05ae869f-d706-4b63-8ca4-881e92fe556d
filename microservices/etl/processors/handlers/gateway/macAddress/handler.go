package macAddress

import (
	"context"
	"fmt"
	"time"

	gatewayv1 "bitbucket.org/synapse-its/protobuf-schemas/protomessages/gateway/v1"
	"cloud.google.com/go/pubsub"
	"google.golang.org/protobuf/proto"
	"synapse-its.com/etl/processors/handlers/etlShared"
	"synapse-its.com/shared/bqbatch"
	"synapse-its.com/shared/connect"
	edihelper "synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/logger"
	"synapse-its.com/shared/pubsubdata"
	"synapse-its.com/shared/schemas"
)

// --- Dependency abstractions for injection and testing ---
type (
	ConnectorFunc           func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error)
	ParseAttributesFunc     func(attrs map[string]string) (pubsubdata.CommonAttributes, pubsubdata.HeaderDetails, error)
	DLQSender               func(ctx context.Context, client connect.PsClient, msg *pubsub.Message, reason string) error
	UnmarshalDeviceDataFunc func(raw []byte) (*gatewayv1.DeviceData, error)
	BatchGetter             func(ctx context.Context) (bqbatch.Batcher, error)
	ToBQConverter           func(orgID string, sgwID string, tz string, topic string, pubsubID string, deviceID string, pubsubTS time.Time, macAddress string) schemas.MacAddress
	MarshalDeviceDataFunc   func(msg proto.Message) ([]byte, error)
)

// HandlerDeps bundles all external dependencies.
type HandlerDeps struct {
	Connector       ConnectorFunc
	ParseAttributes ParseAttributesFunc
	SendToDLQ       DLQSender
	UnmarshalDevice UnmarshalDeviceDataFunc
	GetBatch        BatchGetter
	ToBQ            ToBQConverter
	MarshalDevice   MarshalDeviceDataFunc
}

func HandlerWithDeps(deps HandlerDeps) func(ctx context.Context, subscriptionName string) {
	return func(ctx context.Context, subscriptionName string) {
		batch, batchErr := deps.GetBatch(ctx)
		if batchErr != nil {
			logger.Errorf("Error getting batch: %v", batchErr)
			return
		}
		connections, err := deps.Connector(ctx)
		if err != nil {
			logger.Errorf("Error getting connections%v", err)
			return
		}
		sub := connections.Pubsub.Subscription(subscriptionName)
		err = sub.Receive(ctx, func(ctx context.Context, msg *pubsub.Message) {
			logger.Debugf("Received message on %s MessageID: %s. Message Data:%s", subscriptionName, string(msg.ID), string(msg.Data))

			// Parse Attributes
			commonAttrs, httpHeader, errPa := deps.ParseAttributes(msg.Attributes)
			if errPa != nil {
				logger.Errorf("Unable to parse attributes: %v", msg.Attributes)
				err = deps.SendToDLQ(ctx, connections.Pubsub, msg, fmt.Sprintf("Unable to parse attributes: %v", errPa))
				if err != nil {
					logger.Errorf("Error sending message to the DLQ topic %v", err)
					msg.Nack()
					return
				}
				msg.Ack()
				return
			}

			// Unmarshall protobuf message
			dd, errUm := deps.UnmarshalDevice(msg.Data)
			if errUm != nil {
				logger.Errorf("Error Unmarshaling the Device Data: %v", errUm)
				err = deps.SendToDLQ(ctx, connections.Pubsub, msg, fmt.Sprintf("Error Unmarshaling the Device Data: %v", errUm))
				if err != nil {
					logger.Errorf("Error sending message to the DLQ topic %v", err)
					msg.Nack()
					return
				}
				msg.Ack()
				return
			}

			// Loop through each record
			unprocessedDevices := new(gatewayv1.DeviceData)
			for i, d := range dd.GetMessages() {
				logger.Debugf("Processing record : %v, Deviceid : %v", i, d.DeviceId)
				msgBytes := d.GetMessage()

				// Add to the bigquery insert batch
				if err = batch.Add(deps.ToBQ(
					commonAttrs.OrganizationIdentifier,
					httpHeader.GatewayDeviceID,
					httpHeader.GatewayTimezone,
					commonAttrs.Topic,
					msg.ID,
					d.DeviceId,
					msg.PublishTime.UTC(),
					string(msgBytes),
				)); err != nil {
					logger.Infof("Error adding message to batch: %v", err)
					unprocessedDevices.Messages = append(unprocessedDevices.Messages, d)
					continue
				}
			}

			// Send unprocessed device messages to DLQ for further investigation
			if len(unprocessedDevices.Messages) != 0 {
				logger.Warnf("Unable to process (%v) device messages", len(unprocessedDevices.Messages))
				msg.Data, err = deps.MarshalDevice(unprocessedDevices)
				if err != nil {
					logger.Errorf("Unable to marshal unprocessed messages")
					msg.Ack() // At this point consider data lost, don't try to reprocess the whole batch
					return
				}

				// Send messages to DLQ
				err = deps.SendToDLQ(ctx, connections.Pubsub, msg, fmt.Sprintf("unable to process (%v) device messages", len(unprocessedDevices.Messages)))
				if err != nil {
					logger.Errorf("Error sending message to the DLQ topic %v", err)
					msg.Ack() // At this point consider data lost, don't try to reprocess the whole batch
					return
				}
			}

			msg.Ack()
		})
		if err != nil {
			logger.Errorf("Failed to receive messages from subscription"+subscriptionName+": %v", err)
		}
	}
}

// Handler is the production-ready Pub/Sub processor using real dependencies.
var Handler = HandlerWithDeps(HandlerDeps{
	Connector:       connect.GetConnections,
	ParseAttributes: pubsubdata.ParseAttributes,
	SendToDLQ:       etlShared.SendToDLQ,
	UnmarshalDevice: etlShared.UnmarshalDeviceData,
	GetBatch:        bqbatch.GetBatch,
	ToBQ:            edihelper.MacAddressToBQ,
	MarshalDevice:   etlShared.ProtoMarshal,
})

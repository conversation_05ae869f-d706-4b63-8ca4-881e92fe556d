import { Component, OnInit } from '@angular/core';
import { AuthService, UserProfile } from '../services/auth.service';

@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.css'],
  standalone: false,
})
export class HomeComponent implements OnInit {
  user: UserProfile | null = null;

  constructor(private auth: AuthService) { }

  ngOnInit() {
    this.auth.getProfile().subscribe({
      next: profile => this.user = profile,
      error: () => this.user = null
    });
  }
}

services:
  coordinator:
    build:
      context: ../
      dockerfile: microservices/coordinator/Dockerfile
      target: dev
    container_name: coordinator
    volumes:
      - ../microservices/coordinator:/app:cached
      - ../shared:/shared:cached
      - ../schemas:/schemas:cached
      - go-cache:/go/pkg/mod
      - go-build-cache:/root/.cache/go-build
      - ../creds.json:/workspace/creds.json:ro
    env_file:
      - .env
      - .env.local
      - .env.coordinator.local
    environment:
      - CURRENT_TIMESTAMP=${CURRENT_TIMESTAMP}
    ports:
      - "8083:8081" # K8S healthz service (needs mapping to avoid colissions in dev)
    networks:
      - test-network
    init: true # Use a minimal init process to forward signals properly.

  broker:
    build:
      context: ../
      dockerfile: microservices/broker/Dockerfile
      target: dev
    container_name: broker
    volumes:
      - ../microservices/broker:/app:cached
      - ../shared:/shared:cached
      - go-cache:/go/pkg/mod
      - go-build-cache:/root/.cache/go-build
      - ../creds.json:/workspace/creds.json:ro
    env_file:
      - .env
      - .env.local
      - .env.broker.local
    environment:
      - CURRENT_TIMESTAMP=${CURRENT_TIMESTAMP}
    ports:
      - "8080:8080"
      - "8081:8081" # K8S healthz service (needs mapping to avoid colissions in dev)
    networks:
      - test-network
    init: true # Use a minimal init process to forward signals properly.

  etl:
    build:
      context: ../
      dockerfile: microservices/etl/Dockerfile
      target: dev
    container_name: etl
    volumes:
      - ../microservices/etl:/app:cached
      - ../shared:/shared:cached
      - go-cache:/go/pkg/mod
      - go-build-cache:/root/.cache/go-build
      - ../creds.json:/workspace/creds.json:ro
    env_file:
      - .env
      - .env.local
      - .env.etl.local
    environment:
      - CURRENT_TIMESTAMP=${CURRENT_TIMESTAMP}
    ports:
      - "8082:8081" # K8S healthz service (needs mapping to avoid colissions in dev)
    networks:
      - test-network
    init: true # Use a minimal init process to forward signals properly.

  onramp:
    build:
      context: ../
      dockerfile: microservices/onramp/Dockerfile
      target: dev
    container_name: onramp
    volumes:
      - ../microservices/onramp:/app:cached
      - ../shared:/shared:cached
      - ../schemas:/schemas:cached
      - go-cache:/go/pkg/mod
      - go-build-cache:/root/.cache/go-build
      - npm-cache-devcontainer:/root/.npm:cached
      - onramp-ui-node-modules:/app/.ui/node_modules:cached
      - onramp-ui-angular-cache:/app/.ui/.angular:cached
    env_file:
      - .env
      - .env.local
      - .env.onramp.local
    environment:
      - CURRENT_TIMESTAMP=${CURRENT_TIMESTAMP}
      # This is needed to avoid the Angular CLI from showing progress bars
      # which can cause issues in CI/CD environments.
      - NG_CLI_PROGRESS=false
      - CI=true
    ports:
      - "8084:8081" # K8S healthz service (needs mapping to avoid colissions in dev)
      - "8090:8080" # Onramp
      - "4200:4200" # Angular live reload
    networks:
      - test-network
    extra_hosts:
      - "host.docker.internal:host-gateway"
    init: true # Use a minimal init process to forward signals properly.

  selenium:
    image: selenium/standalone-chrome:latest
    container_name: selenium
    env_file:
      - .env
      - .env.local
    networks:
      - test-network

  cucumber:
    build:
      context: ../
      dockerfile: microservices/onramp/Dockerfile
      target: cucumber
    container_name: cucumber
    volumes:
      - ../microservices/onramp/.e2e:/e2e:cached
      - onramp-ui-e2e-node-modules:/e2e/node_modules:cached
    environment:
      SELENIUM_HOST: selenium
      SELENIUM_PORT: 4444
    networks:
      - test-network

  # This is a local version of Keycloak to act as our local identity provider
  # for development and testing.
  keycloak:
    build:
      context: ../external/keycloak
    container_name: keycloak
    environment:
      - KC_BOOTSTRAP_ADMIN_USERNAME=admin
      - KC_BOOTSTRAP_ADMIN_PASSWORD=admin
      - KC_LOG_LEVEL=WARN
      # Raise the level for the DefaultCookieProvider to ERROR, otherwise it
      # spams the logs with warnings about cookies.
      - KC_LOG_CATEGORY_org_keycloak_cookie_DefaultCookieProvider=ERROR
      - KC_LOG_CONSOLE_ENABLE=true
      - KC_HTTP_ENABLED=true
      - KC_HOSTNAME_STRICT=false
      - KC_PROXY_HEADERS=xforwarded
    volumes:
      - ../external/keycloak/:/app:cached
      - keycloak-realm:/opt/keycloak/realm-storage
    ports:
      - "8091:8080"
    networks:
      - test-network

  pubsub:
    image: gcr.io/google.com/cloudsdktool/cloud-sdk
    container_name: pubsub
    environment:
      - MEMORYSTORE_HOST=${MEMORYSTORE_HOST}
      - MEMORYSTORE_PORT=${MEMORYSTORE_PORT}
    networks:
      - test-network
    ports:
      - "8085:8085"
    command: gcloud beta emulators pubsub start --host-port=0.0.0.0:8085
    init: true # Use a minimal init process to forward signals properly.

  # This version of the BigQuery emulator is running from the official image
  # from goccy.
  # bigquery:
  #   image: ghcr.io/goccy/bigquery-emulator
  #   container_name: bigquery
  #   networks:
  #     - test-network
  #   ports:
  #     - "9050:9050"
  #   command: [ "--project", "${GCP_PROJECT_ID}", "--port", "9050", "--log-level=warn" ]

  # This version of the BigQuery emulator is running a local version of the
  # emulator which can be patched or hacked if we need to.  It is not enabled
  # by default.
  bigquery:
    # If you are debugging the BigQuery emulator, you can uncomment the build section
    # to build the image from the local patched version.  It takes 20-30 minutes to build.
    build:
      context: ../external/bigquery-emulator-patched
      target: wsl
      # target: hacked

      # Instead of building the image, this will use the pre-built image from our repo.
      # image: infra-bigquery:latest

      # The rest is the same for both versions.
    container_name: bigquery
    networks:
      - test-network
    ports:
      - "9050:9050"
    # Change log-level to "info" to see all queries.  "debug" for all queries and responses.
    # If trying to get the https support working, add this to the command: "--https-port", "9070"
    command: [ "--project", "${GCP_PROJECT_ID}", "--port", "9050", "--log-level=warn" ]
    # Used for WSL build only:
    volumes:
      - "../external/bigquery-emulator-patched/unzipAndRun.sh:/app/unzipAndRun.sh:cached"
      - "../external/bigquery-emulator-patched/bqbin.gz:/app/bqbin.gz:ro"
      - bqemulator-bin:/app/bin

  redis:
    image: redis:6-alpine
    container_name: redis
    environment:
      - MEMORYSTORE_HOST=${MEMORYSTORE_HOST}
      - MEMORYSTORE_PORT=${MEMORYSTORE_PORT}
    ports:
      - "6379:6379"
    networks:
      - test-network

  postgres:
    image: postgres:17-alpine
    container_name: cloudsql
    environment:
      - POSTGRES_HOST=${POSTGRES_HOST}
      - POSTGRES_PORT=${POSTGRES_PORT}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DB}
    ports:
      - "5432:5432"
    networks:
      - test-network

  firestore:
    image: gcr.io/google.com/cloudsdktool/cloud-sdk
    container_name: firestore
    networks:
      - test-network
    ports:
      - "8086:8086"
    command: gcloud beta emulators firestore start --host-port=0.0.0.0:8086
    init: true

  testing:
    build:
      context: ../
      dockerfile: microservices/testing/Dockerfile
    container_name: testing
    volumes:
      - ../microservices/testing:/app:cached
      - ../microservices:/microservices:cached
      - ../shared:/shared:cached
      - ../schemas:/schemas:cached
      - go-cache:/go/pkg/mod
      - go-build-cache:/root/.cache/go-build
      - ../creds.json:/workspace/creds.json:ro
    env_file:
      - .env
      - .env.local
      - .env.testing.local
    environment:
      - CURRENT_TIMESTAMP=${CURRENT_TIMESTAMP}
    networks:
      - test-network
    init: true # Use a minimal init process to forward signals properly.

volumes:
  go-cache:
  go-build-cache:
  go-build-cache-bigquery-emulator:
  npm-cache-devcontainer:
  onramp-ui-e2e-node-modules:
  onramp-ui-node-modules:
  onramp-ui-angular-cache:
  bqemulator-bin:
  keycloak-realm:


networks:
  test-network:
    driver: bridge

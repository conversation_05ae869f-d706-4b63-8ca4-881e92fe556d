package authenticate

import (
	"context"
	"database/sql"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	connect "synapse-its.com/shared/connect"
)

// Test_WebAccess tests the WebAccess function with various scenarios.
func Test_WebAccess(t *testing.T) {
	t.<PERSON>llel()
	// Define test cases.
	tests := []struct {
		name                string                                    // Descriptive test name
		userName            string                                    // Username input
		password            string                                    // Password input
		setupHandler        func(*UserHandler, *MockDatabaseExecutor) // Setup function for the handler
		expectedStatusCode  int                                       // Expected HTTP status code
		expectedTokenInBody bool                                      // Whether token should be in response body
		expectedError       error                                     // Expected error (if any)
	}{
		{
			name:     "Success_ValidCredentials_WebEnabled",
			userName: "testuser",
			password: "testpass",
			setupHandler: func(h *UserHandler, db *MockDatabaseExecutor) {
				// Setup connection provider
				h.DBProvider = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return &connect.Connections{Postgres: db}, nil
				}

				// Mock password hasher
				h.PasswordHasher = func(password string) string {
					return "hashed_" + password
				}

				// Mock QueryRowStruct to return a valid user
				db.On("QueryRowStruct", mock.AnythingOfType("*authenticate.dbUser"), mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					user := args.Get(0).(*dbUser)
					user.Id = 123
					user.RoleId = 1
					user.WebEnabled = 1
					user.WebTokenDurationSeconds = 3600
				}).Return(nil)

				// Mock Exec for updating login time
				mockResult := new(MockSQLResult)
				mockResult.On("LastInsertId").Return(int64(1), nil)
				mockResult.On("RowsAffected").Return(int64(1), nil)
				db.On("Exec", mock.Anything, mock.Anything, mock.Anything).Return(mockResult, nil)

				// Mock ValidateRoleFunc
				h.ValidateRoleFunc = func(roleID int64) (string, error) {
					return "user", nil
				}

				// Mock TimeProvider
				h.TimeProvider = func() time.Time {
					return time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC)
				}

				// Mock NonceGenerator
				h.NonceGenerator = func(length int) ([]byte, error) {
					return []byte("testnonce"), nil
				}

				// Mock TokenPersister
				h.TokenPersister = func(pg connect.DatabaseExecutor, userID int64, jwt string, expiresAt time.Time) error {
					return nil
				}
			},
			expectedStatusCode:  200,
			expectedTokenInBody: true,
			expectedError:       nil,
		},
		{
			name:     "Error_DBConnectionFailure",
			userName: "testuser",
			password: "testpass",
			setupHandler: func(h *UserHandler, db *MockDatabaseExecutor) {
				// Setup connection provider to return error
				h.DBProvider = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return nil, errors.New("connection error")
				}

				// Mock password hasher
				h.PasswordHasher = func(password string) string {
					return "hashed_" + password
				}
			},
			expectedStatusCode:  500,
			expectedTokenInBody: false,
			expectedError:       errors.New("connection error"),
		},
		{
			name:     "Error_InvalidCredentials",
			userName: "testuser",
			password: "wrongpass",
			setupHandler: func(h *UserHandler, db *MockDatabaseExecutor) {
				// Setup connection provider
				h.DBProvider = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return &connect.Connections{Postgres: db}, nil
				}

				// Mock password hasher
				h.PasswordHasher = func(password string) string {
					return "hashed_" + password
				}

				// Mock QueryRowStruct to return no rows (invalid credentials)
				db.On("QueryRowStruct", mock.AnythingOfType("*authenticate.dbUser"), mock.Anything, mock.Anything, mock.Anything).Return(sql.ErrNoRows)

				// Mock Exec for updating failed login attempts
				mockResult := new(MockSQLResult)
				mockResult.On("LastInsertId").Return(int64(1), nil)
				mockResult.On("RowsAffected").Return(int64(1), nil)
				db.On("Exec", mock.Anything, mock.Anything).Return(mockResult, nil)
			},
			expectedStatusCode:  401,
			expectedTokenInBody: false,
			expectedError:       ErrInvalidCredentials,
		},
		{
			name:     "Error_WebAccessNotEnabled",
			userName: "testuser",
			password: "testpass",
			setupHandler: func(h *UserHandler, db *MockDatabaseExecutor) {
				// Setup connection provider
				h.DBProvider = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return &connect.Connections{Postgres: db}, nil
				}

				// Mock password hasher
				h.PasswordHasher = func(password string) string {
					return "hashed_" + password
				}

				// Mock QueryRowStruct to return a valid user with web access disabled
				db.On("QueryRowStruct", mock.AnythingOfType("*authenticate.dbUser"), mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					user := args.Get(0).(*dbUser)
					user.Id = 123
					user.RoleId = 1
					user.WebEnabled = 0 // Web access disabled
					user.WebTokenDurationSeconds = 3600
				}).Return(nil)

				// Mock ValidateRoleFunc
				h.ValidateRoleFunc = func(roleID int64) (string, error) {
					return "user", nil
				}

				// Mock TimeProvider
				h.TimeProvider = func() time.Time {
					return time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC)
				}

				// Mock Exec for updating login time
				mockResult := new(MockSQLResult)
				mockResult.On("LastInsertId").Return(int64(1), nil)
				mockResult.On("RowsAffected").Return(int64(1), nil)
				db.On("Exec", mock.Anything, mock.Anything, mock.Anything).Return(mockResult, nil)
			},
			expectedStatusCode:  401,
			expectedTokenInBody: false,
			expectedError:       ErrWebAccessNotEnabled,
		},
		{
			name:     "Error_NonceGenerationFailure",
			userName: "testuser",
			password: "testpass",
			setupHandler: func(h *UserHandler, db *MockDatabaseExecutor) {
				// Setup connection provider
				h.DBProvider = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return &connect.Connections{Postgres: db}, nil
				}

				// Mock password hasher
				h.PasswordHasher = func(password string) string {
					return "hashed_" + password
				}

				// Mock QueryRowStruct to return a valid user
				db.On("QueryRowStruct", mock.AnythingOfType("*authenticate.dbUser"), mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					user := args.Get(0).(*dbUser)
					user.Id = 123
					user.RoleId = 1
					user.WebEnabled = 1
					user.WebTokenDurationSeconds = 3600
				}).Return(nil)

				// Mock ValidateRoleFunc
				h.ValidateRoleFunc = func(roleID int64) (string, error) {
					return "user", nil
				}

				// Mock Exec for updating login time
				mockResult := new(MockSQLResult)
				mockResult.On("LastInsertId").Return(int64(1), nil)
				mockResult.On("RowsAffected").Return(int64(1), nil)
				db.On("Exec", mock.Anything, mock.Anything, mock.Anything).Return(mockResult, nil)

				// Mock TimeProvider
				h.TimeProvider = func() time.Time {
					return time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC)
				}

				// Mock NonceGenerator to return error
				h.NonceGenerator = func(length int) ([]byte, error) {
					return nil, errors.New("nonce generation error")
				}
			},
			expectedStatusCode:  401,
			expectedTokenInBody: false,
			expectedError:       ErrNonceGeneration,
		},
		{
			name:     "Error_DatabaseQueryError",
			userName: "testuser",
			password: "testpass",
			setupHandler: func(h *UserHandler, db *MockDatabaseExecutor) {
				// Setup connection provider
				h.DBProvider = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return &connect.Connections{Postgres: db}, nil
				}

				// Mock password hasher
				h.PasswordHasher = func(password string) string {
					return "hashed_" + password
				}

				// Mock QueryRowStruct to return a database error
				db.On("QueryRowStruct", mock.AnythingOfType("*authenticate.dbUser"), mock.Anything, mock.Anything, mock.Anything).Return(errors.New("database error"))
			},
			expectedStatusCode:  401,
			expectedTokenInBody: false,
			expectedError:       ErrDatabaseQuery,
		},
		{
			name:     "Error_RoleValidationError",
			userName: "testuser",
			password: "testpass",
			setupHandler: func(h *UserHandler, db *MockDatabaseExecutor) {
				// Setup connection provider
				h.DBProvider = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return &connect.Connections{Postgres: db}, nil
				}

				// Mock password hasher
				h.PasswordHasher = func(password string) string {
					return "hashed_" + password
				}

				// Mock QueryRowStruct to return a valid user
				db.On("QueryRowStruct", mock.AnythingOfType("*authenticate.dbUser"), mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					user := args.Get(0).(*dbUser)
					user.Id = 123
					user.RoleId = 999 // Invalid role ID
					user.WebEnabled = 1
					user.WebTokenDurationSeconds = 3600
				}).Return(nil)

				// Mock ValidateRoleFunc to return error
				h.ValidateRoleFunc = func(roleID int64) (string, error) {
					return "", errors.New("invalid role ID")
				}
			},
			expectedStatusCode:  401,
			expectedTokenInBody: false,
			expectedError:       ErrRoleValidation,
		},
		{
			name:     "Error_LoginTimeUpdateError",
			userName: "testuser",
			password: "testpass",
			setupHandler: func(h *UserHandler, db *MockDatabaseExecutor) {
				// Setup connection provider
				h.DBProvider = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return &connect.Connections{Postgres: db}, nil
				}

				// Mock password hasher
				h.PasswordHasher = func(password string) string {
					return "hashed_" + password
				}

				// Mock QueryRowStruct to return a valid user
				db.On("QueryRowStruct", mock.AnythingOfType("*authenticate.dbUser"), mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					user := args.Get(0).(*dbUser)
					user.Id = 123
					user.RoleId = 1
					user.WebEnabled = 1
					user.WebTokenDurationSeconds = 3600
				}).Return(nil)

				// Mock ValidateRoleFunc
				h.ValidateRoleFunc = func(roleID int64) (string, error) {
					return "user", nil
				}

				// Mock TimeProvider
				h.TimeProvider = func() time.Time {
					return time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC)
				}

				// Mock Exec for updating login time to return error
				mockResult := new(MockSQLResult)
				mockResult.On("LastInsertId").Return(int64(1), nil)
				mockResult.On("RowsAffected").Return(int64(1), nil)
				db.On("Exec", mock.Anything, mock.Anything, mock.Anything).Return(mockResult, errors.New("update error"))

				// Mock NonceGenerator
				h.NonceGenerator = func(length int) ([]byte, error) {
					return []byte("testnonce"), nil
				}

				// Mock TokenPersister
				h.TokenPersister = func(pg connect.DatabaseExecutor, userID int64, jwt string, expiresAt time.Time) error {
					return nil
				}
			},
			expectedStatusCode:  200, // Non-critical error, should still succeed
			expectedTokenInBody: true,
			expectedError:       nil,
		},
		{
			name:     "Error_TokenPersistenceError",
			userName: "testuser",
			password: "testpass",
			setupHandler: func(h *UserHandler, db *MockDatabaseExecutor) {
				// Setup connection provider
				h.DBProvider = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return &connect.Connections{Postgres: db}, nil
				}

				// Mock password hasher
				h.PasswordHasher = func(password string) string {
					return "hashed_" + password
				}

				// Mock QueryRowStruct to return a valid user
				db.On("QueryRowStruct", mock.AnythingOfType("*authenticate.dbUser"), mock.Anything, mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					user := args.Get(0).(*dbUser)
					user.Id = 123
					user.RoleId = 1
					user.WebEnabled = 1
					user.WebTokenDurationSeconds = 3600
				}).Return(nil)

				// Mock ValidateRoleFunc
				h.ValidateRoleFunc = func(roleID int64) (string, error) {
					return "user", nil
				}

				// Mock TimeProvider
				h.TimeProvider = func() time.Time {
					return time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC)
				}

				// Mock Exec for updating login time
				mockResult := new(MockSQLResult)
				mockResult.On("LastInsertId").Return(int64(1), nil)
				mockResult.On("RowsAffected").Return(int64(1), nil)
				db.On("Exec", mock.Anything, mock.Anything, mock.Anything).Return(mockResult, nil)

				// Mock NonceGenerator
				h.NonceGenerator = func(length int) ([]byte, error) {
					return []byte("testnonce"), nil
				}

				// Mock TokenPersister to return error
				h.TokenPersister = func(pg connect.DatabaseExecutor, userID int64, jwt string, expiresAt time.Time) error {
					return errors.New("token persistence error")
				}
			},
			expectedStatusCode:  401,
			expectedTokenInBody: false,
			expectedError:       ErrTokenPersistence,
		},
	}

	// Execute all test cases.
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			// Create handler and mock DB
			handler := &UserHandler{}
			mockDB := new(MockDatabaseExecutor)

			// Setup handler
			tt.setupHandler(handler, mockDB)

			// Create a request.
			req, err := http.NewRequest("GET", "/authenticate", nil)
			if err != nil {
				t.Fatal(err)
			}

			// Create a response recorder.
			rr := httptest.NewRecorder()

			// Call the WebAccess function.
			handler.WebAccess(tt.userName, tt.password, rr, req)

			// Check the status code.
			assert.Equal(t, tt.expectedStatusCode, rr.Code, "Status code should match expected")

			// Check if token is in the response body for successful cases.
			if tt.expectedTokenInBody {
				assert.Contains(t, rr.Body.String(), "token", "Response should contain token")
			}

			// Verify all mock expectations were met
			mockDB.AssertExpectations(t)
		})
	}
}

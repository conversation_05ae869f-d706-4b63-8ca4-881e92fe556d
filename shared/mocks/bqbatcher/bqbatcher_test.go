package bqbatcher

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/bqbatch"
)

func TestFakeBatcherWithOptions_Defaults(t *testing.T) {
	// Build a FakeBatcher with no options
	b := FakeBatcherWithOptions()

	// By default, RegisterFn, AddFn, and ShutdownFn are non-nil,
	// but LoadBatchFn is nil. So we only test the methods whose Fn is set.

	// 1) Register should return nil (default)
	err := b.Register(struct{}{}, "table", bqbatch.QueueConfig{MaxSize: 1, FlushInterval: 0})
	assert.NoError(t, err, "expected default Register to return nil")

	// 2) Add should return nil (default)
	err = b.Add("row")
	assert.NoError(t, err, "expected default Add to return nil")

	// 3) Shutdown should return nil (default)
	err = b.Shutdown()
	assert.NoError(t, err, "expected default Shutdown to return nil")

	// 4) LoadBatch would panic if called now, because LoadBatchFn is nil.
	// We do not call it here.
}

func TestFakeBatcherWithOptions_ShutdownError(t *testing.T) {
	// Override only ShutdownFn
	sentinel := errors.New("shutdown-failed")
	b := FakeBatcherWithOptions(WithBatchShutdownError(sentinel))

	// Register/Add still use defaults
	assert.NoError(t, b.Register(struct{}{}, "table", bqbatch.QueueConfig{}))
	assert.NoError(t, b.Add(42))

	// Shutdown should now return the sentinel error
	err := b.Shutdown()
	assert.Equal(t, sentinel, err, "expected Shutdown to return sentinel error")
}

func TestFakeBatcherWithCustomOption_RegisterError(t *testing.T) {
	// Override RegisterFn
	sentinel := errors.New("register-failed")
	customOpt := func(f *FakeBatcher) {
		f.RegisterFn = func(rowExample interface{}, table string, cfg bqbatch.QueueConfig) error {
			return sentinel
		}
	}
	b := FakeBatcherWithOptions(customOpt)

	// Register returns sentinel
	err := b.Register("row", "table", bqbatch.QueueConfig{})
	assert.Equal(t, sentinel, err, "expected custom RegisterFn to return sentinel")

	// Other methods still use default (nil)
	assert.NoError(t, b.Add("row"))
	assert.NoError(t, b.Shutdown())
}

func TestFakeBatcherWithCustomOption_AddError(t *testing.T) {
	// Override AddFn
	sentinel := errors.New("add-failed")
	customOpt := func(f *FakeBatcher) {
		f.AddFn = func(row interface{}) error {
			return sentinel
		}
	}
	b := FakeBatcherWithOptions(customOpt)

	// Add returns sentinel
	err := b.Add("row")
	assert.Equal(t, sentinel, err, "expected custom AddFn to return sentinel")

	// Other methods still use default (nil)
	assert.NoError(t, b.Register(struct{}{}, "table", bqbatch.QueueConfig{}))
	assert.NoError(t, b.Shutdown())
}

func TestFakeBatcherWithCustomOption_LoadBatchError(t *testing.T) {
	// Override LoadBatchFn
	sentinel := errors.New("loadbatch-failed")
	customOpt := func(f *FakeBatcher) {
		f.LoadBatchFn = func(fb bqbatch.FailedBatch) error {
			return sentinel
		}
	}
	b := FakeBatcherWithOptions(customOpt)

	// LoadBatch returns sentinel
	err := b.LoadBatch(bqbatch.FailedBatch{Table: "table"})
	assert.Equal(t, sentinel, err, "expected custom LoadBatchFn to return sentinel")

	// Other methods still use default (nil)
	assert.NoError(t, b.Register(struct{}{}, "table", bqbatch.QueueConfig{}))
	assert.NoError(t, b.Add("row"))
	assert.NoError(t, b.Shutdown())
}

func TestFakeBatch_Defaults(t *testing.T) {
	ctx := context.Background()
	batcherIface, err := FakeBatch(ctx)
	assert.NoError(t, err, "FakeBatch should not return an error")

	// Underlying type should be *FakeBatcher
	fb, ok := batcherIface.(*FakeBatcher)
	assert.True(t, ok, "FakeBatch should return *FakeBatcher")

	// All four Fn fields (RegisterFn, AddFn, LoadBatchFn, ShutdownFn) are non-nil
	assert.NotNil(t, fb.RegisterFn, "RegisterFn must be non-nil")
	assert.NotNil(t, fb.AddFn, "AddFn must be non-nil")
	assert.NotNil(t, fb.LoadBatchFn, "LoadBatchFn must be non-nil")
	assert.NotNil(t, fb.ShutdownFn, "ShutdownFn must be non-nil")

	// Verify Register returns nil
	assert.NoError(t, batcherIface.Register(struct{}{}, "any", bqbatch.QueueConfig{}))

	// Verify Add returns nil
	assert.NoError(t, batcherIface.Add("anything"))

	// Verify LoadBatch returns nil
	assert.NoError(t, batcherIface.LoadBatch(bqbatch.FailedBatch{Table: "any"}))

	// Verify Shutdown returns nil
	assert.NoError(t, batcherIface.Shutdown())
}

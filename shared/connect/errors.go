package connect

import "errors"

var (
	// Postgres() errors
	ErrPostgresUserNotSet     = errors.New("POSTGRES_USER environment variable not set")
	ErrPostgresPasswordNotSet = errors.New("POSTGRES_PASSWORD environment variable not set")
	ErrPostgresDBNotSet       = errors.New("POSTGRES_DB environment variable not set")
	ErrPostgresHostNotSet     = errors.New("POSTGRES_HOST environment variable not set")
	ErrPostgresPortNotSet     = errors.New("POSTGRES_PORT environment variable not set")
	ErrPostgresConnection     = errors.New("Failed to connect to PostgreSQL")

	// QueryRowStruct() errors
	ErrDestNotPointer  = errors.New("dest must be a non-nil pointer")
	ErrDestNotStruct   = errors.New("dest must point to a struct")
	ErrNoMatchingField = errors.New("no matching struct field found for column")
	ErrUnexportedField = errors.New("cannot scan into unexported field")

	// QueryGenericSlice() errors
	ErrDestNotSlice       = errors.New("dest must point to a slice")
	ErrSliceElemNotStruct = errors.New("dest slice element must be a struct")

	// ExecMultiple() errors
	ErrExecStatement = errors.New("error executing statement")
)

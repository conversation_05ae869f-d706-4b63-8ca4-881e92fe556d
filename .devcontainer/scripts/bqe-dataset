#!/usr/bin/env bash
# Description: List or set the current BigQuery dataset
#   bqe dataset
#     Show the current dataset
#   bqe dataset <value>
#     Set the current dataset to <value>

set -euo pipefail

# find the existing dataset (empty if unset)
current=$(bqe-get dataset 2>/dev/null || echo "test_dataset")

# set new dataset, if provided
if [ $# -gt 0 ]; then
  bqe-set dataset "$1"
  echo "Dataset set to '$1'"
else
  echo "$current"
fi

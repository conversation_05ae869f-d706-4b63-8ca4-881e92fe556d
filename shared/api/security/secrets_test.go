package security

import (
	"encoding/base64"
	"os"
	"reflect"
	"testing"

	"synapse-its.com/shared/util/test"
)

func TestGetJWTAsymmetric_TableDriven(t *testing.T) {
	const (
		privTest = "privatekeytest"
		pubTest  = "publickeytest"
	)
	encodedPriv := base64.StdEncoding.EncodeToString([]byte(privTest))
	encodedPub := base64.StdEncoding.EncodeToString([]byte(pubTest))

	tests := []struct {
		name      string
		privEnv   string
		pubEnv    string
		wantPriv  string
		wantPub   string
		expectErr bool
	}{
		{
			name:      "valid keys",
			privEnv:   encodedPriv,
			pubEnv:    encodedPub,
			wantPriv:  privTest,
			wantPub:   pubTest,
			expectErr: false,
		},
		{
			name:      "invalid private key (bad base64)",
			privEnv:   "not-base64",
			pubEnv:    encodedPub,
			expectErr: true,
		},
		{
			name:      "invalid public key (bad base64)",
			privEnv:   encodedPriv,
			pubEnv:    "not-base64",
			expectErr: true,
		},
	}

	for _, tc := range tests {
		tc := tc // capture range variable
		t.Run(tc.name, func(t *testing.T) {
			// Capture original env
			origPriv, privWasSet := os.LookupEnv("JWT_PRIVATE_KEY")
			origPub, pubWasSet := os.LookupEnv("JWT_PUBLIC_KEY")
			// Restore original env at end
			defer func() {
				if privWasSet {
					os.Setenv("JWT_PRIVATE_KEY", origPriv)
				} else {
					os.Unsetenv("JWT_PRIVATE_KEY")
				}
				if pubWasSet {
					os.Setenv("JWT_PUBLIC_KEY", origPub)
				} else {
					os.Unsetenv("JWT_PUBLIC_KEY")
				}
			}()

			// Set up test env
			if tc.privEnv == "" {
				os.Unsetenv("JWT_PRIVATE_KEY")
			} else {
				os.Setenv("JWT_PRIVATE_KEY", tc.privEnv)
			}
			if tc.pubEnv == "" {
				os.Unsetenv("JWT_PUBLIC_KEY")
			} else {
				os.Setenv("JWT_PUBLIC_KEY", tc.pubEnv)
			}

			priv, pub, err := GetJWTAsymmetric()
			if tc.expectErr {
				if err == nil {
					t.Errorf("expected error, got none")
				}
				return
			}
			if err != nil {
				t.Fatalf("unexpected error: %v", err)
			}
			if priv != tc.wantPriv {
				t.Errorf("private key: want %q, got %q", tc.wantPriv, priv)
			}
			if pub != tc.wantPub {
				t.Errorf("public key:  want %q, got %q", tc.wantPub, pub)
			}
		})
	}
}

func TestTypesJSONMarshalable_ByType(t *testing.T) {
	// Because we validate these types here to see if they json.Marshal() without
	// error, then when we call json.Marshal() in our code, we do not have to
	// check for an error, making our test coverage simpler to manage.
	test.AssertJSONTypeMarshalable(t, reflect.TypeOf(jwtAsymmetricSecret{}))
}

package jwttokens

import (
	"time"
)

// JwtToken struct is the format for Synapse Jwt tokens.
type JwtToken struct {
	IssuedBy    string    `json:"issued_by"`   // who issued the token
	ExpiresAt   time.Time `json:"expires_at"`  // when the token expires
	IssuedAt    time.Time `json:"issued_at"`   // time the token was issued
	JwtId       string    `json:"jwt_id"`      // uniuqely identifies the token
	AuthId      string    `json:"auth_id"`     // the username
	Role        string    `json:"role"`        // the role of the username
	Permissions string    `json:"permissions"` // a base64 encoded string of permissions
}

// User device access that is included in the JwtToken.Permissions
type UserDeviceAccess struct {
	Device string `json:"device_id"`
}

// User software gateway access that is included in the JwtToken.Permissions
type UserSoftwareGatewayAccess struct {
	SoftwareGateway string `json:"software_gateway_id"`
}

// The total permission set that is returned when extracting the user permissions from the JwtToken.Permissions attribute
type UserPermissions struct {
	SoftwareGateway []UserSoftwareGatewayAccess `json:"software_gateway"`
	Device          []UserDeviceAccess          `json:"device"`
}

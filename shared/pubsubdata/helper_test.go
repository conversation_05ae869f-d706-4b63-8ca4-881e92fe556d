package pubsubdata

import (
	"encoding/json"
	"testing"
	"time"

	"cloud.google.com/go/pubsub"
	"github.com/stretchr/testify/assert"
)

func TestBuildAttributes(t *testing.T) {
	common := CommonAttributes{
		Topic:                  "broker-topic",
		OrganizationIdentifier: "org123",
		DeviceType:             "gateway",
		DLQReason:              "bad-payload",
	}
	headers := HeaderDetails{
		Host:            "example.com",
		UserAgent:       "unit-test",
		ContentLength:   "123",
		ContentType:     "application/json",
		GatewayDeviceID: "dev-001",
		MessageVersion:  "v1",
		MessageType:     "status",
		APIKey:          "should-be-removed",
		GatewayTimezone: "UTC",
	}

	attrs := BuildAttributes(common, headers)

	assert.Equal(t, "broker-topic", attrs["topic"])
	assert.Equal(t, "org123", attrs["org_id"])
	assert.Equal(t, "gateway", attrs["device_type"])
	assert.Equal(t, "bad-payload", attrs["dlq_reason"])
	assert.NotEmpty(t, attrs["receive_timestamp"])

	var parsedHeaders HeaderDetails
	assert.NoError(t, json.Unmarshal([]byte(attrs["http_headers"]), &parsedHeaders))
	assert.Equal(t, "", parsedHeaders.APIKey) // Ensure APIKey was removed
	assert.Equal(t, "example.com", parsedHeaders.Host)
}

func TestSetAttribute(t *testing.T) {
	msg := &pubsub.Message{}
	SetAttribute(msg, "foo", "bar")
	assert.Equal(t, "bar", msg.Attributes["foo"])

	// Should still work if Attributes is already set
	SetAttribute(msg, "baz", "qux")
	assert.Equal(t, "qux", msg.Attributes["baz"])
}

func TestParseAttributes_Success(t *testing.T) {
	now := time.Now().UTC().Truncate(time.Second)
	headers := HeaderDetails{
		Host: "host",
	}
	headerJSON, _ := json.Marshal(headers)

	attrs := map[string]string{
		"topic":             "broker-topic",
		"org_id":            "org123",
		"device_type":       "gateway",
		"dlq_reason":        "bad",
		"receive_timestamp": now.Format(time.RFC3339),
		"http_headers":      string(headerJSON),
	}

	common, parsedHeaders, err := ParseAttributes(attrs)
	assert.NoError(t, err)
	assert.Equal(t, "broker-topic", common.Topic)
	assert.Equal(t, "org123", common.OrganizationIdentifier)
	assert.Equal(t, "gateway", common.DeviceType)
	assert.Equal(t, "bad", common.DLQReason)
	assert.WithinDuration(t, now, common.ReceiveTimestamp, time.Second)
	assert.Equal(t, "host", parsedHeaders.Host)
}

func TestParseAttributes_InvalidTimestamp(t *testing.T) {
	attrs := map[string]string{
		"receive_timestamp": "invalid-time",
	}
	_, _, err := ParseAttributes(attrs)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "failed to parse receive_timestamp")
}

func TestParseAttributes_InvalidHeaderJSON(t *testing.T) {
	attrs := map[string]string{
		"receive_timestamp": time.Now().UTC().Format(time.RFC3339),
		"http_headers":      "{bad-json",
	}
	_, _, err := ParseAttributes(attrs)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "failed to unmarshal http_headers")
}

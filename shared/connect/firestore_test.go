package connect

import (
	"context"
	"errors"
	"testing"

	"cloud.google.com/go/firestore"
	"github.com/stretchr/testify/assert"
	"google.golang.org/api/option"
)

// Firestore config (a base64 encoded authorization struct, set to &security.GoogleSettings{ProjectId: "proj"})
const magicEncodedKey = "gFizl86slbe5IGllZyhBDruv6hYCfKCyn/KH5IQeBcSEbvXVJeu8lTYW3cp3IDAkkSfuACCALHzT6gudrFDb12a3VU9WThkaT6ShdEeB8nUwweqZO6qgSo5img3TUmEZH709DxFL/FkRe5wvNESVMUksEOkF26jdAltdzkaC37OGPo1PKOU9XXaDI3SNdJKGRF44v4XzNmbyQRzWmORFJVXJU3iQuVJ59Ejz015qSxdC6l6vBXA38uWG46c2JxuWNboqVDe7hr08E2VWpB48cZfceAW8+XZZMdXQEg+qwTY4VO1/Yw4IY+lpw105"

func TestFirestore_AllBranches(t *testing.T) {
	ctx := context.Background()

	tests := []struct {
		name               string
		setupEnv           func(t *testing.T)
		jsonHook           func(any) ([]byte, error)
		clientHook         func(context.Context, string, ...option.ClientOption) (*firestore.Client, error)
		wantErrorSubstring string
	}{
		{
			name: "MissingEnv",
			setupEnv: func(t *testing.T) {
				t.Setenv("FIRESTORE_AUTH_ENCRYPTED", "")
			},
			wantErrorSubstring: "FIRESTORE_AUTH_ENCRYPTED must be set",
		},
		{
			name: "DecryptError",
			setupEnv: func(t *testing.T) {
				t.Setenv("FIRESTORE_AUTH_ENCRYPTED", "not base 64d")
			},
			wantErrorSubstring: "decrypting FIRESTORE_AUTH_ENCRYPTED",
		},
		{
			name: "JSONMarshalError",
			setupEnv: func(t *testing.T) {
				t.Setenv("FIRESTORE_AUTH_ENCRYPTED", magicEncodedKey)
			},
			jsonHook: func(v any) ([]byte, error) {
				return nil, errors.New("json-marshal-failed")
			},
			wantErrorSubstring: "json.Marshal GoogleSettings",
		},
		{
			name: "Emulator_NewClientError",
			setupEnv: func(t *testing.T) {
				t.Setenv("FIRESTORE_AUTH_ENCRYPTED", magicEncodedKey)
				t.Setenv("FIRESTORE_EMULATOR_HOST", "not valid emulator host")
			},
			clientHook: func(ctx context.Context, projectID string, opts ...option.ClientOption) (*firestore.Client, error) {
				return nil, errors.New("new-client-failed")
			},
			wantErrorSubstring: "new-client-failed",
		},
		{
			name: "Production_NewClientError",
			setupEnv: func(t *testing.T) {
				t.Setenv("FIRESTORE_AUTH_ENCRYPTED", magicEncodedKey)
				t.Setenv("FIRESTORE_EMULATOR_HOST", "")
			},
			clientHook: func(ctx context.Context, projectID string, opts ...option.ClientOption) (*firestore.Client, error) {
				return nil, errors.New("prod-client-failed")
			},
			wantErrorSubstring: "prod-client-failed",
		},
		{
			name: "Success",
			setupEnv: func(t *testing.T) {
				t.Setenv("FIRESTORE_AUTH_ENCRYPTED", magicEncodedKey)
				t.Setenv("FIRESTORE_EMULATOR_HOST", "") // production branch
			},
			// stub out real client creation entirely
			clientHook: func(ctx context.Context, projectID string, opts ...option.ClientOption) (*firestore.Client, error) {
				return &firestore.Client{}, nil
			},
		},
	}

	for _, tc := range tests {
		tc := tc // capture
		t.Run(tc.name, func(t *testing.T) {
			// no t.Parallel() because of t.Setenv
			tc.setupEnv(t)

			// install hooks
			origJSON := jsonMarshal
			origClient := firestoreNewClient
			if tc.jsonHook != nil {
				jsonMarshal = tc.jsonHook
			}
			if tc.clientHook != nil {
				firestoreNewClient = tc.clientHook
			}
			t.Cleanup(func() {
				jsonMarshal = origJSON
				firestoreNewClient = origClient
			})

			// invoke
			client, err := Firestore(ctx)
			if tc.wantErrorSubstring != "" {
				sub := assert.New(t)
				sub.Error(err)
				sub.Contains(err.Error(), tc.wantErrorSubstring)
			} else {
				sub := assert.New(t)
				sub.NoError(err)
				sub.Implements((*FirestoreClientInterface)(nil), client)
			}
		})
	}
}

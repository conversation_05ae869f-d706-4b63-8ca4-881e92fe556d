import { Component, EventEmitter, Input, Output, SimpleChanges } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { throwIfEmpty } from 'rxjs';

// TODO: Wire up backend once APIs are built
interface OEMData {
  orgIden: string;
  apiKey: string;
  name: string;
}
@Component({
  selector: 'app-edit-add',
  standalone: false,
  templateUrl: './edit-add.component.html',
  styleUrl: './edit-add.component.css'
})
export class EditAddComponent {
  @Input() isVisible = false;
  @Input() isEditMode = false;
  @Input() data: OEMData | null = null;
  @Output() close = new EventEmitter<void>();
  @Output() confirm = new EventEmitter<void>();
  form: FormGroup;

  constructor(private fb: FormBuilder) {
    this.form = this.fb.group({
      orgIden: ['', [
        Validators.required,
        Validators.pattern(/^[a-zA-Z0-9-]+$/),
        Validators.minLength(3),
        Validators.maxLength(80)
      ]],
      apiKey: [
        '',
        [
          Validators.pattern(/^[a-zA-Z0-9-_]+$/),
          Validators.maxLength(255)
        ]
      ],
      name: ['', [
        Validators.required,
        Validators.maxLength(255)
      ]]
    });
    if (this.isEditMode) {
      this.form.get('name')?.valueChanges.subscribe(value => {
        if (typeof value === 'string') {
          const trimmedValue = value.trim().replace(/\s+/g, ' ');
          if (value !== trimmedValue) {
            this.form.get('name')?.setValue(trimmedValue, { emitEvent: false });
            this.form.get('name')?.updateValueAndValidity();
          }
        }
      });
    }
  }
  ngOnChanges(changes: SimpleChanges): void {
    if (changes['isEditMode'] || changes['data']) {
      if (this.data && this.isEditMode) {
        const nameValue = (this.data.name || '').trim().replace(/\s+/g, ' ');
        this.form.reset({
          orgIden: this.data?.orgIden,
          apiKey: this.data?.apiKey,
          name: nameValue
        }, { emitEvent: false });
        this.form.get('name')?.markAsPristine();
        this.form.get('name')?.markAsUntouched();
        this.form.get('name')?.updateValueAndValidity();
        this.form.get('apiKey')?.disable();
      } else {
        this.form.reset({
          orgIden: '',
          apiKey: '',
          name: ''
        });
        this.form.reset();
        this.form.get('apiKey')?.enable();
      }
    }
  }
  private generateApiKey(): string {
    const randomPart = Math.random().toString(36).substring(2, 5);
    return `key-${randomPart}-${Math.floor(Math.random() * 1000)}`;
  }
  handleSave(): void {
    if (this.form.valid) {
      const apiKeyControl = this.form.get('apiKey');
      if (apiKeyControl && !apiKeyControl.value) {
        const newApiKey = this.generateApiKey();
        apiKeyControl.setValue(newApiKey, { emitEvent: false });
        apiKeyControl.updateValueAndValidity();
      }
      const formValue = this.form.value;
      const saveData = this.isEditMode ? { ...formValue, apiKey: this.data?.apiKey } : formValue;
      this.confirm.emit(saveData);
      this.form.reset();
    } else {
      Object.values(this.form.controls).forEach(control => {
        if (control.invalid) {
          control.markAsDirty();
          control.updateValueAndValidity({ onlySelf: true });
        }
      });
    }
  }

  handleCancel(): void {
    this.form.reset();
    this.close.emit();
  }
}

package middleware

import (
	"errors"
	"net/http"
	"strings"

	authorizer "synapse-its.com/shared/api/authorizer"
	response "synapse-its.com/shared/api/response"
)

// List only the endpoints being protected
// Only user endpoints at this time (no gateway or authenticate)
// TODO: make this a subrouter so we don't set endpoints here like this
var protectedPrefixes = []string{
	// Legacy endpoints, will eventually be removed once Flutter app is updated
	"/data/v2/device", "/data/v2/fault", "/user/v2/account/close", "/user/v2/account/notifications", "/user/v3/instruction", "/user/v2/profile",
	// Endpoints to be monitoring
	"/api/v3/data/device", "/api/v3/data/fault", "/api/v3/user/account/close", "/api/v3/user/account/notifications", "/api/v3/user/instruction", "/api/v3/user/profile",
}

func JWTAuthorizerMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		for _, pre := range protectedPrefixes {
			if strings.HasPrefix(r.URL.Path, pre) {
				ctx, err := authorizer.Authorize(r.Context(), r)
				// Will error if authentication fails, return immediately
				if err != nil {
					switch {
					case errors.Is(err, authorizer.ErrUnauthorized):
						response.CreateUnauthorizedResponse(w)
					case errors.Is(err, authorizer.ErrForbidden):
						response.CreateForbiddenResponse(w)
					default:
						response.CreateInternalErrorResponse(w)
					}
					return
				}
				r = r.WithContext(ctx)
			}
		}
		// pass the enriched context down
		next.ServeHTTP(w, r)
	})
}

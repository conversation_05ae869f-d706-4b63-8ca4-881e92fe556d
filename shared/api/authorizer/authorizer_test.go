package authorizer

import (
	"context"
	"database/sql"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"synapse-its.com/shared/api/jwttokens"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/mocks"
)

func TestAuthorize(t *testing.T) {
	// Save and restore globals
	origValidate := jwttokens.ValidateJJwtToken
	origGetConns := connect.GetConnections
	origGetUser := getUserFromToken
	origValidEP := validEndPointRequestFromRole
	defer func() {
		jwttokens.ValidateJJwtToken = origValidate
		connect.GetConnections = origGetConns
		getUserFromToken = origGetUser
		validEndPointRequestFromRole = origValidEP
	}()

	tests := []struct {
		name          string
		headerToken   string
		validateErr   error
		connsErr      error
		userID        int64
		roleID        int64
		expiresIn     time.Duration
		getUserErr    error
		allowEndpoint bool
		wantErr       error
	}{
		{
			name:        "missing header",
			headerToken: "",
			wantErr:     ErrUnauthorized,
		},
		{
			name:        "invalid JWT",
			headerToken: "bad",
			validateErr: errors.New("bad sig"),
			wantErr:     ErrUnauthorized,
		},
		{
			name:        "connection failure",
			headerToken: "tok",
			validateErr: nil,
			connsErr:    errors.New("no db"),
			wantErr:     ErrInternal,
		},
		{
			name:        "token not found",
			headerToken: "tok",
			validateErr: nil,
			getUserErr:  sql.ErrNoRows,
			wantErr:     ErrUnauthorized,
		},
		{
			name:        "generic database error",
			headerToken: "tok",
			validateErr: nil,
			getUserErr:  errors.New("database error"),
			wantErr:     ErrInternal,
		},
		{
			name:        "token expired",
			headerToken: "tok",
			validateErr: nil,
			userID:      1,
			roleID:      1,
			expiresIn:   -time.Hour,
			getUserErr:  nil,
			wantErr:     ErrForbidden,
		},
		{
			name:          "forbidden by role",
			headerToken:   "tok",
			validateErr:   nil,
			userID:        2,
			roleID:        2,
			expiresIn:     time.Hour,
			getUserErr:    nil,
			allowEndpoint: false,
			wantErr:       ErrForbidden,
		},
		{
			name:          "success",
			headerToken:   "tok",
			validateErr:   nil,
			userID:        3,
			roleID:        1,
			expiresIn:     time.Hour,
			getUserErr:    nil,
			allowEndpoint: true,
			wantErr:       nil,
		},
	}

	for _, tc := range tests {
		tc := tc // capture range variable
		t.Run(tc.name, func(t *testing.T) {
			// stub JWT validation
			jwttokens.ValidateJJwtToken = func(token string) (*jwttokens.JwtToken, *jwttokens.UserPermissions, error) {
				return nil, nil, tc.validateErr
			}

			// stub connections (match real signature)
			connect.GetConnections = func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
				if tc.connsErr != nil {
					return nil, tc.connsErr
				}
				conns := mocks.FakeConns()
				conns.Postgres = &mocks.FakeDBExecutor{
					QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
						if tc.getUserErr != nil {
							return tc.getUserErr
						}
						dbui := dest.(*dbUserInfo)
						dbui.UserID = tc.userID
						dbui.RoleID = tc.roleID
						dbui.ExpirationUTC = time.Now().UTC().Add(tc.expiresIn)
						return nil
					},
				}
				return conns, nil
			}

			// stub endpoint check
			validEndPointRequestFromRole = func(userInfo *dbUserInfo, r *http.Request) bool {
				return tc.allowEndpoint
			}

			// build request
			req := httptest.NewRequest("GET", "/data/v2/device", nil)
			if tc.headerToken != "" {
				req.Header.Set(headerJWT, tc.headerToken)
			}

			// invoke
			ctx, err := Authorize(context.Background(), req)

			// assert error
			if tc.wantErr != nil {
				if !errors.Is(err, tc.wantErr) {
					t.Fatalf("got err %v, want %v", err, tc.wantErr)
				}
				return
			}

			// success path
			if err != nil {
				t.Fatalf("unexpected error: %v", err)
			}
			ui, ok := UserInfoFromContext(ctx)
			if !ok {
				t.Fatal("UserInfo not in context")
			}
			if ui.ID != tc.userID {
				t.Errorf("got ID %d, want %d", ui.ID, tc.userID)
			}
			if ui.RoleID != tc.roleID {
				t.Errorf("got RoleID %d, want %d", ui.RoleID, tc.roleID)
			}
		})
	}
}

func TestValidEndPointRequestFromRole(t *testing.T) {
	tests := []struct {
		name     string
		userRole int64
		path     string
		want     bool
	}{
		{"admin access any", 1, "/foo/bar", true},
		{"integrator allowed v2", 2, "/data/v2/device/123", true},
		{"integrator allowed v3", 2, "/api/data/v3/device/abc", true},
		{"integrator denied other", 2, "/data/v2/fault", false},
		{"other role denied", 3, "/data/v2/device", false},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			dbui := &dbUserInfo{RoleID: tc.userRole}
			req := httptest.NewRequest("GET", tc.path, nil)
			got := validEndPointRequestFromRole(dbui, req)
			if got != tc.want {
				t.Errorf("validEndPointRequestFromRole(%d, %q) = %v; want %v", tc.userRole, tc.path, got, tc.want)
			}
		})
	}
}

package pubsubdata

import (
	"encoding/json"
	"fmt"
	"time"

	"cloud.google.com/go/pubsub"
)

func BuildAttributes(common CommonAttributes, headers HeaderDetails) map[string]string {
	headers.APIKey = "" // stub API key before sending to pubsub

	attrs := make(map[string]string)

	if common.Topic != "" {
		attrs["topic"] = common.Topic
	}
	if common.OrganizationIdentifier != "" {
		attrs["org_id"] = common.OrganizationIdentifier
	}
	if common.DeviceType != "" {
		attrs["device_type"] = common.DeviceType
	}
	if common.DLQReason != "" {
		attrs["dlq_reason"] = common.DLQReason
	}

	attrs["receive_timestamp"] = time.Now().UTC().Format(time.RFC3339)

	headerJSON, _ := json.Marshal(headers)
	if string(headerJSON) != "{}" {
		attrs["http_headers"] = string(headerJSON)
	}

	return attrs
}

func SetAttribute(msg *pubsub.Message, key, value string) {
	if msg.Attributes == nil {
		msg.Attributes = make(map[string]string)
	}
	msg.Attributes[key] = value
}

func ParseAttributes(attrs map[string]string) (CommonAttributes, HeaderDetails, error) {
	receiveTimestamp, err := time.Parse(time.RFC3339, attrs["receive_timestamp"])
	if err != nil {
		return CommonAttributes{}, HeaderDetails{}, fmt.Errorf("failed to parse receive_timestamp: %w", err)
	}
	common := CommonAttributes{
		Topic:                  attrs["topic"],
		OrganizationIdentifier: attrs["org_id"],
		DeviceType:             attrs["device_type"],
		DLQReason:              attrs["dlq_reason"],
		ReceiveTimestamp:       receiveTimestamp,
	}

	var headers HeaderDetails
	if raw, ok := attrs["http_headers"]; ok && raw != "" {
		if err := json.Unmarshal([]byte(raw), &headers); err != nil {
			return common, HeaderDetails{}, fmt.Errorf("failed to unmarshal http_headers: %w", err)
		}
	}
	return common, headers, nil
}

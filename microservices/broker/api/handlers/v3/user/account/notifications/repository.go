package notifications

import (
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/logger"
)

// repository is the interface that wraps the updateNotificationSmsEnabled method.
type Repository interface {
	UpdateNotificationSmsEnabled(userID int64, notificationSmsEnabled bool) error
}

// v3UserAccountNotificationsRepository is the implementation of the repository interface.
type v3UserAccountNotificationsRepository struct {
	db connect.DatabaseExecutor
}

func getV3UserAccountNotificationsRepository(conns *connect.Connections) Repository {
	return &v3UserAccountNotificationsRepository{
		db: conns.Postgres,
	}
}

func (r *v3UserAccountNotificationsRepository) UpdateNotificationSmsEnabled(userID int64, notificationSmsEnabled bool) error {
	query := "UPDATE {{User}} SET NotificationSmsEnabled = $1 WHERE Id = $2"
	result, err := r.db.Exec(query, notificationSmsEnabled, userID)
	if err != nil {
		return err
	}
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return err
	}
	logger.Debugf("UserId (%d) notifications preferences set, row(s) affected = (%d)", userID, rowsAffected)
	return nil
}

package ediecl2010

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/devices/edi/helper"
)

func Test_combineBytes(t *testing.T) {
	tests := []struct {
		name  string
		bytes []byte
		want  int
	}{
		{
			name:  "all zeros",
			bytes: []byte{0x00, 0x00, 0x00},
			want:  0,
		},
		{
			name:  "all ones",
			bytes: []byte{0x01, 0x01, 0x01},
			want:  0x010101,
		},
		{
			name:  "max values",
			bytes: []byte{0xFF, 0xFF, 0xFF},
			want:  0xFFFFFF,
		},
		{
			name:  "mixed values",
			bytes: []byte{0x12, 0x34, 0x56},
			want:  0x123456,
		},
		{
			name:  "high byte only",
			bytes: []byte{0xFF, 0x00, 0x00},
			want:  0xFF0000,
		},
		{
			name:  "middle byte only",
			bytes: []byte{0x00, 0xFF, 0x00},
			want:  0x00FF00,
		},
		{
			name:  "low byte only",
			bytes: []byte{0x00, 0x00, 0xFF},
			want:  0x0000FF,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := combineBytes(tt.bytes)
			assert.Equal(t, tt.want, got)
		})
	}
}

func Test_parseChannelStatus(t *testing.T) {
	tests := []struct {
		name        string
		status      int
		maxChannels int
		setBits     []int
		wantNil     bool
	}{
		{"all bits set", 0xFFFFFF, 24, func() (s []int) {
			for i := 0; i < 24; i++ {
				s = append(s, i)
			}
			return
		}(), false},
		{"no bits set", 0, 24, nil, false},
		{"mixed bits", 0x0F0F0F, 24, []int{0, 1, 2, 3, 8, 9, 10, 11, 16, 17, 18, 19}, false},
		{"maxChannels 0", 0xFF, 0, nil, true},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.wantNil {
				assert.Nil(t, parseChannelStatus(tt.status, tt.maxChannels))
				return
			}
			want := make([]bool, tt.maxChannels)
			for _, i := range tt.setBits {
				want[i] = true
			}
			got := parseChannelStatus(tt.status, tt.maxChannels)
			assert.Equal(t, want, got)
		})
	}
}

func Test_adjustVoltage(t *testing.T) {
	tests := []struct {
		name         string
		volt         int32
		headerDetail *helper.HeaderRecord
		want         int32
	}{
		{
			name: "normal voltage",
			volt: 50,
			headerDetail: &helper.HeaderRecord{
				Volt220: false,
				VoltDC:  false,
			},
			want: 50,
		},
		{
			name: "220V configuration",
			volt: 50,
			headerDetail: &helper.HeaderRecord{
				Volt220: true,
				VoltDC:  false,
			},
			want: 100,
		},
		{
			name: "DC voltage configuration",
			volt: 48,
			headerDetail: &helper.HeaderRecord{
				Volt220: false,
				VoltDC:  true,
			},
			want: 12,
		},
		{
			name: "220V and DC configuration",
			volt: 48,
			headerDetail: &helper.HeaderRecord{
				Volt220: true,
				VoltDC:  true,
			},
			want: 24,
		},
		{
			name: "zero voltage",
			volt: 0,
			headerDetail: &helper.HeaderRecord{
				Volt220: false,
				VoltDC:  false,
			},
			want: 0,
		},
		{
			name: "negative voltage",
			volt: -50,
			headerDetail: &helper.HeaderRecord{
				Volt220: false,
				VoltDC:  false,
			},
			want: -50,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := adjustVoltage(tt.volt, tt.headerDetail)
			assert.Equal(t, tt.want, got)
		})
	}
}

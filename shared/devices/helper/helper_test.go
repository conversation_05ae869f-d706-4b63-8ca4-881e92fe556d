package helper

import (
	"reflect"
	"testing"
)

func TestBoolTranslator_String(t *testing.T) {
	tests := []struct {
		name string
		tr   BoolTranslator
		in   bool
		want string
	}{
		{"TrueCase", BoolTranslator{"YES", "NO"}, true, "YES"},
		{"FalseCase", BoolTranslator{"YES", "NO"}, false, "NO"},
		{"EmptyStrings", BoolTranslator{"", ""}, true, ""},
	}
	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			got := tc.tr.String(tc.in)
			if got != tc.want {
				t.Errorf("String(%v) = %q; want %q", tc.in, got, tc.want)
			}
		})
	}
}

func TestTranslateConfig_Errors(t *testing.T) {
	// non-struct
	_, err := TranslateConfig(123, nil)
	if err == nil {
		t.<PERSON>("TranslateConfig(123): expected error, got nil")
	}
	// pointer to non-struct
	x := 123
	_, err = TranslateConfig(&x, nil)
	if err == nil {
		t.Fatal("TranslateConfig(&123): expected error, got nil")
	}
}

func TestTranslateConfig_Success(t *testing.T) {
	type config struct {
		Foo    bool
		Bar    bool
		SkipMe bool   // exported but no translator -> should be skipped
		Baz    string // non-bool -> skipped
		qux    bool   // unexported -> skipped
	}

	cfg := config{
		Foo:    true,
		Bar:    false,
		SkipMe: true,
		Baz:    "ignored",
		qux:    true,
	}

	translators := map[string]BoolTranslator{
		"Foo":   {"FooYes", "FooNo"},
		"Bar":   {"BarOn", "BarOff"},
		"Extra": {"X", "Y"}, // no matching field
	}

	want := map[string]string{
		"Foo": "FooYes",
		"Bar": "BarOff",
	}

	for _, ptr := range []bool{false, true} {
		var input interface{}
		testName := "Value"
		if ptr {
			input = &cfg
			testName = "Pointer"
		} else {
			input = cfg
		}

		t.Run("TranslateConfig_"+testName, func(t *testing.T) {
			got, err := TranslateConfig(input, translators)
			if err != nil {
				t.Fatalf("unexpected error: %v", err)
			}
			if !reflect.DeepEqual(got, want) {
				t.Errorf("TranslateConfig(%#v) = %#v; want %#v", input, got, want)
			}
			// SkipMe should not appear
			if _, exists := got["SkipMe"]; exists {
				t.Errorf("TranslateConfig included SkipMe; want it skipped")
			}
		})
	}
}

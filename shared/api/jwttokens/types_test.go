package jwttokens

import (
	"encoding/json"
	"reflect"
	"testing"
	"time"

	test "synapse-its.com/shared/util/test"
)

func TestJwtTokenJSON(t *testing.T) {
	token := JwtToken{
		IssuedBy:    "issuer",
		ExpiresAt:   time.Now().Add(1 * time.Hour).UTC(),
		IssuedAt:    time.Now().UTC(),
		JwtId:       "abc123",
		AuthId:      "testuser",
		Role:        "user",
		Permissions: "dummy",
	}

	data, err := json.Marshal(token)
	if err != nil {
		t.Fatalf("failed to marshal JwtToken: %v", err)
	}
	var token2 JwtToken
	if err := json.Unmarshal(data, &token2); err != nil {
		t.Fatalf("failed to unmarshal JwtToken: %v", err)
	}
	if token2.AuthId != token.AuthId || token2.Role != token.Role {
		t.<PERSON>rf("expected token %v, got %v", token, token2)
	}
}

func TestTypesJSONMarshalable_ByType(t *testing.T) {
	// Because we validate these types here to see if they json.Marshal() without
	// error, then when we call json.Marshal() in our code, we do not have to
	// check for an error, making our test coverage simpler to manage.
	test.AssertJSONTypeMarshalable(t, reflect.TypeOf(JwtToken{}))
	test.AssertJSONTypeMarshalable(t, reflect.TypeOf(UserDeviceAccess{}))
	test.AssertJSONTypeMarshalable(t, reflect.TypeOf(UserSoftwareGatewayAccess{}))
	test.AssertJSONTypeMarshalable(t, reflect.TypeOf(UserPermissions{}))
}

# pubsubdata

---

## Overview

Package `pubsubdata` defines mappings between subscription names and Pub/Sub topics, along with schema types for serializing and deserializing messages sent to and received from Pub/Sub.

---

## Package Layout

```
shared/
├── pubsubdata/
│   └── schemas.go
```

---

## Features

- **Subscription Mapping**: mapping of subscription names to topics via `PubsubSubscriptions`
- **Topics List**: list of available Pub/Sub topic names in `Topics`
- **PubsubBody Struct**: `PubsubBody` struct for message payloads
- **HeaderDetails Struct**: `HeaderDetails` struct for HTTP header metadata
- **PubsubMessageWrapper Struct**: `PubsubMessageWrapper` struct for Pub/Sub message attributes

---

## Usage

```go
import "synapse-its.com/shared/pubsubdata"

// Get the topic for a given subscription
topic := pubsubdata.PubsubSubscriptions["broker-subscription"]

// Build a message body
body := pubsubdata.PubsubBody{
    OrganizationIdentifier: "org123",
    Topic:                  topic,
    Headers: pubsubdata.HeaderDetails{
        Host:                  "example.com",
        UserAgent:             "Go-http-client/1.1",
        ContentLength:         "123",
        ContentType:           "application/json",
        GatewayDeviceID:       "device-001",
        GatewayMessageVersion: "1.0",
        MessageType:           "data",
        APIKey:                "apikey123",
        GatewayTimezone:       "US/Central",
    },
    Body: "payload data",
}
```

---

## API Reference

```go
var PubsubSubscriptions map[string]string
var Topics []string

type PubsubBody struct {
    OrganizationIdentifier string `json:"organizationIdentifier"`
    Topic                  string `json:"topic"`
    Headers                HeaderDetails `json:"headers"`
    Body                   string `json:"body"`
}

type HeaderDetails struct {
    Host                  string `json:"host"`
    UserAgent             string `json:"userAgent"`
    ContentLength         string `json:"contentLength"`
    ContentType           string `json:"contentType"`
    GatewayDeviceID       string `json:"gatewayDeviceID"`
    GatewayMessageVersion string `json:"gatewayMessageVersion"`
    MessageType           string `json:"messageType"`
    APIKey                string `json:"apiKey"`
    GatewayTimezone       string `json:"gatewayTimezone"`
}

type PubsubMessageWrapper struct {
    ID              string
    Data            string
    Attributes      map[string]string
    OrderingKey     string
    PublishTime     string
    DeliveryAttempt *int
}
```

---

## Internals

The package initializes two core data structures:
- `PubsubSubscriptions`: built-in map linking subscription identifiers to topics.
- `Topics`: automatically extracted slice of topic names for easy lookup.

All structs include JSON tags to ensure correct serialization when publishing or consuming messages.

---

## Example

```
# Run package tests
go test ./pubsubdata
```

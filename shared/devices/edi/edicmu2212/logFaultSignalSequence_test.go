package edicmu2212

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/pubsubdata"
)

func Test_LogFaultSignalSequence(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name          string
		httpHeader    *pubsubdata.HeaderDetails
		byteMsg       []byte
		header        *helper.HeaderRecord
		expectedError error
		expectedType  string
		checkResult   func(t *testing.T, result *helper.FaultSignalSequenceRecords)
	}{
		{
			name:          "nil header",
			httpHeader:    &pubsubdata.HeaderDetails{},
			byteMsg:       []byte{},
			header:        nil,
			expectedError: helper.ErrMsgHeaderRecordNil,
		},
		{
			name:          "invalid checksum",
			httpHeader:    &pubsubdata.HeaderDetails{},
			byteMsg:       []byte{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}, // Invalid checksum
			header:        &helper.HeaderRecord{},
			expectedError: helper.ErrMsgByteChecksum,
		},
		{
			name:       "invalid byte length",
			httpHeader: &pubsubdata.HeaderDetails{},
			// Create a message with invalid length (too short)
			byteMsg: func() []byte {
				msg := []byte{1, 2, 3, 4, 5, 6, 7, 8, 1, 0}
				msg[len(msg)-1] = calculateFaultSignalChecksum(msg[:len(msg)-1])
				return msg
			}(),
			header:        &helper.HeaderRecord{},
			expectedError: helper.ErrMsgByteLen,
		},
		{
			name:         "valid message with 24Vdc fault",
			httpHeader:   &pubsubdata.HeaderDetails{},
			byteMsg:      createValidMessage(1, 1), // Fault type 1 (24Vdc Fault)
			header:       &helper.HeaderRecord{MaxChannels: 16},
			expectedType: "24Vdc Fault",
			checkResult: func(t *testing.T, result *helper.FaultSignalSequenceRecords) {
				assert.NotNil(t, result)
				assert.Equal(t, "24Vdc Fault", result.FaultType)
				assert.Equal(t, DeviceModel, result.DeviceModel)
				assert.Len(t, result.Records, 1)
				if len(result.Records) > 0 {
					record := result.Records[0]
					assert.Equal(t, int64(0), record.Timestamp)
					assert.NotNil(t, record.Reds)
					assert.NotNil(t, record.Yellows)
					assert.NotNil(t, record.Greens)
					assert.NotNil(t, record.Walks)
					assert.False(t, record.EE_SF_RE)
					assert.Equal(t, 120, record.AcVoltage)
				}
			},
		},
		{
			name:         "timestamp exceeds max value",
			httpHeader:   &pubsubdata.HeaderDetails{},
			byteMsg:      createMessageWithMaxTimestamp(1),
			header:       &helper.HeaderRecord{MaxChannels: 16},
			expectedType: "24Vdc Fault",
			checkResult: func(t *testing.T, result *helper.FaultSignalSequenceRecords) {
				assert.NotNil(t, result)
				assert.Len(t, result.Records, 1)
				if len(result.Records) > 0 {
					assert.Equal(t, int64(0), result.Records[0].Timestamp)
				}
			},
		},
		{
			name:         "multiple fault types",
			httpHeader:   &pubsubdata.HeaderDetails{},
			byteMsg:      createValidMessage(10, 1), // Fault type 10 (Lack of Signal Fault)
			header:       &helper.HeaderRecord{MaxChannels: 16},
			expectedType: "Lack of Signal Fault",
			checkResult: func(t *testing.T, result *helper.FaultSignalSequenceRecords) {
				assert.NotNil(t, result)
				assert.Equal(t, "Lack of Signal Fault", result.FaultType)
			},
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			device := EDICMU2212{}
			result, err := device.LogFaultSignalSequence(tt.httpHeader, tt.byteMsg, tt.header)

			if tt.expectedError != nil {
				assert.Error(t, err)
				assert.ErrorIs(t, err, tt.expectedError)
				assert.Nil(t, result)
				return
			}

			assert.NoError(t, err)
			assert.NotNil(t, result)
			assert.Equal(t, tt.expectedType, result.FaultType)

			if tt.checkResult != nil {
				tt.checkResult(t, result)
			}
		})
	}
}

// Helper functions to create test messages

func calculateFaultSignalChecksum(data []byte) byte {
	var sum uint8
	for _, b := range data {
		sum += b
	}
	return ^sum // 1's complement
}

func createValidMessage(faultType byte, numRecords byte) []byte {
	// Create a valid message with proper checksum
	msgLen := HeaderLength + LogNumOffset + int(numRecords)*TraceLength + HeaderOffset
	msg := make([]byte, msgLen)

	// Set header bytes
	msg[0] = 0x01 // Message type
	msg[1] = 0x02 // Device address
	msg[2] = 0x03 // Command code
	msg[3] = 0x00 // Response status

	// Set event count and fault type
	msg[HeaderLength] = 1 // Event count
	msg[HeaderLength+FaultTypeOffset] = faultType
	msg[HeaderLength+LogNumOffset] = numRecords

	// Create a trace record
	if numRecords > 0 {
		recordStart := HeaderLength + LogNumOffset + 1
		// Set timestamp (1000)
		msg[recordStart] = 0x03
		msg[recordStart+1] = 0xE8

		// Set signal statuses (all zeros for simplicity)
		for i := 0; i < 4; i++ {
			msg[recordStart+RedCMU2Offset+i] = 0
			msg[recordStart+YellowCMU2Offset+i] = 0
			msg[recordStart+GreenCMU2Offset+i] = 0
			msg[recordStart+CHiCMU2Offset+i] = 0
		}

		// Set cabinet status and AC voltage
		msg[recordStart+CabCMU2Offset] = 0
		msg[recordStart+ACCMU2Offset] = 120
	}

	// Calculate and set checksum
	checksum := calculateFaultSignalChecksum(msg[:len(msg)-1])
	msg[len(msg)-1] = checksum

	return msg
}

func createMessageWithMaxTimestamp(faultType byte) []byte {
	msg := createValidMessage(faultType, 1)
	// Set timestamp to exceed max value
	msg[HeaderLength+LogNumOffset+1] = 0xFF
	msg[HeaderLength+LogNumOffset+2] = 0xFF
	// Recalculate checksum
	checksum := calculateFaultSignalChecksum(msg[:len(msg)-1])
	msg[len(msg)-1] = checksum
	return msg
}

// Test getFaultType function
func Test_getFaultType(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name      string
		faultType byte
		expected  string
	}{
		{"24Vdc Fault", 1, "24Vdc Fault"},
		{"12Vdc Fault", 2, "12Vdc Fault"},
		{"Conflict Fault", 3, "Conflict Fault"},
		{"Serial Bus #1 Error", 4, "Serial Bus #1 Error"},
		{"Serial Bus #3 Error", 5, "Serial Bus #3 Error"},
		{"CU Frame-62 Latched Flash (LFSA)", 6, "CU Frame-62 Latched Flash (LFSA)"},
		{"CU Frame-62 Non-Latched Flash (NFSA)", 7, "CU Frame-62 Non-Latched Flash (NFSA)"},
		{"Diagnostic Fault", 8, "Diagnostic Fault"},
		{"Multiple Indication Fault", 9, "Multiple Indication Fault"},
		{"Lack of Signal Fault", 10, "Lack of Signal Fault"},
		{"Clearance (Short Yellow) Fault", 11, "Clearance (Short Yellow) Fault"},
		{"Clearance (Skipped Yellow) Fault", 12, "Clearance (Skipped Yellow) Fault"},
		{"Yellow + Red Clearance Fault", 13, "Yellow + Red Clearance Fault"},
		{"Field Output Check Fault", 14, "Field Output Check Fault"},
		{"Data Key Absent", 15, "Data Key Absent"},
		{"Data Key FCS Error", 16, "Data Key FCS Error"},
		{"Data Key Invalid Parameter Error", 17, "Data Key Invalid Parameter Error"},
		{"Local Flash", 18, "Local Flash"},
		{"Circuit Breaker Trip", 19, "Circuit Breaker Trip"},
		{"AC Line Low Voltage", 20, "AC Line Low Voltage"},
		{"HDSP Diagnostic Error", 22, "HDSP Diagnostic Error"},
		{"FYA Flash Rate Fault", 23, "FYA Flash Rate Fault"},
		{"48Vdc Fault", 24, "48Vdc Fault"},
		{"Recurrent Pulse Conflict Fault", 129, "Recurrent Pulse Conflict Fault"},
		{"Recurrent Pulse Lack of Signal Fault", 130, "Recurrent Pulse Lack of Signal Fault"},
		{"Recurrent Pulse Multiple Indication Fault", 131, "Recurrent Pulse Multiple Indication Fault"},
		{"Configuration Change Fault", 132, "Configuration Change Fault"},
		{"48Vdc Fault (alt code)", 133, "48Vdc Fault"},
		{"CU Watchdog Fail", 134, "CU Watchdog Fail"},
		{"Unknown Fault (21)", 21, ""},
		{"Unknown Fault (25)", 25, ""},
		{"Unknown Fault (128)", 128, ""},
		{"Unknown Fault (135)", 135, ""},
		{"Unknown Fault (255)", 255, ""},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			result := getFaultType(tt.faultType)
			assert.Equal(t, tt.expected, result)
		})
	}
}

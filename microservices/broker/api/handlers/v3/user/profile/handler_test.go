package profile

import (
	"context"
	"database/sql"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"synapse-its.com/shared/api/authorizer"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/mocks/dbexecutor"
)

func Test_HandlerWithDeps(t *testing.T) {
	t.<PERSON>llel()

	tests := []struct {
		name           string
		method         string
		setupMocks     func(*dbexecutor.FakeDBExecutor)
		expectedStatus int
		expectedBody   map[string]interface{}
		wantErr        bool
	}{
		{
			name:   "successful_profile_retrieval",
			method: http.MethodGet,
			setupMocks: func(mdb *dbexecutor.FakeDBExecutor) {
				// Setup database mocks for profile
				mdb.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					d, ok := dest.(*UserProfileRecord)
					if !ok {
						return errors.New("dest must be of type *UserProfileRecord")
					}
					*d = UserProfileRecord{
						UserID:                 1,
						Username:               "testuser",
						Firstname:              "Test",
						Lastname:               "User",
						Email:                  "<EMAIL>",
						Mobile:                 "1234567890",
						NotificationSmsEnabled: true,
						LastLogin:              time.Date(2024, 3, 20, 10, 0, 0, 0, time.UTC),
						Description:            "Test user",
					}

					return nil
				}
			},
			expectedStatus: http.StatusOK,
			expectedBody: map[string]interface{}{
				"status": "success",
				"data": map[string]interface{}{
					"user_profile": map[string]interface{}{
						"user_id":                  float64(1),
						"username":                 "testuser",
						"first_name":               "Test",
						"last_name":                "User",
						"email":                    "<EMAIL>",
						"mobile":                   "1234567890",
						"notification_sms_enabled": true,
						"last_login":               "2024-03-20T10:00:00Z",
						"description":              "Test user",
					},
					"app_version": map[string]interface{}{
						"EDIFieldServiceApp": map[string]interface{}{
							"dev": map[string]interface{}{
								"android": "2",
								"ios":     "2",
								"windows": "1",
							},
							"notpublished": map[string]interface{}{
								"android": "2",
								"ios":     "8",
								"windows": "1",
							},
							"published": map[string]interface{}{
								"android": "1",
								"ios":     "1",
								"windows": "1",
							},
						},
					},
				},
			},
			wantErr: false,
		},
		{
			name:   "profile_not_found",
			method: http.MethodGet,
			setupMocks: func(mdb *dbexecutor.FakeDBExecutor) {
				mdb.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					return ErrProfileNotFound
				}
			},
			expectedStatus: http.StatusInternalServerError,
			expectedBody: map[string]interface{}{
				"status":  "error",
				"data":    nil,
				"message": "Internal server error",
				"code":    float64(http.StatusInternalServerError),
			},
			wantErr: true,
		},
		{
			name:   "user_info_not_found",
			method: http.MethodGet,
			setupMocks: func(mdb *dbexecutor.FakeDBExecutor) {
				// No setup needed as we'll mock UserInfoFromContext to return false
			},
			expectedStatus: http.StatusInternalServerError,
			expectedBody: map[string]interface{}{
				"status":  "error",
				"data":    nil,
				"message": "Internal server error",
				"code":    float64(http.StatusInternalServerError),
			},
			wantErr: true,
		},
		{
			name:   "connection_error",
			method: http.MethodGet,
			setupMocks: func(mdb *dbexecutor.FakeDBExecutor) {
				// No setup needed as we'll mock GetConnections to return error
			},
			expectedStatus: http.StatusInternalServerError,
			expectedBody: map[string]interface{}{
				"status":  "error",
				"data":    nil,
				"message": "Internal server error",
				"code":    float64(http.StatusInternalServerError),
			},
			wantErr: true,
		},
		{
			name:   "profile_error",
			method: http.MethodGet,
			setupMocks: func(mdb *dbexecutor.FakeDBExecutor) {
				mdb.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					return assert.AnError
				}
			},
			expectedStatus: http.StatusInternalServerError,
			expectedBody: map[string]interface{}{
				"status":  "error",
				"data":    nil,
				"message": "Internal server error",
				"code":    float64(http.StatusInternalServerError),
			},
			wantErr: true,
		},
		{
			name:   "app_version_error",
			method: http.MethodGet,
			setupMocks: func(mdb *dbexecutor.FakeDBExecutor) {
				mdb.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					d, ok := dest.(*UserProfileRecord)
					if !ok {
						return errors.New("dest must be of type *UserProfileRecord")
					}
					*d = UserProfileRecord{
						UserID:                 1,
						Username:               "testuser",
						Firstname:              "Test",
						Lastname:               "User",
						Email:                  "<EMAIL>",
						Mobile:                 "1234567890",
						NotificationSmsEnabled: true,
						LastLogin:              time.Date(2024, 3, 20, 10, 0, 0, 0, time.UTC),
						Description:            "Test user",
					}
					return nil
				}
			},
			expectedStatus: http.StatusInternalServerError,
			expectedBody: map[string]interface{}{
				"status":  "error",
				"data":    nil,
				"message": "Internal server error",
				"code":    float64(http.StatusInternalServerError),
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create mock instances
			mockDB := new(dbexecutor.FakeDBExecutor)

			// Setup mocks
			tt.setupMocks(mockDB)

			// Create handler with mocked dependencies
			handler := HandlerWithDeps(HandlerDeps{
				UserInfoFromContext: func(ctx context.Context) (*authorizer.UserInfo, bool) {
					if tt.name == "user_info_not_found" {
						return nil, false
					}
					return &authorizer.UserInfo{ID: 1}, true
				},
				GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					if tt.name == "connection_error" {
						return nil, assert.AnError
					}
					return &connect.Connections{Postgres: mockDB}, nil
				},
				GetUserProfile: getUserProfile,
				GetAppVersion: func(pg connect.DatabaseExecutor) (*AppVersions, error) {
					if tt.name == "app_version_error" {
						return nil, assert.AnError
					}
					return getAppVersion(pg)
				},
			})

			// Create test request
			req := httptest.NewRequest(tt.method, "/profile", nil)
			w := httptest.NewRecorder()

			// Execute request
			handler(w, req)

			// Assert response
			assert.Equal(t, tt.expectedStatus, w.Code)
		})
	}
}

func Test_getUserProfile(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name          string
		userID        int64
		setupMocks    func(*dbexecutor.FakeDBExecutor)
		expectedError error
	}{
		{
			name:   "success",
			userID: 1,
			setupMocks: func(mdb *dbexecutor.FakeDBExecutor) {
				mdb.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					d, ok := dest.(*UserProfileRecord)
					if !ok {
						return errors.New("dest must be of type *UserProfileRecord")
					}
					*d = UserProfileRecord{
						UserID:                 1,
						Username:               "testuser",
						Firstname:              "Test",
						Lastname:               "User",
						Email:                  "<EMAIL>",
						Mobile:                 "1234567890",
						NotificationSmsEnabled: true,
						LastLogin:              time.Date(2024, 3, 20, 10, 0, 0, 0, time.UTC),
						Description:            "Test user",
					}
					return nil
				}
			},
			expectedError: nil,
		},
		{
			name:   "no_rows",
			userID: 2,
			setupMocks: func(mdb *dbexecutor.FakeDBExecutor) {
				mdb.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					return sql.ErrNoRows
				}
			},
			expectedError: ErrProfileNotFound,
		},
		{
			name:   "not_found",
			userID: 2,
			setupMocks: func(mdb *dbexecutor.FakeDBExecutor) {
				mdb.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					return ErrProfileNotFound
				}
			},
			expectedError: ErrProfileNotFound,
		},
		{
			name:   "generic_error",
			userID: 3,
			setupMocks: func(mdb *dbexecutor.FakeDBExecutor) {
				mdb.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					return assert.AnError
				}
			},
			expectedError: assert.AnError,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			mockDB := new(dbexecutor.FakeDBExecutor)
			tt.setupMocks(mockDB)

			profile, err := getUserProfile(mockDB, tt.userID)
			if tt.expectedError != nil {
				assert.ErrorIs(t, err, tt.expectedError)
				assert.Nil(t, profile)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, profile)
				assert.Equal(t, tt.userID, profile.UserID)
			}
		})
	}
}

func Test_getAppVersion(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name          string
		setupMocks    func(*dbexecutor.FakeDBExecutor)
		expectedError error
	}{
		{
			name: "success",
			setupMocks: func(mdb *dbexecutor.FakeDBExecutor) {
				// No setup needed as we're using hardcoded values
			},
			expectedError: nil,
		},
	}

	for _, tt := range tests {
		tt := tt // capture range variable
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			mockDB := new(dbexecutor.FakeDBExecutor)
			tt.setupMocks(mockDB)

			version, err := getAppVersion(mockDB)
			if tt.expectedError != nil {
				assert.ErrorIs(t, err, tt.expectedError)
				assert.Nil(t, version)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, version)
				assert.Equal(t, "2", version.EDIFieldServiceApp.Dev.Android)
				assert.Equal(t, "2", version.EDIFieldServiceApp.Dev.IOS)
				assert.Equal(t, "1", version.EDIFieldServiceApp.Dev.Windows)
				assert.Equal(t, "2", version.EDIFieldServiceApp.NotPublished.Android)
				assert.Equal(t, "8", version.EDIFieldServiceApp.NotPublished.IOS)
				assert.Equal(t, "1", version.EDIFieldServiceApp.NotPublished.Windows)
				assert.Equal(t, "1", version.EDIFieldServiceApp.Published.Android)
				assert.Equal(t, "1", version.EDIFieldServiceApp.Published.IOS)
				assert.Equal(t, "1", version.EDIFieldServiceApp.Published.Windows)
			}
		})
	}
}

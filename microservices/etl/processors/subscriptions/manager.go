package subscriptions

import (
	"context"
	"sync"
	"sync/atomic"
	"time"

	"synapse-its.com/shared/logger"
)

// HandlerFunc is the signature for each subscription handler.
type HandlerFunc func(context.Context, string)

// Subscription describes one Pub/Sub consumer: its name, its handler.
type Subscription struct {
	Name    string
	Handler HandlerFunc
}

// BackoffConfig holds defaults for how failed handlers are retried.
type BackoffConfig struct {
	Initial    time.Duration
	Max        time.Duration
	MaxRetries int
}

// Manager runs multiple SubscriptionWorkers, tracks their health, and allows clean shutdown.
type Manager struct {
	subs        []Subscription
	backoff     BackoffConfig
	healthState sync.Map
	workWG      sync.WaitGroup
	workers     []SubscriptionWorker
}

// SubscriptionWorker is one goroutine + “done” channel.
// (It’s exported because callers might want to log which ones are still running.)
type SubscriptionWorker struct {
	Subscription Subscription
	Done         chan struct{}
}

// NewManager constructs a Manager with a list of subscriptions and default backoff settings.
func NewManager(subs []Subscription, bc BackoffConfig) *Manager {
	return &Manager{
		subs:    subs,
		backoff: bc,
	}
}

// Start all each subscription worker
func (m *Manager) Start(ctx context.Context) {
	m.workers = make([]SubscriptionWorker, len(m.subs))

	for i, sub := range m.subs {
		// Mark each subscription as “unhealthy” until it actually begins
		m.MarkUnhealthy(sub.Name)

		w := SubscriptionWorker{
			Subscription: sub,
			Done:         make(chan struct{}),
		}
		m.workers[i] = w
		m.workWG.Add(1)

		go m.runWorker(ctx, w)
	}
}

func (m *Manager) runWorker(ctx context.Context, w SubscriptionWorker) {
	defer m.workWG.Done()
	defer close(w.Done)

	initial := m.backoff.Initial
	maxDelay := m.backoff.Max
	maxRetries := m.backoff.MaxRetries

	retries := 0
	for {
		if ctx.Err() != nil {
			logger.Infof("Subscription %s: context cancelled, exiting", w.Subscription.Name)
			return
		}

		logger.Infof("Subscription %s: starting handler", w.Subscription.Name)
		m.MarkHealthy(w.Subscription.Name)
		w.Subscription.Handler(ctx, w.Subscription.Name)

		if ctx.Err() != nil {
			logger.Infof("Subscription %s: shut down cleanly", w.Subscription.Name)
			return
		}

		// Exited unexpectedly—retry with exponential backoff
		retries++
		delay := initial * (1 << retries)
		if delay > maxDelay {
			delay = maxDelay
		}

		logger.Warnf("Subscription %s exited, retrying in %v (retry #%d)", w.Subscription.Name, delay, retries)
		m.MarkUnhealthy(w.Subscription.Name)

		select {
		case <-ctx.Done():
			return
		case <-time.After(delay):
			// Continue retry loop
		}

		if maxRetries > 0 && retries >= maxRetries {
			logger.Errorf("Subscription %s exceeded max retries (%d), stopping", w.Subscription.Name, retries)
			return
		}
	}
}

// Wait blocks until either all workers finish, or the timeout elapses.
// It returns a slice of subscription names that did not finish before timeout.
func (m *Manager) Wait(timeout time.Duration) []string {
	done := make(chan struct{})
	go func() {
		m.workWG.Wait()
		close(done)
	}()

	select {
	case <-done:
		logger.Info("All subscription workers exited cleanly.")
		return nil
	case <-time.After(timeout):
		var stillRunning []string
		logger.Warn("Timeout reached waiting for subscription workers; still running:")
		for _, w := range m.workers {
			select {
			case <-w.Done:
				_ = true // no-op for testing coverage
			default:
				logger.Warnf(" - %s", w.Subscription.Name)
				stillRunning = append(stillRunning, w.Subscription.Name)
			}
		}
		return stillRunning
	}
}

// Marks unhealthy status of a single subscription
func (m *Manager) MarkUnhealthy(sub string) {
	val := new(int32)
	atomic.StoreInt32(val, 1)
	m.healthState.Store(sub, val)
}

// Marks healthy status of a single subscription
func (m *Manager) MarkHealthy(sub string) {
	val := new(int32)
	atomic.StoreInt32(val, 0)
	m.healthState.Store(sub, val)
}

// Reports if any subscription is currently marked unhealthy
func (m *Manager) IsAnyUnhealthy() bool {
	var unhealthy bool
	m.healthState.Range(func(_, v any) bool {
		if atomic.LoadInt32(v.(*int32)) == 1 {
			unhealthy = true
			return false
		}
		return true
	})
	return unhealthy
}

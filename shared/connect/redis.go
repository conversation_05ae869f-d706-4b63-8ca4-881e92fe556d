package connect

import (
	"context"
	"fmt"
	"os"
	"time"

	// https://pkg.go.dev/github.com/go-redis/redis/v9
	"github.com/redis/go-redis/v9"

	"synapse-its.com/shared/logger"
)

var (
	redisNewClient = redis.NewClient
	timeSleepRedis = time.Sleep
	timeAfter      = time.After
)

// Establish a connection using information passed in via environmental variables.
var Redis = func(ctx context.Context) (*redis.Client, error) {
	// Read environment variables for Redis with defaults.
	redisHost := os.Getenv("MEMORYSTORE_HOST")
	if redisHost == "" {
		redisHost = "redis"
	}
	redisPort := os.Getenv("MEMORYSTORE_PORT")
	if redisPort == "" {
		redisPort = "6379"
	}
	redisAddr := fmt.Sprintf("%s:%s", redisHost, redisPort)
	logger.Debugf("Connecting to Redis at %s", redisAddr)

	// Create the Redis client.
	redisClient := redisNewClient(&redis.Options{
		Addr: redisAddr,
	})

	// Exponential backoff retry loop to wait for Redis to be ready.
	var pong string
	var err error
	backoff := time.Second
	maxRetries := 10

	for i := 0; i < maxRetries; i++ {
		pong, err = redisClient.Ping(ctx).Result()
		if err == nil {
			break
		}
		logger.Debugf("Attempt %d/%d: Redis not ready, retrying in %s: %v", i+1, maxRetries, backoff, err)
		timeSleepRedis(backoff)
		backoff *= 2
	}

	if err != nil {
		return nil, fmt.Errorf("could not connect to Redis after %d attempts: %v", maxRetries, err)
	}
	logger.Debugf("Redis connected: %s", pong)

	return redisClient, nil
}

// Set the value related to the supplied key.
var RedisSet = func(ctx context.Context, redisClient *redis.Client, key string, val string) error {
	err := redisClient.Set(ctx, key, val, 0).Err()
	if err != nil {
		return fmt.Errorf("failed to set key: %v", err)
	}
	return nil
}

// Get the value related to the supplied key.
var RedisGet = func(ctx context.Context, redisClient *redis.Client, key string) (string, error) {
	val, err := redisClient.Get(ctx, key).Result()
	if err != nil {
		return "", fmt.Errorf("failed to get key: %v", err)
	}
	return val, nil
}

// RedisAwait repeatedly checks for the key/value pair specified. If the pair does not match or does not exist,
// it retries using exponential backoff starting at 500 ms and capping at 60 seconds.
var RedisAwait = func(ctx context.Context, redisClient *redis.Client, key string, expectedValue string) error {
	backoff := 500 * time.Millisecond
	maxBackoff := 60 * time.Second

	for {
		// Check if the context has been cancelled.
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}

		// Try to get the value for the specified key.
		val, err := RedisGet(ctx, redisClient, key)
		if err != nil {
			// Log the error and proceed to retry.
			logger.Debugf("Error retrieving key %q: %v", key, err)
		} else {
			// If the value matches the expected value, return success.
			if val == expectedValue {
				return nil
			}
			logger.Debugf("Key %q has value %q; expecting %q", key, val, expectedValue)
		}

		// Wait for the backoff period before trying again.
		select {
		case <-timeAfter(backoff):
			// Increase backoff for the next iteration.
			backoff *= 2
			if backoff > maxBackoff {
				backoff = maxBackoff
			}
		case <-ctx.Done():
			return ctx.Err()
		}
	}
}

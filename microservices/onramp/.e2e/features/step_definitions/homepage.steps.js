const { Given, Then } = require('@cucumber/cucumber');
const { By, until } = require('selenium-webdriver');

Given('I open the home page', async function () {
  try {
    await this.driver.get(process.env.BASE_URL);
    await this.waitForAngularStable();
  }
  catch (err) {
    console.error(err);
    console.error('page source at error:\n', await this.driver.getPageSource());
    throw err;
  }
});

Then('I should see the title {string}', async function (expectedTitle) {
  try {
    const actual = await this.driver.getTitle();
    if (actual !== expectedTitle) {
      throw new Error(`Expected title "${expectedTitle}" but got "${actual}"`);
    }
  }
  catch (err) {
    console.error(err);
    console.error('page source at error:\n', await this.driver.getPageSource());
    throw err;
  }
});

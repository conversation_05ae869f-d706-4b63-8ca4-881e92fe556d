// Package authenticate encapsulates the logic dealing with access requests via a web integration
package authenticate

import (
	"database/sql"
	"encoding/hex"
	"errors"
	"net/http"
	"time"

	response "synapse-its.com/shared/api/response"
	logger "synapse-its.com/shared/logger"
)

// WebAccess takes a username and password, and if valid, returns a token in the body of the response
func (h *UserHandler) WebAccess(userName, password string, w http.ResponseWriter, r *http.Request) {
	// Create hash of the password - all passwords are stored hashed in the db
	hashedPassword := h.PasswordHasher(password)

	// Get the postgres connection
	connections, err := h.DBProvider(r.Context())
	if err != nil {
		response.CreateInternalErrorResponse(w)
		logger.Errorf("%v", err) // Database connection errors are ERROR level
		return
	}

	pg := connections.Postgres

	// Lookup the username in the db
	query := `
		SELECT
			u.Id,
			u.RoleId,
			u.WebEnabled,
			u.WebTokenDurationSeconds
		FROM {{User}} u
		WHERE u.UserName = $1 AND u.Password = $2 AND u.IsEnabled = 1`

	// Use QueryRowStruct with the dbUser struct
	user := dbUser{}
	err = pg.QueryRowStruct(&user, query, userName, hashedPassword)

	switch {
	case errors.Is(err, sql.ErrNoRows):
		// Username and password combo not found -- update the failed attempts for the user name and return not-authorized
		// Could be the username is bogus -- that's fine - no records will be updated
		query = `
		UPDATE
			{{User}}
		SET FailedLoginAttempts = FailedLoginAttempts + 1,
			IsEnabled =
				CASE
					WHEN FailedLoginAttempts >= 3 THEN 0
					ELSE IsEnabled
				END
		WHERE UserName = $1`
		_, err = pg.Exec(query, userName)

		logger.Warnf("%v: username: %s, err: %v", ErrInvalidCredentials, userName, err) // Authentication failures are WARN level
		response.CreateUnauthorizedResponse(w)
		return
	case err != nil:
		logger.Errorf("%v: %v", ErrDatabaseQuery, err) // Database query errors are ERROR level
		response.CreateUnauthorizedResponse(w)
		return
	default:
		// Use the struct fields directly instead of GetColumn calls
		userID := user.Id
		roleID := user.RoleId
		webEnabled := user.WebEnabled
		webTokenDurationSeconds := user.WebTokenDurationSeconds

		if _, err = h.ValidateRoleFunc(roleID); err != nil {
			logger.Errorf("%v: %v", ErrRoleValidation, err) // Role validation errors are ERROR level
			response.CreateUnauthorizedResponse(w)
			return
		}

		// Reset failed login attempts and update last login time
		query = "UPDATE {{User}} SET FailedLoginAttempts = 0, LastLoginUTC = $1 WHERE UserName = $2"
		_, err = pg.Exec(query, h.TimeProvider().UTC(), userName)
		if err != nil {
			logger.Warnf("%v: user %s, err: %v", ErrLoginTimeUpdate, userName, err) // Non-critical errors are WARN level
			// Continue despite this error - it's not critical
		}

		// EMA-320
		if webEnabled != 1 { // the request was for web access; however, this user is not setup for web access.
			logger.Warnf("%v: user ID %d", ErrWebAccessNotEnabled, userID) // Configuration issue is a WARN level
			response.CreateUnauthorizedResponse(w)
			return
		}

		// Generate nonce
		theNonce, err := h.NonceGenerator(NonceByteLength)
		if err != nil {
			logger.Errorf("%v: user ID %d, err: %v", ErrNonceGeneration, userID, err) // Nonce generation errors are ERROR level
			response.CreateUnauthorizedResponse(w)
			return
		}
		encodedNonce := hex.EncodeToString(theNonce)

		// Create token
		duration := time.Duration(webTokenDurationSeconds) * time.Second
		expiresAt := h.TimeProvider().Add(duration).UTC()

		// Persist token to database
		err = h.TokenPersister(pg, userID, encodedNonce, expiresAt)
		if err != nil {
			logger.Errorf("%v: %v", ErrTokenPersistence, err) // Token persistence errors are ERROR level
			response.CreateUnauthorizedResponse(w)
			return
		}

		// Create and send response
		tokenMsg := &dataTokenPayload{}
		tokenMsg.Token = encodedNonce
		response.CreateAuthSuccessResponse(tokenMsg, w)
		return
	}
}

package setup

import (
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/schema_mgmt"
)

// Postgres ensures that the database is managed under schema_mgmt control.  It
// then applies schema migrations to upgrade the dataset to the highest
// available version up to the specified version.  If the provided version is
// an empty string, the dataset is upgraded to the latest available schema.
func Postgres(pg *connect.PostgresExecutor, version string) error {
	return schema_mgmt.ApplyMigrations(&schema_mgmt.PostgresMigrationExecutor{
		Client: pg,
	}, "data-core-pg", version)
}

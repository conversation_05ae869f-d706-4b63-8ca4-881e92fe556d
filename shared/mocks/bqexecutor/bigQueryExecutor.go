package bqexecutor

import (
	"context"

	"cloud.google.com/go/bigquery"

	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/mocks/dbexecutor"
)

// FakeBigQueryExecutor is a mock implementation of the BigQueryExecutorInterface
type FakeBigQueryExecutor struct {
	dbexecutor.FakeDBExecutor
	Client *bigquery.Client
	Config connect.DatabaseConfig
	Ctx    context.Context
}

// Get the BigQuery client.
func (bq *FakeBigQueryExecutor) GetClient() connect.BQClient {
	return connect.NewBQClientAdapter(bq.Client)
}

// Get the BigQuery config.
func (bq *FakeBigQueryExecutor) GetConfig() connect.DatabaseConfig {
	return bq.Config
}

// Get the context.
func (bq *FakeBigQueryExecutor) GetContext() context.Context {
	return bq.Ctx
}

// main/main_test.go
package main

import (
	"bytes"
	"context"
	"errors"
	"net/http"
	"os"
	"reflect"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"

	"synapse-its.com/shared/bqbatch"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/healthz"
	"synapse-its.com/shared/logger"
	"synapse-its.com/shared/mocks"
)

// fakeServer implements our Server interface.
type fakeServer struct {
	listenCalled   bool
	shutdownCalled bool
	listenErr      error
	shutdownErr    error
}

func (f *fakeServer) ListenAndServe() error {
	f.listenCalled = true
	return f.listenErr
}

func (f *fakeServer) Shutdown(ctx context.Context) error {
	f.shutdownCalled = true
	return f.shutdownErr
}

func TestRun_AllPaths(t *testing.T) {
	// Backup globals
	origLogger := logger.Logger
	origHealthzNewServer := healthzNewServer
	origTimeSleep := timeSleep
	origLoggerFatalf := loggerFatalf
	defer func() {
		logger.Logger = origLogger
		healthzNewServer = origHealthzNewServer
		timeSleep = origTimeSleep
		loggerFatalf = origLoggerFatalf
	}()

	tests := []struct {
		name                    string
		ctxFunc                 func() context.Context
		signalDelay             time.Duration
		addr                    string
		batchErr                error
		healthzListenErr        error
		healthzShutdownErr      error
		listenErr               error
		shutdownErr             error
		expectBatchLogErr       bool
		expectHealthzLogErr     bool
		expectSvrListenErr      bool
		expectSvrShutdownLogErr bool
	}{
		{
			name:        "signal_shutdown",
			ctxFunc:     func() context.Context { return context.Background() },
			signalDelay: 10 * time.Millisecond,
			addr:        ":8080",
		},
		{
			name:        "immediate_ctx_cancel",
			ctxFunc:     func() context.Context { c, cancel := context.WithCancel(context.Background()); cancel(); return c },
			signalDelay: 0,
			addr:        ":9090",
		},
		{
			name:              "batch_shutdown_error",
			ctxFunc:           func() context.Context { return context.Background() },
			signalDelay:       10 * time.Millisecond,
			addr:              ":7070",
			batchErr:          errors.New("boom"),
			expectBatchLogErr: true,
		},
		{
			name:                "healthz_listen_error",
			ctxFunc:             func() context.Context { return context.Background() },
			signalDelay:         10 * time.Millisecond,
			addr:                ":6060",
			healthzListenErr:    errors.New("hz-listen-fail"),
			expectHealthzLogErr: true,
		},
		{
			name:                "healthz_shutdown_error",
			ctxFunc:             func() context.Context { return context.Background() },
			signalDelay:         10 * time.Millisecond,
			addr:                ":6061",
			healthzShutdownErr:  errors.New("hz-shutdown-fail"),
			expectHealthzLogErr: false, // No longer errors on shutdown error, only on listen error
		},
		{
			name:                    "server_shutdown_error",
			ctxFunc:                 func() context.Context { return context.Background() },
			signalDelay:             10 * time.Millisecond,
			addr:                    ":5050",
			shutdownErr:             errors.New("shut-boom"),
			expectSvrShutdownLogErr: true,
		},
		{
			name:               "server_listen_error",
			ctxFunc:            func() context.Context { return context.Background() },
			signalDelay:        10 * time.Millisecond,
			addr:               ":4040",
			listenErr:          errors.New("start-pow"),
			expectSvrListenErr: true,
		},
	}

	for _, tc := range tests {
		tc := tc // capture
		t.Run(tc.name, func(t *testing.T) {
			// Buffer‐backed logger if we expect errors in logs
			var logBuf bytes.Buffer
			if tc.expectBatchLogErr || tc.expectHealthzLogErr || tc.expectSvrListenErr || tc.expectSvrShutdownLogErr {
				// Redirect Fatalf to Errorf so we don't os.Exit
				loggerFatalf = logger.Errorf

				encCfg := zap.NewDevelopmentEncoderConfig()
				encCfg.EncodeTime = zapcore.EpochTimeEncoder
				core := zapcore.NewCore(zapcore.NewConsoleEncoder(encCfg), zapcore.AddSync(&logBuf), zap.DebugLevel)
				logger.Logger = zap.New(core)
			}

			// Override healthzShutdown to return tc.healthErr
			fakeHZ := &mocks.FakeHealthzServer{
				ListenErr:   tc.healthzListenErr,
				ShutdownErr: tc.healthzShutdownErr,
			}
			healthzNewServer = func(port string) healthz.HealthzServer {
				return fakeHZ
			}
			// No actual sleep
			timeSleep = func(d time.Duration) {}

			// Context and signals
			ctx := tc.ctxFunc()
			sigs := func() <-chan os.Signal {
				ch := make(chan os.Signal, 1)
				if tc.signalDelay > 0 {
					go func() {
						time.Sleep(tc.signalDelay)
						ch <- os.Interrupt
					}()
				}
				return ch
			}

			// Fake connections
			newConns := func(_ context.Context) *connect.Connections {
				return mocks.FakeConns()
			}
			// Fake batcher
			fakeBatch := func(_ connect.BigQueryExecutorInterface, _ connect.PsClient) bqbatch.Batcher {
				return mocks.FakeBatcherWithOptions(mocks.WithBatchShutdownError(tc.batchErr))
			}

			// Fake server
			fakeSrv := &fakeServer{listenErr: tc.listenErr, shutdownErr: tc.shutdownErr}
			newServer := func(addr string, _ http.Handler) Server {
				assert.Equal(t, tc.addr, addr, "NewServer got wrong addr")
				return fakeSrv
			}

			// Run!
			err := Run(ctx, "", tc.addr, newConns, fakeBatch, newServer, sigs)
			require.NoError(t, err, "Run should not return error")

			// Ensure the fake healthz methods were called
			assert.True(t, fakeHZ.ListenCalled, "healthz ListenAndServe should be called")
			assert.True(t, fakeHZ.SetBootCalled, "healthz SetBootComplete should be called")
			assert.True(t, fakeHZ.SetReadyCalled, "healthz SetReady should be called")
			assert.True(t, fakeHZ.SetNotReadyCalled, "healthz SetNotReady should be called")
			assert.True(t, fakeHZ.ShutdownCalled, "healthz Shutdown should be called")

			// Ensure ListenAndServe and Shutdown were called
			assert.True(t, fakeSrv.listenCalled, "ListenAndServe should be called")
			assert.True(t, fakeSrv.shutdownCalled, "Shutdown should be called")

			// Check log buffer for the expected error messages
			logs := logBuf.String()
			if tc.expectBatchLogErr {
				assert.Contains(t, logs, "batch shutdown error: boom")
			}
			if tc.expectHealthzLogErr {
				assert.Contains(t, logs, "healthz listen error: hz-listen-fail")
			}
			if tc.expectSvrListenErr {
				assert.Contains(t, logs, "server listenandserve error: start-pow")
			}
			if tc.expectSvrShutdownLogErr {
				assert.Contains(t, logs, "server shutdown error: shut-boom")
			}
		})
	}
}

func TestDefaultSignalChan(t *testing.T) {
	ch := DefaultSignalChan()
	assert.NotNil(t, ch, "channel should not be nil")
	v := reflect.ValueOf(ch)
	assert.Equal(t, reflect.Chan, v.Kind(), "should be a channel")
	assert.Equal(t, 1, v.Cap(), "should have buffer size 1")
}

func TestDefaultServer(t *testing.T) {
	mux := http.NewServeMux()
	addr := ":4242"
	srv := DefaultServer(addr, mux)

	// It should return an *http.Server
	assert.IsType(t, &http.Server{}, srv)
	hs := srv.(*http.Server)
	assert.Equal(t, addr, hs.Addr, "Addr should match")
	assert.Equal(t, mux, hs.Handler, "Handler should match")
}

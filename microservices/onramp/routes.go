package main

import (
	"context"
	_ "embed"
	"encoding/json"
	"net"
	"net/http"
	"os"
	"strings"
	"time"

	"github.com/coreos/go-oidc"
	"github.com/gorilla/mux"
	"golang.org/x/oauth2"

	"synapse-its.com/onramp/handlers"
	"synapse-its.com/onramp/handlers/assets"
	"synapse-its.com/shared/api/middleware"
	"synapse-its.com/shared/bqbatch"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/httplogger"
	"synapse-its.com/shared/logger"
	"synapse-its.com/shared/util"
	RestOrganization "synapse-its.com/shared/rest/onramp/organization"
)

// NewRouter initializes the router and registers routes.
func NewRouter(connections *connect.Connections, batch bqbatch.Batcher) *mux.Router {
	router := mux.NewRouter()

	// Apply middleware globally (e.g., logging, authentication).
	router.Use(middleware.ConnectionsMiddleware(connections))
	router.Use(middleware.BQBatchMiddleware(batch))
	router.Use(httplogger.LoggingMiddleware)

	// These endpoints for full CRUD operations for organization management
	router.HandleFunc("/api/organizations", RestOrganization.CreateHandler).Methods(http.MethodPost)
	router.HandleFunc("/api/organizations", RestOrganization.GetAllHandler).Methods(http.MethodGet)
	router.HandleFunc("/api/organizations/{identifier}", RestOrganization.GetByIdentifierHandler).Methods(http.MethodGet)
	router.HandleFunc("/api/organizations/{identifier}", RestOrganization.UpdateHandler).Methods(http.MethodPatch)
	router.HandleFunc("/api/organizations/{identifier}", RestOrganization.DeleteHandler).Methods(http.MethodDelete)

	// Bare-bones /foo endpoint returning ["Alice","Bob"]
	router.HandleFunc("/foo", handlers.FooHandler).Methods(http.MethodGet)

	// /api endpoints
	apiRouter := router.PathPrefix("/api").Subrouter()
	apiRouter.HandleFunc("/foo", handlers.FooHandler).Methods(http.MethodGet)

	// Define default endpoints
	router.HandleFunc("/assets/env.js", assets.EnvJsHandler)
	fs := http.FileServer(http.Dir("./static/browser"))

	// Login/Callback endpoints
	router.HandleFunc("/login", handleLogin).Methods(http.MethodGet)
	router.HandleFunc("/callback", handleCallback).Methods(http.MethodGet)
	router.HandleFunc("/logout", handleLogout).Methods(http.MethodGet)

	protectedRouter := router.PathPrefix("/protected").Subrouter()
	protectedRouter.Use(authMiddleware)
	protectedRouter.HandleFunc("/foo", handlers.FooHandler).Methods(http.MethodGet)
	protectedRouter.HandleFunc("/profile", func(w http.ResponseWriter, r *http.Request) {
		// Fetch the claims from the context.
		claims, ok := r.Context().Value(userKey).(map[string]interface{})
		if !ok {
			http.Error(w, "unauthorized", http.StatusUnauthorized)
			return
		}

		// Extract the name and email from the claims.
		name, okName := claims["name"].(string)
		name = map[bool]string{
			true:  name,
			false: "",
		}[okName]
		email, okEmail := claims["email"].(string)
		email = map[bool]string{
			true:  email,
			false: "",
		}[okEmail]
		profile := UserProfile{
			Name:  name,
			Email: email,
		}

		// If no email address, then we don't return the profile.
		if email == "" {
			http.Error(w, "profile not found", http.StatusNotFound)
			return
		}

		// Return the profile as JSON
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(profile)
	})

	// The catch-all (file system server) must be last.
	router.PathPrefix("/").Handler(fs)
	return router
}

type UserProfile struct {
	Name  string `json:"name"`
	Email string `json:"email"`
}

// OIDCConfig holds the configuration for OpenID Connect (OIDC) authentication.
type OIDCConfig struct {
	ClientID     string
	ClientSecret string
	RedirectURL  string
	IssuerURL    string
	Provider     *oidc.Provider
	OAuth2Config *oauth2.Config
	Verifier     *oidc.IDTokenVerifier
	Scope        string
}

var (
	// synapseOIDC is a global variable that holds the OIDC configuration
	// for the Synapse OAuth2 server.
	synapseOIDC = OIDCConfig{
		ClientID:     os.Getenv("SYNAPSE_OIDC_CLIENT_ID"),
		ClientSecret: os.Getenv("SYNAPSE_OIDC_CLIENT_SECRET"),
		RedirectURL:  os.Getenv("SYNAPSE_OIDC_CLIENT_CALLBACK_URL"),
		IssuerURL:    os.Getenv("SYNAPSE_OIDC_ISSUER_URL"),
	}

	// synapseOIDCLocal is a local version of the OIDC configuration
	// used for development purposes, pointing to keycloak.
	synapseOIDCLocal OIDCConfig

	synapseOIDCScopes = []string{oidc.ScopeOpenID, "profile", "email"}

	// localhostHTTPProxy is a custom HTTP client that rewrites requests
	// to "localhost:8091" to "host.docker.internal:8091". This is necessary
	// for the OIDC provider to communicate with the host machine from within
	// a Docker container, as Docker containers cannot directly access services
	// running on the host machine using "localhost".  This is not a security
	// concern in prod, because in production, the request will simply fail
	// because there is no OIDC provider listening there.
	localhostHTTPProxy = &http.Client{Transport: &http.Transport{
		DialContext: func(ctx context.Context, network, addr string) (net.Conn, error) {
			if strings.HasSuffix(addr, "localhost:8091") {
				addr = "host.docker.internal:8091"
			}
			return (&net.Dialer{}).DialContext(ctx, network, addr)
		},
	}}
)

// Initialize all OIDC configurations.
func init() {
	ctx := context.Background()

	// Real Synapse OIDC configuration
	var err error
	start := time.Now()
	for {
		synapseOIDC.Provider, err = oidc.NewProvider(ctx, synapseOIDC.IssuerURL)
		if err != nil {
			logger.Warnf("failed to init real OIDC provider (OK for testing, bad for prod): %v", err)
			time.Sleep(time.Second)
			continue
		}
		break
	}
	logger.Info(time.Since(start))

	synapseOIDC.Verifier = synapseOIDC.Provider.Verifier(&oidc.Config{
		ClientID: synapseOIDC.ClientID,
	})
	synapseOIDC.OAuth2Config = &oauth2.Config{
		ClientID:     synapseOIDC.ClientID,
		ClientSecret: synapseOIDC.ClientSecret,
		Endpoint:     synapseOIDC.Provider.Endpoint(),
		RedirectURL:  synapseOIDC.RedirectURL,
		Scopes:       synapseOIDCScopes,
	}

	// Local Synapse OIDC configuration for development
	synapseOIDCLocal = synapseOIDC

	// Override only the URLs for dev.  In production, the URLs won't contain
	// "onramp" or "keycloak", so they won't change.
	synapseOIDCLocal.IssuerURL = strings.ReplaceAll(
		synapseOIDC.IssuerURL,
		"keycloak:8080",
		"localhost:8091",
	)
	synapseOIDCLocal.RedirectURL = strings.ReplaceAll(
		synapseOIDC.RedirectURL,
		"onramp:4200",
		"localhost:4200",
	)

	// Re-init provider/verifier/oauth2.Config for localhost
	synapseOIDCLocal.Provider, err = oidc.NewProvider(oidc.ClientContext(ctx, localhostHTTPProxy), synapseOIDCLocal.IssuerURL)
	if err != nil {
		// In production, this will error because there is no OIDC provider
		// listening on localhost:8091.  `synapseOIDCLocal` is a copy of the actual
		// OIDC provider because it is not overwritten here.  If someone tries to
		// send us forged host headers, the worst that will happen is that nothing
		// will validate, which is what we want.
		logger.Warnf("failed to init local OIDC provider: %v", err)
	} else {
		// If we made it here, then the provider was able to talk to the OIDC
		// server on localhost.
		synapseOIDCLocal.Verifier = synapseOIDCLocal.Provider.Verifier(&oidc.Config{
			ClientID: synapseOIDCLocal.ClientID,
		})
		synapseOIDCLocal.OAuth2Config = &oauth2.Config{
			ClientID:     synapseOIDCLocal.ClientID,
			ClientSecret: synapseOIDCLocal.ClientSecret,
			Endpoint:     synapseOIDCLocal.Provider.Endpoint(),
			RedirectURL:  synapseOIDCLocal.RedirectURL,
			Scopes:       synapseOIDCScopes,
		}
	}
}

// A simple in-memory session store
var sessionStore = map[string]*oauth2.Token{}

func handleLogin(w http.ResponseWriter, r *http.Request) {
	isDev := strings.HasPrefix(r.Host, "localhost:4200")
	state := util.RandomString(32)

	// Set the state cookie
	http.SetCookie(w, &http.Cookie{
		Name:     "oauth_state",
		Value:    state,
		Path:     "/",
		HttpOnly: true,
		Secure:   !(isDev || strings.HasPrefix(r.Host, "onramp:4200")),
		SameSite: http.SameSiteLaxMode,
	})

	// Choose the OIDC configuration based on the request host
	cfg := map[bool]*oauth2.Config{
		false: synapseOIDC.OAuth2Config,
		true:  synapseOIDCLocal.OAuth2Config,
	}[isDev]

	// Redirect to the OIDC provider's authorization endpoint
	http.Redirect(w, r, cfg.AuthCodeURL(state), http.StatusFound)
}

func handleCallback(w http.ResponseWriter, r *http.Request) {
	ctx := oidc.ClientContext(r.Context(), localhostHTTPProxy)
	isDev := strings.HasPrefix(r.Host, "localhost:4200")

	// Verify state cookie
	st, err := r.Cookie("oauth_state")
	if err != nil || r.URL.Query().Get("state") != st.Value {
		http.Error(w, "invalid state", http.StatusBadRequest)
		return
	}

	// Delete the state cookie so it can’t be reused
	http.SetCookie(w, &http.Cookie{
		Name:     "oauth_state",
		Value:    "",
		Path:     "/",
		HttpOnly: true,
		Secure:   !isDev,
		Expires:  time.Unix(0, 0),
		MaxAge:   -1,
		SameSite: http.SameSiteLaxMode,
	})

	// Choose the OIDC configuration based on the request host
	oidcConfig := map[bool]*OIDCConfig{
		false: &synapseOIDC,
		true:  &synapseOIDCLocal,
	}[isDev]

	// Exchange code for token
	token, err := oidcConfig.OAuth2Config.Exchange(ctx, r.URL.Query().Get("code"))
	if err != nil {
		http.Error(w, "token exchange failed: "+err.Error(), http.StatusInternalServerError)
		return
	}

	// Verify the ID Token
	rawID := token.Extra("id_token").(string)
	_, err = oidcConfig.Verifier.Verify(ctx, rawID)
	if err != nil {
		http.Error(w, "ID token invalid: "+err.Error(), http.StatusUnauthorized)
		return
	}

	// Create a session
	sessionID := util.RandomString(32)
	sessionStore[sessionID] = token

	http.SetCookie(w, &http.Cookie{
		Name:     "session_id",
		Value:    sessionID,
		Path:     "/",
		HttpOnly: true,
		Secure:   !(isDev || strings.HasPrefix(r.Host, "onramp:4200")),
		SameSite: http.SameSiteStrictMode,
	})

	http.Redirect(w, r, "/", http.StatusFound)
}

func handleLogout(w http.ResponseWriter, r *http.Request) {
	isDev := strings.HasPrefix(r.Host, "localhost:4200")

	// Grab the session_id cookie (if any)
	cookie, err := r.Cookie("session_id")
	if err == nil {
		// Delete the server‐side session
		delete(sessionStore, cookie.Value)
	}

	// Clear the cookie in the browser
	http.SetCookie(w, &http.Cookie{
		Name:     "session_id",
		Value:    "",
		Path:     "/",
		HttpOnly: true,
		Secure:   !(isDev || strings.HasPrefix(r.Host, "onramp:4200")),
		Expires:  time.Unix(0, 0),
		MaxAge:   -1,
		SameSite: http.SameSiteNoneMode,
	})

	// Redirect to its end-session endpoint:
	// NOTE: This would log the user out of the OIDC provider, but it is not
	// strictly necessary for our use case, since we are not using the OIDC
	// provider for anything other than authentication.  This code is left here
	// for reference, in case we want to implement a full logout flow in the
	// future.
	//
	// redirectURI := url.QueryEscape(oidcConfig.RedirectURL)
	// logoutURL := oidcConfig.IssuerURL +
	// 	"/protocol/openid-connect/logout?redirect_uri=" + redirectURI
	// http.Redirect(w, r, logoutURL, http.StatusFound)

	http.Redirect(w, r, "/", http.StatusFound)
}

type key int

const userKey key = 0

func authMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		ctx := oidc.ClientContext(r.Context(), localhostHTTPProxy)
		isDev := strings.HasPrefix(r.Host, "localhost:4200")
		oidcConfig := map[bool]*OIDCConfig{
			false: &synapseOIDC,
			true:  &synapseOIDCLocal,
		}[isDev]
		verifier := oidcConfig.Verifier

		// 1) Grab session cookie
		c, err := r.Cookie("session_id")
		if err != nil {
			http.Error(w, "unauthorized", http.StatusUnauthorized)
			return
		}
		tok, ok := sessionStore[c.Value]
		if !ok {
			http.Error(w, "invalid session", http.StatusUnauthorized)
			return
		}

		// 2) Verify ID Token again (optional if you trust your store)
		rawID := tok.Extra("id_token").(string)
		idToken, err := verifier.Verify(ctx, rawID)
		if err != nil {
			http.Error(w, "invalid token", http.StatusUnauthorized)
			return
		}

		// 3) Extract claims
		var claims map[string]interface{}
		if err := idToken.Claims(&claims); err != nil {
			http.Error(w, "cannot read claims", http.StatusInternalServerError)
			return
		}

		// 4) Next handler with claims in context
		next.ServeHTTP(w, r.WithContext(context.WithValue(r.Context(), userKey, claims)))
	})
}

package middleware

import (
	"context"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"

	authorizer "synapse-its.com/shared/api/authorizer"
)

func TestJWTAuthorizerMiddleware(t *testing.T) {
	// save + restore original
	origAuth := authorizer.Authorize
	defer func() { authorizer.Authorize = origAuth }()

	tests := []struct {
		name          string
		path          string
		setupAuth     func()
		wantNext      bool
		wantStatus    int
		wantBodyCheck func(t *testing.T, body map[string]interface{})
	}{
		{
			name: "unprotected path skips auth",
			path: "/ping",
			setupAuth: func() {
				authorizer.Authorize = func(ctx context.Context, r *http.Request) (context.Context, error) {
					t.Fatal("Authorize should NOT be called on unprotected paths")
					return ctx, nil
				}
			},
			wantNext:   true,
			wantStatus: http.StatusOK,
			wantBodyCheck: func(t *testing.T, body map[string]interface{}) {
				if body["msg"] != "pong" {
					t.<PERSON><PERSON><PERSON>("expected next handler body 'pong', got %v", body["msg"])
				}
			},
		},
		{
			name: "unauthorized on protected",
			path: "/data/v2/device",
			setupAuth: func() {
				authorizer.Authorize = func(ctx context.Context, r *http.Request) (context.Context, error) {
					return ctx, authorizer.ErrUnauthorized
				}
			},
			wantNext:   false,
			wantStatus: http.StatusUnauthorized,
			wantBodyCheck: func(t *testing.T, body map[string]interface{}) {
				if body["status"] != "error" {
					t.Errorf("expected status 'error', got %v", body["status"])
				}
				if body["message"] != "Unauthorized" {
					t.Errorf("expected message 'Unauthorized', got %v", body["message"])
				}
				if code, ok := body["code"].(float64); !ok || int(code) != http.StatusUnauthorized {
					t.Errorf("expected code %d, got %v", http.StatusUnauthorized, body["code"])
				}
				if body["data"] != nil {
					t.Errorf("expected data nil, got %v", body["data"])
				}
			},
		},
		{
			name: "forbidden on protected",
			path: "/user/v2/profile",
			setupAuth: func() {
				authorizer.Authorize = func(ctx context.Context, r *http.Request) (context.Context, error) {
					return ctx, authorizer.ErrForbidden
				}
			},
			wantNext:   false,
			wantStatus: http.StatusForbidden,
			wantBodyCheck: func(t *testing.T, body map[string]interface{}) {
				if body["status"] != "forbidden" {
					t.Errorf("expected status 'forbidden', got %v", body["status"])
				}
				if body["message"] != "Forbidden" {
					t.Errorf("expected message 'Forbidden', got %v", body["message"])
				}
				if code, ok := body["code"].(float64); !ok || int(code) != http.StatusForbidden {
					t.Errorf("expected code %d, got %v", http.StatusForbidden, body["code"])
				}
				if body["data"] != nil {
					t.Errorf("expected data nil, got %v", body["data"])
				}
			},
		},
		{
			name: "internal error on protected",
			path: "/api/v3/data/device",
			setupAuth: func() {
				authorizer.Authorize = func(ctx context.Context, r *http.Request) (context.Context, error) {
					return ctx, errors.New("boom")
				}
			},
			wantNext:   false,
			wantStatus: http.StatusInternalServerError,
			wantBodyCheck: func(t *testing.T, body map[string]interface{}) {
				if body["status"] != "error" {
					t.Errorf("expected status 'error', got %v", body["status"])
				}
				if body["message"] != "Internal Server Error" {
					t.Errorf("expected message 'Internal Server Error', got %v", body["message"])
				}
				if code, ok := body["code"].(float64); !ok || int(code) != http.StatusInternalServerError {
					t.Errorf("expected code %d, got %v", http.StatusInternalServerError, body["code"])
				}
				if body["data"] != nil {
					t.Errorf("expected data nil, got %v", body["data"])
				}
			},
		},
		{
			name: "success passes to next",
			path: "/data/v2/fault",
			setupAuth: func() {
				authorizer.Authorize = func(ctx context.Context, r *http.Request) (context.Context, error) {
					return ctx, nil
				}
			},
			wantNext:   true,
			wantStatus: http.StatusOK,
			wantBodyCheck: func(t *testing.T, body map[string]interface{}) {
				if body["ok"] != true {
					t.Errorf("expected next handler body ok=true, got %v", body["ok"])
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.setupAuth()

			// build handler chain
			next := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				// simple JSON response
				w.Header().Set("Content-Type", "application/json")
				w.WriteHeader(http.StatusOK)
				json.NewEncoder(w).Encode(map[string]interface{}{"msg": "pong", "ok": true})
			})
			handler := JWTAuthorizerMiddleware(next)

			req := httptest.NewRequest("GET", tt.path, nil)
			rr := httptest.NewRecorder()

			handler.ServeHTTP(rr, req)

			// did we hit next?
			if got := rr.Code == http.StatusOK && tt.wantNext; got != tt.wantNext {
				t.Errorf("nextCalled=%v, want %v", got, tt.wantNext)
			}

			// status check
			if rr.Code != tt.wantStatus {
				t.Errorf("status code = %d; want %d", rr.Code, tt.wantStatus)
			}

			// decode body
			var body map[string]interface{}
			if err := json.NewDecoder(rr.Body).Decode(&body); err != nil {
				t.Fatalf("decoding body: %v", err)
			}
			tt.wantBodyCheck(t, body)
		})
	}
}

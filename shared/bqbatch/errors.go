package bqbatch

import (
	"errors"
)

var (
	ErrBatcherIsShutDown = errors.New("batcher is shut down")
	ErrUnknownType       = errors.New("unknown struct type")
	ErrNotStruct         = errors.New("row must be a struct or pointer to struct")
	ErrSchemaInfer       = errors.New("schema inference failed")
	ErrDefaultBatcher    = errors.New("default batcher failed initialization")
	ErrBatchContext      = errors.New("batch is not found in the context")
)

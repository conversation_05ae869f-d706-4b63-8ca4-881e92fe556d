package httplogger

import (
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"go.uber.org/zap/zaptest/observer"
	"synapse-its.com/shared/logger"
)

// findHTTPReqField extracts the "logging.googleapis.com/httpRequest" field from a logged entry.
func findHTTPReqField(fields []zap.Field) (map[string]interface{}, bool) {
	for _, field := range fields {
		if field.Key == "logging.googleapis.com/httpRequest" {
			if m, ok := field.Interface.(map[string]interface{}); ok {
				return m, true
			}
		}
	}
	return nil, false
}

func TestLoggingMiddleware(t *testing.T) {
	// Set up zap observer and override the global logger.
	core, observed := observer.New(zapcore.DebugLevel)
	logger.Logger = zap.New(core)

	// Create a dummy handler that returns a specific status code and writes a body.
	dummyHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusCreated) // 201
		w.Write([]byte("hello world"))
	})

	// Wrap the dummy handler with the LoggingMiddleware.
	wrappedHandler := LoggingMiddleware(dummyHandler)

	// Create a test request.
	req := httptest.NewRequest("GET", "http://example.com/test", nil)
	req.Header.Set("User-Agent", "TestAgent")
	req.Header.Set("Referer", "http://referrer.com")
	// Set a dummy remote address.
	req.RemoteAddr = "*********:1234"

	// Create a ResponseRecorder.
	rr := httptest.NewRecorder()

	// Call the middleware-wrapped handler.
	start := time.Now()
	wrappedHandler.ServeHTTP(rr, req)
	_ = time.Since(start) // We measure latency here, but we won't enforce a strict comparison.

	// Verify the response.
	if status := rr.Code; status != http.StatusCreated {
		t.Errorf("expected status code %d, got %d", http.StatusCreated, status)
	}
	body := rr.Body.String()
	if body != "hello world" {
		t.Errorf("expected body %q, got %q", "hello world", body)
	}

	// Ensure a log entry was captured.
	logs := observed.All()
	if len(logs) != 1 {
		t.Fatalf("expected 1 log entry, got %d", len(logs))
	}
	entry := logs[0]

	// Verify the log message.
	expectedMsg := "HTTP GET /test processed"
	if entry.Entry.Message != expectedMsg {
		t.Errorf("expected log message %q, got %q", expectedMsg, entry.Entry.Message)
	}

	// Verify the structured httpRequest field.
	httpReqField, found := findHTTPReqField(entry.Context)
	if !found {
		t.Fatal("expected logging.googleapis.com/httpRequest field in log context")
	}

	// Check some of the expected values.
	if method, ok := httpReqField["requestMethod"].(string); !ok || method != "GET" {
		t.Errorf("expected requestMethod 'GET', got %v", httpReqField["requestMethod"])
	}

	if url, ok := httpReqField["requestUrl"].(string); !ok || url != "http://example.com/test" {
		t.Errorf("expected requestUrl 'http://example.com/test', got %v", httpReqField["requestUrl"])
	}

	if userAgent, ok := httpReqField["userAgent"].(string); !ok || userAgent != "TestAgent" {
		t.Errorf("expected userAgent 'TestAgent', got %v", httpReqField["userAgent"])
	}

	if remoteIP, ok := httpReqField["remoteIp"].(string); !ok || !strings.Contains(remoteIP, "*********") {
		t.Errorf("expected remoteIp to contain '*********', got %v", httpReqField["remoteIp"])
	}

	if referer, ok := httpReqField["referer"].(string); !ok || referer != "http://referrer.com" {
		t.Errorf("expected referer 'http://referrer.com', got %v", httpReqField["referer"])
	}

	if status, ok := httpReqField["status"].(int); !ok || status != http.StatusCreated {
		t.Errorf("expected status %d, got %v", http.StatusCreated, httpReqField["status"])
	}

	if size, ok := httpReqField["responseSize"].(int); !ok || size != len("hello world") {
		t.Errorf("expected responseSize %d, got %v", len("hello world"), httpReqField["responseSize"])
	}

	// Instead of comparing logged latency with a measured value,
	// simply check that it is a positive float64.
	loggedLatency, ok := httpReqField["latency"].(float64)
	if !ok {
		t.Errorf("latency field is not a float64: %v", httpReqField["latency"])
	}
	if loggedLatency <= 0 {
		t.Errorf("expected logged latency to be positive, got %v", loggedLatency)
	}
}

package firestore

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestSeedAndGet(t *testing.T) {
	t.Parallel()
	ctx := context.Background()
	fs := NewFakeFirestoreClient()
	assert := assert.New(t)

	// Seed a document
	initial := map[string]interface{}{"foo": "bar"}
	fs.SeedDoc("colA", "doc1", initial)

	// Get via DocumentRef.Get
	ref := fs.Collection("colA").Doc("doc1")
	snap, err := ref.Get(ctx)
	assert.NoError(err)
	assert.True(snap.Exists(), "seeded doc should exist")
	data := snap.Data()
	assert.Equal("bar", data["foo"], "field foo should be 'bar'")
}

func TestGetNonexistentDoc(t *testing.T) {
	t.Parallel()
	ctx := context.Background()
	fs := NewFakeFirestoreClient()
	assert := assert.New(t)

	// No seeding
	ref := fs.Collection("colB").Doc("missing")
	snap, err := ref.Get(ctx)
	assert.NoError(err)
	assert.False(snap.Exists(), "nonexistent doc should not exist")
	assert.Nil(snap.Data(), "data of nonexistent doc should be nil")
}

func TestSetAndOverwrite(t *testing.T) {
	t.Parallel()
	ctx := context.Background()
	fs := NewFakeFirestoreClient()
	assert := assert.New(t)

	ref := fs.Collection("colC").Doc("docX")

	// First Set
	_, err := ref.Set(ctx, map[string]interface{}{"n": 1})
	assert.NoError(err)
	snap, _ := ref.Get(ctx)
	assert.True(snap.Exists())
	assert.Equal(1, snap.Data()["n"])

	// Overwrite with new data
	_, err = ref.Set(ctx, map[string]interface{}{"n": 2, "extra": "yes"})
	assert.NoError(err)
	snap, _ = ref.Get(ctx)
	assert.True(snap.Exists())
	assert.Equal(2, snap.Data()["n"])
	assert.Equal("yes", snap.Data()["extra"])
}

func TestSet_WrongType(t *testing.T) {
	t.Parallel()
	ctx := context.Background()
	fs := NewFakeFirestoreClient()
	assert := assert.New(t)

	// Seed an initial document so we can verify it isn't overwritten
	initial := map[string]interface{}{"keep": "value"}
	fs.SeedDoc("colZ", "docZ", initial)

	// Attempt to Set with the wrong type (not a map[string]interface{})
	wr, err := fs.Collection("colZ").Doc("docZ").Set(ctx, "not-a-map")
	assert.NoError(err, "should not return an error on wrong type")
	assert.Nil(wr, "write result should be nil when data is wrong type")

	// Verify the original document is still intact
	snap, err := fs.Collection("colZ").Doc("docZ").Get(ctx)
	assert.NoError(err)
	assert.True(snap.Exists(), "original doc should still exist")
	data := snap.Data()
	assert.Equal("value", data["keep"], "original field must be unchanged")
}

func TestBulkWriterDeleteFlush(t *testing.T) {
	t.Parallel()
	ctx := context.Background()
	fs := NewFakeFirestoreClient()
	assert := assert.New(t)

	// Seed two docs
	fs.SeedDoc("colD", "a", map[string]interface{}{"v": "1"})
	fs.SeedDoc("colD", "b", map[string]interface{}{"v": "2"})

	// Queue delete of "a"
	bw := fs.BulkWriter(ctx)
	err := bw.Delete(fs.Collection("colD").Doc("a"))
	assert.NoError(err)

	// Before Flush, both should still exist
	aSnap, _ := fs.Collection("colD").Doc("a").Get(ctx)
	bSnap, _ := fs.Collection("colD").Doc("b").Get(ctx)
	assert.True(aSnap.Exists(), "a should exist before flush")
	assert.True(bSnap.Exists(), "b should exist before flush")

	// Flush and then "a" is gone, "b" remains
	err = bw.Flush()
	assert.NoError(err)
	aSnap, _ = fs.Collection("colD").Doc("a").Get(ctx)
	bSnap, _ = fs.Collection("colD").Doc("b").Get(ctx)
	assert.False(aSnap.Exists(), "a should be deleted after flush")
	assert.True(bSnap.Exists(), "b should remain")
}

func TestGetAllDocsShallowCopy(t *testing.T) {
	t.Parallel()
	fs := NewFakeFirestoreClient()
	assert := assert.New(t)

	fs.SeedDoc("colE", "x", map[string]interface{}{"k": "v"})
	fs.SeedDoc("colE", "y", map[string]interface{}{"k": "w"})

	docs := fs.GetAllDocs("colE")
	// Original store has two docs
	assert.Len(docs, 2)
	assert.Equal("v", docs["x"]["k"])
	assert.Equal("w", docs["y"]["k"])

	// Modify returned map and ensure original not affected
	delete(docs, "x")
	assert.Len(docs, 1)
	orig := fs.GetAllDocs("colE")
	assert.Len(orig, 2, "original store must remain unchanged")
}

func TestCollectionDocCreatesCollection(t *testing.T) {
	t.Parallel()
	ctx := context.Background()
	fs := NewFakeFirestoreClient()
	assert := assert.New(t)

	// Collection not pre-existing → calling Doc should create it
	ref := fs.Collection("newCol").Doc("d1")
	snap, err := ref.Get(ctx)
	assert.NoError(err)
	assert.False(snap.Exists(), "new doc in new collection should not exist")

	// Now set and get
	_, err = ref.Set(ctx, map[string]interface{}{"a": 1})
	assert.NoError(err)
	snap, err = ref.Get(ctx)
	assert.NoError(err)
	assert.True(snap.Exists())
	assert.Equal(1, snap.Data()["a"])
}

func TestCloseNoOp(t *testing.T) {
	t.Parallel()
	fs := NewFakeFirestoreClient()
	assert := assert.New(t)

	// Close should always return nil
	err := fs.Close()
	assert.NoError(err)
}

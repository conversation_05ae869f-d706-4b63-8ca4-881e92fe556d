package bqexecutor

import (
	"context"
	"testing"

	"cloud.google.com/go/bigquery"
	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/connect"
)

// TestFakeBigQueryExecutor_Getters covers all getter methods on FakeBigQueryExecutor.
func TestFakeBigQueryExecutor_Getters(t *testing.T) {
	// Prepare dummy values
	client := new(bigquery.Client)
	config := connect.DatabaseConfig{Environment: "dev", DBName: "test-dataset"}
	ctx := context.Background()

	exec := &FakeBigQueryExecutor{
		Client: client,
		Config: config,
		Ctx:    ctx,
	}

	// Verify each getter returns the configured value
	assert.Equal(t, connect.NewBQClientAdapter(client), exec.GetClient(), "GetClient should return the injected BigQuery client")
	assert.Equal(t, config, exec.GetConfig(), "GetConfig should return the injected DatabaseConfig")
	assert.Equal(t, ctx, exec.GetContext(), "GetContext should return the injected context")
}

package fault

import (
	// "encoding/json"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"

	authorizer "synapse-its.com/shared/api/authorizer"
	response "synapse-its.com/shared/api/response"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/devices"
	edihelper "synapse-its.com/shared/devices/edi/helper"
	logger "synapse-its.com/shared/logger"
	"synapse-its.com/shared/schemas"
)

// TODO: Inject depenencies instead of saving functions on the package level, to allow for t.Parallel
var (
	userInfoFromContext  = authorizer.UserInfoFromContext
	deviceProcessRmsData = devices.ProcessRmsData
	jsonMarshalIndentFn  = json.MarshalIndent
	jsonCleanFn          = ReplaceNullsInJSON
)

func Handler(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	// Get user info from jwt authorizer
	userInfo, ok := userInfoFromContext(ctx)
	if !ok {
		logger.Error("Unable to retrieve user info from request context")
		response.CreateInternalErrorResponse(w)
		return
	}

	deviceId, err := parseRequest(r)
	if err != nil {
		logger.Infof("Unable to parse API request: %v", err)
		response.CreateUnauthorizedResponse(w)
		return
	}

	connections, err := connect.GetConnections(ctx)
	if err != nil {
		logger.Errorf("%v", err)
		response.CreateInternalErrorResponse(w)
		return
	}

	pgDeviceDetail, err := getPGDeviceDetail(connections.Postgres, userInfo.ID, deviceId)
	if err != nil {
		logger.Errorf("%v", err)
		response.CreateInternalErrorResponse(w)
		return
	}

	allLogs, err := getBQLogs(connections.Bigquery, pgDeviceDetail.DeviceID, pgDeviceDetail.SoftWareGateWayID, pgDeviceDetail.OrganizationID)
	if err != nil {
		logger.Errorf("%v", err)
		response.CreateInternalErrorResponse(w)
		return
	}

	schema := getSchema(allLogs.FaultLogs.Model)
	var unified any
	switch schema {
	case "fault_mmu2_16leip.proto":
		deviceDetail := Logs_16leip_DeviceDetail{
			Model:                    allLogs.FaultLogs.DeviceModel,
			MonitorID:                pgDeviceDetail.MonitorID,
			MonitorName:              pgDeviceDetail.MonitorName,
			FirmwareType:             strconv.FormatInt(allLogs.FaultLogs.FirmwareReVision, 10),
			FirmwareVersion:          strconv.FormatInt(allLogs.FaultLogs.FirmwareVersion, 10),
			MonitorCommVersion:       strconv.FormatInt(allLogs.FaultLogs.MonitorCommVersion, 10),
			RMSEngineFirmwareType:    strconv.FormatInt(pgDeviceDetail.RMSEngineFirmwareType, 10),
			RMSEngineFirmwareVersion: strconv.FormatInt(pgDeviceDetail.RMSEngineFirmwareVersion, 10),
			Logic24FirmwareType:      "",
			Logic24FirmwareVersion:   "",
			Manufacturer:             "EDI",
		}
		unified = ConvertSchemasTo16leip(
			allLogs.FaultLogs.PubsubTimestamp,
			schema,
			pgDeviceDetail.DeviceID,
			deviceDetail,
			allLogs.LogMonitorReset,
			allLogs.LogPreviousFail,
			allLogs.LogConfiguration,
			allLogs.LogACLineEvent,
			allLogs.LogFaultSignalSequence,
		)
	case "logs_edi_ecl2010_fplus.proto":
		deviceDetail := &Logs_ECL2010_FPlus_LogData_DeviceDetail{
			Model:                    allLogs.FaultLogs.DeviceModel,
			MonitorId:                pgDeviceDetail.MonitorID,
			MonitorName:              pgDeviceDetail.MonitorName,
			FirmwareType:             strconv.FormatInt(allLogs.FaultLogs.FirmwareReVision, 10),
			FirmwareVersion:          strconv.FormatInt(allLogs.FaultLogs.FirmwareVersion, 10),
			MonitorCommVersion:       strconv.FormatInt(allLogs.FaultLogs.MonitorCommVersion, 10),
			RmsEngineFirmwareType:    strconv.FormatInt(pgDeviceDetail.RMSEngineFirmwareType, 10),
			RmSEngineFirmwareVersion: strconv.FormatInt(pgDeviceDetail.RMSEngineFirmwareVersion, 10),
			Logic24FirmwareType:      "",
			Logic24FirmwareVersion:   "",
			Manufacturer:             "EDI",
		}

		unified = ConvertSchemasToECL2010FPlus(
			allLogs.FaultLogs.PubsubTimestamp,
			schema,
			pgDeviceDetail.DeviceID,
			deviceDetail,
			allLogs.LogMonitorReset,
			allLogs.LogPreviousFail,
			allLogs.LogConfiguration,
			allLogs.LogACLineEvent,
			allLogs.LogFaultSignalSequence,
		)
	case "logs_edi_cmu2212_base.proto":
		deviceDetail := &Logs_CMU2212_Base_LogData_DeviceDetail{
			Model:                    allLogs.FaultLogs.DeviceModel,
			MonitorId:                pgDeviceDetail.MonitorID,
			MonitorName:              pgDeviceDetail.MonitorName,
			FirmwareType:             strconv.FormatInt(allLogs.FaultLogs.FirmwareReVision, 10),
			FirmwareVersion:          strconv.FormatInt(allLogs.FaultLogs.FirmwareVersion, 10),
			MonitorCommVersion:       strconv.FormatInt(allLogs.FaultLogs.MonitorCommVersion, 10),
			RmsEngineFirmwareType:    strconv.FormatInt(pgDeviceDetail.RMSEngineFirmwareType, 10),
			RmSEngineFirmwareVersion: strconv.FormatInt(pgDeviceDetail.RMSEngineFirmwareVersion, 10),
			Logic24FirmwareType:      "",
			Logic24FirmwareVersion:   "",
			Manufacturer:             "EDI",
		}

		unified = ConvertSchemasToCMU2212Base(
			allLogs.FaultLogs.PubsubTimestamp,
			schema,
			pgDeviceDetail.DeviceID,
			deviceDetail,
			allLogs.LogMonitorReset,
			allLogs.LogPreviousFail,
			allLogs.LogConfiguration,
			allLogs.LogACLineEvent,
			allLogs.LogFaultSignalSequence,
		)
	}

	// 1) Marshal struct to indented JSON
	raw, err := jsonMarshalIndentFn(unified, "", "  ")
	if err != nil {
		http.Error(w, "failed to marshal unified log", http.StatusInternalServerError)
		return
	}

	// 2) Replace nulls with [] via helper
	clean, err := jsonCleanFn(raw)
	if err != nil {
		http.Error(w, "failed to clean JSON", http.StatusInternalServerError)
		return
	}

	// 3) Wrap the cleaned JSON in a RawMessage so CreateSuccessResponse will inline it
	var data json.RawMessage = clean

	// 3) Send the cleaned JSON as success response payload
	response.CreateSuccessResponse(data, w)
}

var getPGDeviceDetail = func(pg connect.DatabaseExecutor, userID int64, deviceId int) (*PGDeviceDetail, error) {
	// TODO: Need to fetch the date and loguuid from a postgress table DeviceLog to be used in the bigquery query
	query := `SELECT
  d.DeviceIdentifier            AS DeviceID,
	sg.softwaregatewayidentifier  AS SoftWareGateWayID,
  COALESCE(dm.MonitorId,0)      AS MonitorID,
  COALESCE(dm.MonitorName,'')   AS MonitorName,
  COALESCE(er.EngineVersion,0)  AS RMSEngineFirmwareType,
  COALESCE(er.EngineRevision,0) AS RMSEngineFirmwareVersion,
	o.organizationidentifier      AS OrganizationID

  FROM {{Device}} d
  JOIN {{UserDevice}} ud 
    ON ud.DeviceId = d.Id
    AND ud.UserId   = $1

  LEFT JOIN {{DeviceMonitorName}} dm 
    ON dm.DeviceIdentifier = d.DeviceIdentifier

  LEFT JOIN {{DeviceRMSEngine}} er 
    ON er.DeviceIdentifier = d.DeviceIdentifier
	
	LEFT JOIN {{SoftwareGateway}} sg
	  ON sg.id = d.softwaregatewayid

	LEFT JOIN {{Organization}} o
		ON o.id = sg.organizationid

  WHERE d.Id = $2;`
	pgDeviceDetail := &PGDeviceDetail{}
	err := pg.QueryRowStruct(pgDeviceDetail, query, userID, deviceId)
	if err != nil {
		return nil, err
	}

	return pgDeviceDetail, nil
}

var getBQLogs = func(bq connect.BigQueryExecutorInterface, deviceID string, softwaregatewayID string, orgID string, filterDays ...int) (schemas.AllLogs, error) {
	// TODO: Need to fetch the date and loguuid from a postgress and use those as filters in the fault log table
	// Remove filterDays and days variables
	days := 90
	if len(filterDays) > 0 {
		days = filterDays[0]
	}

	query := `
	WITH fault_logs AS (
		SELECT *
		FROM {{FaultLogs}}
		WHERE
			pubsubtimestamp >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL $1 DAY)
			AND organizationidentifier  = $2
			AND softwaregatewayid       = $3
			AND deviceid                = $4
	)

	SELECT
		(SELECT AS STRUCT fl.* EXCEPT(loguuid,rawlogmessages),
		COALESCE(mr.devicemodel,pf.devicemodel,ae.devicemodel,fs.devicemodel,cc.devicemodel) as devicemodel,
		COALESCE(mr.header.monitorid,pf.header.monitorid,ae.header.monitorid,fs.header.monitorid,cc.header.monitorid) as monitorid,
		COALESCE(mr.header.firmwareversion,pf.header.firmwareversion,ae.header.firmwareversion,fs.header.firmwareversion,cc.header.firmwareversion) as firmwareversion,
		COALESCE(mr.header.firmwarerevision,pf.header.firmwarerevision,ae.header.firmwarerevision,fs.header.firmwarerevision,cc.header.firmwarerevision) as firmwarerevision,
		COALESCE(mr.header.commversion,pf.header.commversion,ae.header.commversion,fs.header.commversion,cc.header.commversion) as commversion,
		COALESCE(mr.header.model,pf.header.model,ae.header.model,fs.header.model,cc.header.model) as model,   
		) AS fault_logs,

		(
			SELECT AS STRUCT
				mr.header as header,
				mr.records as records,
				coalesce(mr.devicemodel, "") AS devicemodel,
		) AS monitor_reset,

		(
			SELECT AS STRUCT
				pf.header as header,
				pf.records as records,
				coalesce(pf.devicemodel, "") AS devicemodel,
		) AS previous_fail,

		(
			SELECT AS STRUCT
				ae.header as header,
				ae.record as record,
				coalesce(ae.devicemodel, "") AS devicemodel,
		) AS ac_line_event,

		(
			SELECT AS STRUCT
				fs.header AS header,
				fs.records AS records,
				coalesce(fs.devicemodel, "") AS devicemodel,
		) AS fault_signal_sequence,

		(
			SELECT AS STRUCT
				cc.header AS header,
				cc.record AS record,
				coalesce(cc.devicemodel, "") AS devicemodel,
		) AS configuration_change

	FROM fault_logs AS fl

	LEFT JOIN {{LogMonitorReset}} mr
		ON mr.loguuid            = fl.loguuid
	AND mr.pubsubtimestamp    = fl.pubsubtimestamp
	AND mr.organizationidentifier = fl.organizationidentifier
	AND mr.softwaregatewayid      = fl.softwaregatewayid
	AND mr.deviceid               = fl.deviceid

	LEFT JOIN {{logPreviousFail}} pf
		ON pf.loguuid            = fl.loguuid
	AND pf.pubsubtimestamp    = fl.pubsubtimestamp
	AND pf.organizationidentifier = fl.organizationidentifier
	AND pf.softwaregatewayid      = fl.softwaregatewayid
	AND pf.deviceid               = fl.deviceid

	LEFT JOIN {{logACLineEvent}} ae
		ON ae.loguuid            = fl.loguuid
	AND ae.pubsubtimestamp    = fl.pubsubtimestamp
	AND ae.organizationidentifier = fl.organizationidentifier
	AND ae.softwaregatewayid      = fl.softwaregatewayid
	AND ae.deviceid               = fl.deviceid

	LEFT JOIN {{logFaultSignalSequence}} fs
		ON fs.loguuid            = fl.loguuid
	AND fs.pubsubtimestamp    = fl.pubsubtimestamp
	AND fs.organizationidentifier = fl.organizationidentifier
	AND fs.softwaregatewayid      = fl.softwaregatewayid
	AND fs.deviceid               = fl.deviceid

	LEFT JOIN {{logConfiguration}} cc
		ON cc.loguuid            = fl.loguuid
	AND cc.pubsubtimestamp    = fl.pubsubtimestamp
	AND cc.organizationidentifier = fl.organizationidentifier
	AND cc.softwaregatewayid      = fl.softwaregatewayid
	AND cc.deviceid               = fl.deviceid
	;`

	allLogs := schemas.AllLogs{}
	err := bq.QueryRowStruct(&allLogs, query, days, orgID, softwaregatewayID, deviceID)
	if err != nil {
		logger.Errorf("%v", err)
	}
	return allLogs, nil
}

var parseRequest = func(r *http.Request) (deviceId int, err error) {
	rQuery := r.URL.Query()
	deviceIdString := rQuery.Get("deviceid")
	if deviceIdString == "" {
		return deviceId, fmt.Errorf("%w: %v", ErrInvalidUrlQuery, rQuery)
	}
	deviceId, err = strconv.Atoi(deviceIdString)
	if err != nil {
		logger.Infof("unable to convert query string parameter deviceid to int: %v", err)
		return deviceId, fmt.Errorf("%w: %v", ErrConvertQueryParam, err)
	}

	return deviceId, nil
}

var getSchema = func(model int64) string {
	switch edihelper.MonitorModel(model) {
	case edihelper.Ecl2010:
		return "logs_edi_ecl2010_fplus.proto"
	case edihelper.CMUip2212_hv:
		return "logs_edi_cmu2212_base.proto"
	case edihelper.Mmu16le:
		return "fault_mmu2_16leip.proto"
	default:
		// return "Unknow Device"
		return "fault_mmu2_16leip.proto"
	}
}

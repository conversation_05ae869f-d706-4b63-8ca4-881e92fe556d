package middleware

import (
	"net/http"

	"synapse-its.com/shared/bqbatch"
)

// Return a handler that sets the Connections as provided in the arguments.
func BQBatchMiddleware(batch bqbatch.Batcher) func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			ctx := bqbatch.WithBatch(r.Context(), batch)
			next.ServeHTTP(w, r.WithContext(ctx))
		})
	}
}

package end2end

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"testing"
	"time"

	Utils "synapse-its.com/testing/utils"
)

func TestUserAuthenticate(t *testing.T) {
	// Build a context that respects the test’s timeout, if any.
	ctx := context.Background()
	if dl, ok := t.Deadline(); ok {
		var cancel context.CancelFunc
		ctx, cancel = context.WithDeadline(ctx, dl)
		defer cancel()
	}

	// Wait for broker to be ready once.
	Utils.AwaitBroker(ctx, time.Second)

	client := &http.Client{Timeout: 5 * time.Second}
	url := "http://broker:8080/api/v3/user/authenticate"

	tests := []struct {
		name       string
		payload    map[string]string
		wantStatus int
	}{
		{
			name: "Successful Authentication",
			payload: map[string]string{
				"username": "<EMAIL>",
				"password": "puppies1234",
			},
			wantStatus: http.StatusOK,
		},
		{
			name: "Wrong Password",
			payload: map[string]string{
				"username": "<EMAIL>",
				"password": "puppies123",
			},
			wantStatus: http.StatusUnauthorized,
		},
		{
			name: "Wrong Username",
			payload: map[string]string{
				"username": "<EMAIL>",
				"password": "puppies1234",
			},
			wantStatus: http.StatusUnauthorized,
		},
		{
			name: "Missing Password",
			payload: map[string]string{
				"username": "<EMAIL>",
				"password": "",
			},
			wantStatus: http.StatusUnauthorized,
		},
		{
			name: "Missing Username",
			payload: map[string]string{
				"username": "",
				"password": "puppies1234",
			},
			wantStatus: http.StatusUnauthorized,
		},
		{
			name: "Empty Username and Password",
			payload: map[string]string{
				"username": "",
				"password": "",
			},
			wantStatus: http.StatusUnauthorized,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			bodyBytes, err := json.Marshal(tc.payload)
			if err != nil {
				t.Fatalf("failed to marshal JSON payload: %v", err)
			}

			req, err := http.NewRequestWithContext(ctx, http.MethodPost, url, bytes.NewReader(bodyBytes))
			if err != nil {
				t.Fatalf("failed to create request: %v", err)
			}
			req.Header.Set("Content-Type", "application/json")

			resp, err := client.Do(req)
			if err != nil {
				t.Fatalf("HTTP request failed: %v", err)
			}
			defer resp.Body.Close()

			if resp.StatusCode != tc.wantStatus {
				t.Fatalf("expected status %d, got %d", tc.wantStatus, resp.StatusCode)
			}
		})
	}
}

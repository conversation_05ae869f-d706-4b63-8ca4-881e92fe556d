package schema_mgmt

import (
	"database/sql"
	"errors"
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/Masterminds/semver"

	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/logger"
)

// This is the location of the schema directory, relative to root.
const SCHEMA_DIR = "schemas"

// Represents the "largest" schema version.
const SCHEMA_VERSION_MAX = "999.999.999"

// Migration represents a single migration step
type Migration struct {
	Sequence float64
	FileName string
	MD5Hash  string
}

// PostgresExecutor handles PostgreSQL migrations
type PostgresMigrationExecutor struct {
	Client *connect.PostgresExecutor
}

// BigQueryExecutor handles BigQuery migrations
type BigQueryMigrationExecutor struct {
	Client *connect.BigQueryExecutor
}

// SchemaMigrationExecutor abstracts different database types
type SchemaMigrationExecutor interface {
	EnsureSchemaMigrationsTable() error
	QueryGeneric(query string, args ...interface{}) ([]map[string]interface{}, error)
	Exec(query string, args ...interface{}) (sql.Result, error)
	ExecMultiple(query string) error
	QueryRow(query string, args ...interface{}) (map[string]interface{}, error)
	Config() connect.DatabaseConfig
}

// Get the most recent schema version.
func GetMostRecentSchema(schemaName string) (string, error) {
	versionDirs, err := getSchemaPaths(schemaName)
	if err != nil {
		return "", err
	}
	if len(versionDirs) == 0 {
		return "", fmt.Errorf("no schema directories found for schema %s", schemaName)
	}
	return filepath.Base(versionDirs[len(versionDirs)-1]), nil
}

type migration_state int

const (
	VALIDATE migration_state = iota
	APPLY
)

var (
	// Mockable functions for testing
	ApplyMigrations_absJoin1  func(path ...string) (string, error)   = absJoin
	ApplyMigrations_absJoin1a func(path ...string) (string, error)   = absJoin
	ApplyMigrations_absJoin2  func(path ...string) (string, error)   = absJoin
	ApplyMigrations_absJoin3  func(path ...string) (string, error)   = absJoin
	ApplyMigrations_absJoin4  func(path ...string) (string, error)   = absJoin
	ApplyMigrations_os_Stat0  func(path string) (os.FileInfo, error) = os.Stat
	ApplyMigrations_os_Stat1  func(path string) (os.FileInfo, error) = os.Stat
	ApplyMigrations_os_Stat2  func(path string) (os.FileInfo, error) = os.Stat
)

// ApplyMigrations applies the necessary migrations with namespace and version
// tracking support. Assuming all goes well, the database will be at the most
// recent version of the target version.
func ApplyMigrations(executor SchemaMigrationExecutor, schemaName string, desiredSchemaVersion string) error {
	// Helper variable declaration.
	var ok bool

	// Set up variables to be used in this function.
	config := executor.Config()

	// If the `desiredSchemaVersion` is empty, then default to an absurdely high
	// number, which should result in the latest schema being applied.
	if desiredSchemaVersion == "" {
		desiredSchemaVersion = SCHEMA_VERSION_MAX
	}

	// A schema migration table must exist before anything else can be done.
	logger.Infof("Starting migration process for schema %s to version %s", schemaName, desiredSchemaVersion)
	if err := executor.EnsureSchemaMigrationsTable(); err != nil {
		return fmt.Errorf("%w: %v", ErrSchemaTable, err)
	}

	// Get a list of all potential schema versions that are less than or equal to
	// the target version.
	logger.Debug("Getting list of version directories.")
	versionDirs, err := listVersionDirectories(schemaName, desiredSchemaVersion)
	if err != nil || len(versionDirs) == 0 {
		return fmt.Errorf("%w: %v", ErrVersionDirs, err)
	}

	actualSchemaVersion := filepath.Base(versionDirs[len(versionDirs)-1])

	// Ask the database which version of the schema it was created at.
	row, err := executor.QueryRow(SQLGetDatabaseSchemaVersionCreatedAt)
	creationSchemaVersion := ""
	if err != nil {
		if !errors.Is(err, sql.ErrNoRows) {
			return fmt.Errorf("%w:  %v", ErrSchemaRetrieval, err)
		}
	} else {
		// Verify that the version is a string type.
		creationSchemaVersion, ok = row["schema_version"].(string)
		if !ok {
			return fmt.Errorf("%w: %T", ErrSchemaRetrievalType, row["schema_version"])
		}
	}

	// If this is a fresh install, then populate accordingly.
	if creationSchemaVersion == "" {
		// The first record will be a "default" message indicating the creation of
		// the schema at the target version.
		_, err = executor.Exec(SQLInsertSchemaMigrationsCreationRecord, actualSchemaVersion, "SCHEMA_CREATION SCHEMA:"+schemaName+" VERSION_DESIRED:"+desiredSchemaVersion+" ENVIRONMENT:"+config.Environment, time.Now().UTC())
		if err != nil {
			return fmt.Errorf("%w: %v", ErrSchemaCreationRecord, err)
		}

		// Now, create the schema in it's up-to-date form using the DDL.
		// File should be in: /<SCHEMA_DIR>/<schema_name>/<schema_version>/DDL/schema.sql
		var schemaFile string
		schemaFile, err = ApplyMigrations_absJoin1(SCHEMA_DIR, schemaName, actualSchemaVersion, "DDL", "schema.sql")
		if err != nil {
			return fmt.Errorf("%w: %v", ErrSchemaAbsJoin, err)
		}
		logger.Debugf("Executing schema creation from %s", schemaFile)
		if err = executeSQLFile(executor, schemaFile); err != nil {
			return fmt.Errorf("%w: %v", ErrSchemaCreation, err)
		}

		// Execute any environment-specific DDL.  It is not required to exist.
		// File should be in: /<SCHEMA_DIR>/<schema_name>/<schema_version>/DDL/<environment>.sql
		schemaFile, err = ApplyMigrations_absJoin1a(SCHEMA_DIR, schemaName, actualSchemaVersion, "DDL", fmt.Sprintf("%s.sql", config.Environment))
		if err != nil {
			return fmt.Errorf("%w: %v", ErrEnvironmentSchemaAbsJoin, err)
		}
		if _, err = ApplyMigrations_os_Stat0(schemaFile); err == nil {
			logger.Debugf("Executing environment-specific schema creation from %s", schemaFile)
			if err = executeSQLFile(executor, schemaFile); err != nil {
				return fmt.Errorf("%w: %v", ErrEnvironmentSchemaCreation, err)
			}
		}

		// Now, execute the DML for the schema version.
		// File should be in: /<SCHEMA_DIR>/<schema_name>/<schema_version>/DML/data.sql)
		var file string
		file, err = ApplyMigrations_absJoin2(SCHEMA_DIR, schemaName, actualSchemaVersion, "DML", "data.sql")
		if err != nil {
			return fmt.Errorf("%w: %v", ErrAbsPathDML, err)
		}
		if _, err = ApplyMigrations_os_Stat1(file); err == nil {
			logger.Debugf("Executing DML from %s", file)
			if err = executeSQLFile(executor, file); err != nil {
				return fmt.Errorf("%w: %v", ErrDMLExecute, err)
			}
		} else {
			return fmt.Errorf("%w: %v", ErrDMLRead, err)
		}

		// Execute the DML for the environment (not required to exist).
		// File should be in: /<SCHEMA_DIR>/<schema_name>/<schema_version>/DML/<environment>.sql)
		file, err = ApplyMigrations_absJoin3(SCHEMA_DIR, schemaName, actualSchemaVersion, "DML", fmt.Sprintf("%s.sql", config.Environment))
		if err != nil {
			return fmt.Errorf("%w: %v", ErrDMLAbsJoin, err)
		}
		if _, err = ApplyMigrations_os_Stat2(file); err == nil {
			logger.Debugf("Executing DML from %s", file)
			if err = executeSQLFile(executor, file); err != nil {
				return fmt.Errorf("%w: %v", ErrEnvironmentDMLExecute, err)
			}
		}

		// Lastly, we need to record that all existing migrations have already been
		// applied.
		// Get the sequence files for this version.
		sequenceFiles, sequenceErr := getSequenceFiles(schemaName, actualSchemaVersion, config.Environment)
		if sequenceErr != nil {
			return fmt.Errorf("%w: %v", ErrGetSequenceFilesFailed, sequenceErr)
		}

		// Parse the sequence files to get the list of updates defined.
		updatesDefined, parseErr := parseSequenceFiles(sequenceFiles)
		if parseErr != nil {
			return fmt.Errorf("%w: %v", ErrParseSequenceFiles, parseErr)
		}

		// Insert the migrations into the schema_migrations table.
		err = insertMigrations(executor, actualSchemaVersion, updatesDefined)
		if err != nil {
			return fmt.Errorf("%w: %v", ErrInsertMigrations, err)
		}

		// Success.
		logger.Infof("Migration process completed for schema %s.  New schema created at version %s", schemaName, actualSchemaVersion)
		return nil
	}

	// This is *not* a fresh install, so we need to apply the updates for the
	// schema and environment, in a deterministic order.

	// `creationSchemaVersion`` has already been retreived from the database and
	// is a string.  Make sure that it is a valid semantic version.
	semverCreationVersion, err := semver.NewVersion(creationSchemaVersion)
	if err != nil {
		return fmt.Errorf("%w (%s): %v", ErrSchemaVersion, creationSchemaVersion, err)
	}

	// Now, let's loop through the version directories, which is already
	// filtered to remove schemas that are beyond the desired version.
	// Skip any that are older than the `creationSchemaVersion`.
	state := VALIDATE
	for _, versionDir := range versionDirs {
		// versionDirs has already been filtered to remove any directories that are
		// not valid semantic versions, so we can safely use the directory name
		// directly.
		vDir := filepath.Base(versionDir)
		vDirSchema, _ := semver.NewVersion(vDir)

		if vDirSchema.LessThan(semverCreationVersion) {
			// NOTE: Test coverage
			// This cannot be easily tested until there is more than one version of
			// the schema in the repository.
			// It will be covered by integration tests.
			continue
		}

		// Get all applied migrations.
		appliedMigrations, err := executor.QueryGeneric(SQLGetMigrations, vDir)
		if err != nil {
			if !errors.Is(err, sql.ErrNoRows) {
				return fmt.Errorf("%w: %v", ErrGetMigrations, err)
			}
		}

		// Get the sequence files for this version.
		sequenceFiles, sequenceErr := getSequenceFiles(schemaName, vDir, config.Environment)
		if sequenceErr != nil {
			return fmt.Errorf("%w: %v", ErrGetSequenceFilesFailed, sequenceErr)
		}

		// Parse the sequence files to get the list of updates defined.
		updatesDefined, err := parseSequenceFiles(sequenceFiles)
		if err != nil {
			return fmt.Errorf("%w: %v", ErrParseSequenceFiles, err)
		}

		// Loop through the migrations and check/validate existing entries, and
		// once all checks have been completed, apply the remaining.
		totalAppliedMigrations := len(appliedMigrations)
		totalUpdatesDefined := len(updatesDefined)
		if totalAppliedMigrations > totalUpdatesDefined {
			return fmt.Errorf("%w (%d > %d)", ErrTooManyMigrations, totalAppliedMigrations, totalUpdatesDefined)
		}
		for i := 0; i < totalUpdatesDefined; i++ {
			if state == VALIDATE {
				if i == totalAppliedMigrations {
					// Back up i by one and re-run the loop iteration.
					i -= 1
					state = APPLY
					continue
				}
				// This is a normal check.  Just make sure that the sequence value,
				// the name of the file, and the MD5 matches.
				applied := appliedMigrations[i]
				update := updatesDefined[i]
				uSequence, ok1 := applied["sequence"].(float64)
				uFileName, ok2 := applied["update_file"].(string)
				uMD5Hash, ok3 := applied["md5_hash"].(string)
				if !ok1 || !ok2 || !ok3 {
					return fmt.Errorf("%w: %v", ErrGetMigrations, applied)
				}
				if uSequence != update.Sequence ||
					uFileName != update.FileName ||
					uMD5Hash != update.MD5Hash {

					return fmt.Errorf("%w. [%s] (%g, %s, %s) != (%g, %s, %s)",
						ErrMigrationSequenceMismatch, vDir, uSequence, uFileName, uMD5Hash, update.Sequence, update.FileName, update.MD5Hash)
				}

				// Migration has already been applied successfully.
				logger.Debugf("Successfully verified migration: [%s] (%g, %s, %s)", vDir, update.Sequence, update.FileName, update.MD5Hash)
			} else {
				// The only other state is APPLY.
				// Apply the update.
				update := updatesDefined[i]
				path, err := ApplyMigrations_absJoin4(SCHEMA_DIR, schemaName, vDir, "updates", update.FileName)
				if err != nil {
					return fmt.Errorf("%w: %v", ErrAbsPathMigrationFile, err)
				}
				if err = executeSQLFile(executor, path); err != nil {
					return fmt.Errorf("%w (%s): %v", ErrApplyMigration, update.FileName, err)
				}

				// Add the Migration record to the database.
				_, err = executor.Exec(SQLInsertSchemaMigrations, vDir, update.Sequence, update.FileName, update.MD5Hash, time.Now().UTC())
				if err != nil {
					return fmt.Errorf("%w (%s): %v", ErrInsertMigration, update.FileName, err)
				}

				// Single update has applied
				logger.Debugf("Successfully applied migration: [%s] (%g, %s, %s)", vDir, update.Sequence, update.FileName, update.MD5Hash)
			}
		}

		// Successfully verified/applied all Migrations in this schema version.
		logger.Debugf("Successfully applied all migrations for schema version: %s", vDir)
	}

	logger.Info("Migration process completed.")
	return nil
}

/*******************************************
 * Utility Functions
 *******************************************/

// Mockable functions for testing
var absJoin_filepath_Abs func(path string) (string, error) = filepath.Abs

// Join strings together to form an absolute path.
// The default function (`filepath.Join`) produces a relative path, and because
// there is the possibility of an `err` variable, it's not easy to chain them
// together on the same line.  This is just to streamline the use of the
// function in this file.
func absJoin(elem ...string) (string, error) {
	absPath, err := absJoin_filepath_Abs(filepath.Join("/", filepath.Join(elem...)))
	if err != nil {
		return "", fmt.Errorf("%w, %v", ErrAbsJoin, err)
	}
	return absPath, nil
}

// retry executes a function with retry logic for transient failures
// NOTE: Because this function is not used or tested, it is commented out for
// the time being.
// func retry(attempts int, sleep time.Duration, fn func() error) error {
// 	for i := 0; i < attempts; i++ {
// 		err := fn()
// 		if err == nil {
// 			return nil
// 		}
// 		if i < attempts-1 {
// 			logger.Debugf("Retrying after error: %v", err)
// 			time.Sleep(sleep + time.Duration(rand.Intn(100))*time.Millisecond)
// 		}
// 	}
// 	return errors.New("operation failed after maximum retries")
// }

var (
	// Mockable functions for testing
	getSchemaPaths_absJoin1   func(path ...string) (string, error)     = absJoin
	getSchemaPaths_absJoin2   func(path ...string) (string, error)     = absJoin
	getSchemaPaths_os_ReadDir func(path string) ([]os.DirEntry, error) = os.ReadDir
)

// getSchemaPaths returns a list of all version directories for a given schema
// sorted in ascending order. It filters out any directories that are not
// valid semantic versions.
// It also handles errors related to file system operations.
func getSchemaPaths(schemaName string) ([]string, error) {
	baseDir, err := getSchemaPaths_absJoin1(SCHEMA_DIR, schemaName)
	if err != nil {
		return nil, fmt.Errorf("%w: %v", ErrSchemaPathAbsSchema, err)
	}

	entries, err := getSchemaPaths_os_ReadDir(baseDir)
	if err != nil {
		return nil, err
	}

	var versionDirs []string
	for _, e := range entries {
		if !e.IsDir() {
			continue
		}
		name := e.Name()
		if _, err := semver.NewVersion(name); err != nil {
			continue
		}
		// build the absolute path to each version subdir
		dirPath, err := getSchemaPaths_absJoin2(SCHEMA_DIR, schemaName, name)
		if err != nil {
			return nil, fmt.Errorf("%w: %v", ErrSchemaPathAbsVersion, err)
		}
		versionDirs = append(versionDirs, dirPath)
	}

	// sort by semantic version
	sort.Slice(versionDirs, func(i, j int) bool {
		v1, _ := semver.NewVersion(filepath.Base(versionDirs[i]))
		v2, _ := semver.NewVersion(filepath.Base(versionDirs[j]))
		return v1.LessThan(v2)
	})

	return versionDirs, nil
}

var (
	// Mockable functions for testing
	getSequenceFiles_absJoin1 func(path ...string) (string, error) = absJoin
	getSequenceFiles_absJoin2 func(path ...string) (string, error) = absJoin
)

// Helper function to generate the list of sequence files.
func getSequenceFiles(schemaName string, version string, environment string) ([]string, error) {
	// File should be in:
	//   /<SCHEMA_DIR>/<schema_name>/<schema_version>/updates/sequence.txt
	//   /<SCHEMA_DIR>/<schema_name>/<schema_version>/updates/sequence.<environment>.txt
	s1, err1 := getSequenceFiles_absJoin1(SCHEMA_DIR, schemaName, version, "updates", "sequence.txt")
	if err1 != nil {
		return nil, fmt.Errorf("%w: %v", ErrGetSequenceFiles, err1)
	}
	s2, err2 := getSequenceFiles_absJoin2(SCHEMA_DIR, schemaName, version, "updates", fmt.Sprintf("sequence.%s.txt", environment))
	if err2 != nil {
		return nil, fmt.Errorf("%w: %v", ErrGetSequenceFilesEnv, err2)
	}
	return []string{s1, s2}, nil
}

// Helper function: List all version directories, sorted ascending.
// Do not include any directories whose version is greater than the
// target version.
func listVersionDirectories(schemaName string, desiredSchemaVersion string) ([]string, error) {
	// Parse the desiredVer.
	desiredVer, err := semver.NewVersion(desiredSchemaVersion)
	if err != nil {
		return nil, fmt.Errorf("%w (%q): %v", ErrListVersionDirsInvalidSchema, desiredVer, err)
	}

	allVersions, err := getSchemaPaths(schemaName)
	if err != nil {
		return nil, err
	}

	// Filter out files and unwanted directories.
	var versionDirs []string
	for _, dirName := range allVersions {
		// Attempt to parse the directory name as a semantic version.
		// We are ignoring errors because getSchemaPaths() already filters out
		// invalid directories, and has complete test coverage.
		v, _ := semver.NewVersion(filepath.Base(dirName))
		// Only include directories whose version is less than or equal to desiredVer.
		if !v.GreaterThan(desiredVer) {
			versionDirs = append(versionDirs, dirName)
		}
	}

	// The directories are already present in ascending order. No need to sort.
	return versionDirs, nil
}

var (
	// Mockable functions for testing
	parseSequenceFiles_os_ReadFile1 = os.ReadFile
	parseSequenceFiles_os_ReadFile2 = os.ReadFile
)

// parseSequenceFiles reads and sorts migrations from sequence.txt and
// environment-specific sequence files.
// It expects each non-empty line to contain two fields:
//  1. A sequence number (float)
//  2. A file name (e.g., foo.sql)
//
// For each migration, it loads the MD5 hash from the sibling file (e.g.,
// foo.md5).
func parseSequenceFiles(sequenceFiles []string) ([]Migration, error) {
	var migrations []Migration

	for _, seqFile := range sequenceFiles {
		content, err := parseSequenceFiles_os_ReadFile1(seqFile)
		if err != nil {
			// Files are not required.
			continue
		}

		lines := strings.Split(string(content), "\n")
		for _, line := range lines {
			parts := strings.Fields(line)
			if len(parts) == 0 {
				// Ignore empty lines.
				continue
			}
			// Expect exactly 2 parts: sequence and file name.
			if len(parts) != 2 {
				return nil, fmt.Errorf("%w (%s): '%s'", ErrParseSequenceFilesInvalidLine, seqFile, line)
			}

			var sequence float64
			sequence, err := strconv.ParseFloat(parts[0], 64)
			if err != nil {
				return nil, fmt.Errorf("%w '%s': %v", ErrParseSequenceFilesInvalidNumber, line, err)
			}
			fileName := parts[1]

			// Determine the full path to the sibling .md5 file.
			// For example, if seqFile is "foo/sequence.txt" and fileName is "bar.sql",
			// then the md5 file should be "foo/bar.md5".
			siblingMD5 := filepath.Join(filepath.Dir(seqFile), strings.Replace(fileName, ".sql", ".md5", 1))

			// Load the MD5 value from the sibling file.
			md5Bytes, err := parseSequenceFiles_os_ReadFile2(siblingMD5)
			if err != nil {
				return nil, fmt.Errorf("%w (%s): %v", ErrParseSequenceFilesInvalidMD5, siblingMD5, err)
			}
			md5Hash := strings.TrimSpace(string(md5Bytes))

			migrations = append(migrations, Migration{
				Sequence: sequence,
				FileName: fileName,
				MD5Hash:  md5Hash,
			})
		}
	}

	// Sort the migrations in ascending order based on the sequence.
	sort.Slice(migrations, func(i, j int) bool {
		return migrations[i].Sequence < migrations[j].Sequence
	})
	return migrations, nil
}

// `insertMigrations()` is a helper function that will insert a row for all
// migrations which are defined in the sequence files.  This function should
// only be called when a database is created for the first time.
func insertMigrations(executor SchemaMigrationExecutor, actualSchemaVersion string, updatesDefined []Migration) error {
	if len(updatesDefined) == 0 {
		return nil
	}

	// Build a single INSERT statement of the form:
	// INSERT INTO {{schema_migrations}} (schema_version, sequence, update_file, md5_hash, applied_at)
	// VALUES ($1, $2, $3, $4, $5), ($6, $7, $8, $9, $10), ...
	var (
		placeholders []string
		values       []interface{}
	)

	// For each migration, we need five values.
	for i, m := range updatesDefined {
		// The numbering for placeholders needs to be sequential.
		offset := i * 5
		placeholders = append(placeholders, fmt.Sprintf("($%d, $%d, $%d, $%d, $%d)",
			offset+1, offset+2, offset+3, offset+4, offset+5))
		// Append the values for this migration.
		values = append(values, actualSchemaVersion, m.Sequence, m.FileName, m.MD5Hash, time.Now().UTC())
	}

	query := fmt.Sprintf(`
		INSERT INTO {{schema_migrations}}
			(schema_version, sequence, update_file, md5_hash, applied_at)
		VALUES %s
	`, strings.Join(placeholders, ", "))

	// Execute the bulk insert.
	if _, err := executor.Exec(query, values...); err != nil {
		return fmt.Errorf("%w: %v", ErrInsertMigrations, err)
	}

	logger.Debugf("Successfully inserted %d migrations", len(updatesDefined))
	return nil
}

// Mockable functions for testing
var executeSQLFile_os_ReadFile = os.ReadFile

func executeSQLFile(executor SchemaMigrationExecutor, filePath string) error {
	sqlContent, err := executeSQLFile_os_ReadFile(filePath)
	if err != nil {
		return fmt.Errorf("%w (%s): %v", ErrExecuteSQLFileFailedRead, filePath, err)
	}
	err = executor.ExecMultiple(string(sqlContent))
	return err
}

/*******************************************
 * PostgresExecutor functions.
 *******************************************/

func (p *PostgresMigrationExecutor) QueryGeneric(query string, args ...interface{}) ([]map[string]interface{}, error) {
	return p.Client.QueryGeneric(query, args...)
}

func (p *PostgresMigrationExecutor) Exec(query string, args ...interface{}) (sql.Result, error) {
	return p.Client.Exec(query, args...)
}

func (p *PostgresMigrationExecutor) ExecMultiple(query string) error {
	return p.Client.ExecMultiple(query)
}

func (p *PostgresMigrationExecutor) QueryRow(query string, args ...interface{}) (map[string]interface{}, error) {
	return p.Client.QueryRow(query, args...)
}

// EnsureSchemaMigrationsTable ensures the schema_migrations table exists
func (p *PostgresMigrationExecutor) EnsureSchemaMigrationsTable() error {
	query := `CREATE TABLE IF NOT EXISTS {{schema_migrations}} (
		schema_version TEXT NOT NULL,
		sequence DOUBLE PRECISION,
		update_file TEXT UNIQUE NOT NULL,
		md5_hash TEXT NOT NULL,
		applied_at TIMESTAMP NOT NULL
	);`
	_, err := p.Client.Exec(query)
	return err
}

// Config returns the database configuration
func (p *PostgresMigrationExecutor) Config() connect.DatabaseConfig {
	return p.Client.Config
}

/*******************************************
 * BigQueryExecutor functions.
 *******************************************/

func (bq *BigQueryMigrationExecutor) QueryGeneric(query string, args ...interface{}) ([]map[string]interface{}, error) {
	return bq.Client.QueryGeneric(query, args...)
}

func (bq *BigQueryMigrationExecutor) Exec(query string, args ...interface{}) (sql.Result, error) {
	return bq.Client.Exec(query, args...)
}

func (bq *BigQueryMigrationExecutor) ExecMultiple(query string) error {
	return bq.Client.ExecMultiple(query)
}

func (bq *BigQueryMigrationExecutor) QueryRow(query string, args ...interface{}) (map[string]interface{}, error) {
	return bq.Client.QueryRow(query, args...)
}

func (bq *BigQueryMigrationExecutor) EnsureSchemaMigrationsTable() error {
	query := `CREATE TABLE IF NOT EXISTS {{schema_migrations}} (
		schema_version STRING NOT NULL,
		sequence FLOAT64,
		update_file STRING NOT NULL,
		md5_hash STRING NOT NULL,
		applied_at TIMESTAMP NOT NULL
	);`
	_, err := bq.Client.Exec(query)
	return err
}

// Config returns the database configuration
func (bq *BigQueryMigrationExecutor) Config() connect.DatabaseConfig {
	return bq.Client.Config
}

package schema_mgmt

import "errors"

var (
	// ApplyMigrations() errors
	ErrSchemaTable               = errors.New("failed to ensure schema migrations table")
	ErrVersionDirs               = errors.New("failed to list version directories")
	ErrSchemaRetrieval           = errors.New("failed to retrieve schema version")
	ErrSchemaRetrievalType       = errors.New("failed to retrieve schema version (type mismatch)")
	ErrSchemaCreationRecord      = errors.New("failed to record schema creation version")
	ErrSchemaCreation            = errors.New("failed to execute schema creation")
	ErrSchemaAbsJoin             = errors.New("failed to get absolute path for schema file")
	ErrEnvironmentSchemaCreation = errors.New("failed to execute environment schema creation")
	ErrEnvironmentSchemaAbsJoin  = errors.New("failed to get absolute path for environment schema file")
	ErrDMLExecute                = errors.New("failed to execute DML file")
	ErrDMLRead                   = errors.New("failed to read DML file")
	ErrDMLAbsJoin                = errors.New("failed to get absolute path for DML file")
	ErrEnvironmentDMLExecute     = errors.New("failed to execute environment DML file")
	ErrGetSequenceFilesFailed    = errors.New("failed to get sequence files")
	ErrParseSequenceFiles        = errors.New("failed to parse sequence files")
	ErrInsertMigrations          = errors.New("failed to insert migrations")
	ErrSchemaVersion             = errors.New("schema version is not valid")
	ErrGetMigrations             = errors.New("failed to get migrations")
	ErrTooManyMigrations         = errors.New("number of Migrations found in database is greater than expected")
	ErrMigrationSequenceMismatch = errors.New("migration sequence verification mismatch")
	ErrAbsPathMigrationFile      = errors.New("failed to get absolute path for migration file")
	ErrApplyMigration            = errors.New("applying migration failed")
	ErrInsertMigration           = errors.New("inserting migration failed")

	// AbsJoin() errors
	ErrAbsJoin = errors.New("error converting path to absolute")

	ErrAbsPathDML           = errors.New("failed to get absolute path for DML file")
	ErrSchemaPathAbsSchema  = errors.New("failed to get absolute path for schema directory")
	ErrSchemaPathAbsVersion = errors.New("failed to get absolute path for version directory")

	// getSequenceFiles() errors
	ErrGetSequenceFiles    = errors.New("failed to get absolute path for sequence files")
	ErrGetSequenceFilesEnv = errors.New("failed to get absolute path for environment sequence files")

	ErrListVersionDirsInvalidSchema    = errors.New("invalid schema version specified")
	ErrParseSequenceFilesInvalidLine   = errors.New("invalid line in sequence file")
	ErrParseSequenceFilesInvalidNumber = errors.New("failed to parse sequence number in line")
	ErrParseSequenceFilesInvalidMD5    = errors.New("failed to read md5 file")
	ErrExecuteSQLFileFailedRead        = errors.New("failed to read migration file")
)

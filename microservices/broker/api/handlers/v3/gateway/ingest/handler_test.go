package ingest

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"cloud.google.com/go/pubsub"
	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/assert"

	connect "synapse-its.com/shared/connect"
	"synapse-its.com/shared/mocks"
	"synapse-its.com/shared/pubsubdata"
)

// an io.ReadCloser whose Read always fails
type errorReader struct{}

func (errorReader) Read(p []byte) (int, error) {
	return 0, fmt.Errorf("whoops, read failed")
}

func (errorReader) Close() error { return nil }

// errPublishResult always returns an error on Get().
type errPublishResult struct{}

func (r *errPublishResult) Get(ctx context.Context) (string, error) {
	return "", errors.New("publishing boom")
}

// errTopic implements PsTopic but its Publish returns errPublishResult.
type errTopic struct{}

func (t *errTopic) Publish(ctx context.Context, m *pubsub.Message) connect.PsPublishResult {
	return &errPublishResult{}
}

func (t *errTopic) Exists(ctx context.Context) (bool, error) {
	return true, nil
}

// errClient implements PsClient, always uses errTopic.
type errClient struct{}

func (c *errClient) Topic(name string) connect.PsTopic {
	return &errTopic{}
}

func (c *errClient) Subscription(name string) connect.PsSubscription {
	// we can reuse the FakePubsubSubscription for Receive, but not needed here
	return mocks.NewFakePubsubClient().Subscription(name)
}

func (c *errClient) CreateTopic(ctx context.Context, name string) (connect.PsTopic, error) {
	return &errTopic{}, nil
}

func (c *errClient) CreateSubscription(ctx context.Context, name string, cfg connect.SubscriptionConfig) (connect.PsSubscription, error) {
	return mocks.NewFakePubsubClient().Subscription(name), nil
}

func (c *errClient) Close() error {
	return nil
}

// Build a PsClient whose Publish->Get always errors:
type badPubResult struct{}

func (b *badPubResult) Get(ctx context.Context) (string, error) {
	return "", errors.New("pubsub down")
}

type badTopic struct{}

func (t *badTopic) Publish(ctx context.Context, m *pubsub.Message) connect.PsPublishResult {
	return &badPubResult{}
}

func (t *badTopic) Exists(ctx context.Context) (bool, error) {
	return true, nil
}

type badClient struct{}

func (c *badClient) Topic(name string) connect.PsTopic { return &badTopic{} }

func (c *badClient) Subscription(name string) connect.PsSubscription {
	conns := mocks.FakeConns()
	return conns.Pubsub.Subscription(name)
}

func (c *badClient) CreateTopic(ctx context.Context, name string) (connect.PsTopic, error) {
	return &badTopic{}, nil
}

func (c *badClient) CreateSubscription(ctx context.Context, name string, cfg connect.SubscriptionConfig) (connect.PsSubscription, error) {
	conns := mocks.FakeConns()
	return conns.Pubsub.CreateSubscription(ctx, name, cfg)
}

func (c *badClient) Close() error { return nil }

// TestHandler exercises the happy & sad paths of Handler, swapping out all external dependencies.
func TestHandler(t *testing.T) {
	// Backup globals
	origGetConns := connect.GetConnections
	origAuth := authenticateInfo
	origRedisSet := connect.RedisSet
	origIsValid := connect.IsValidPubSubTopic
	origApprovedMessageVersions := ApprovedMessageVersions
	defer func() {
		connect.GetConnections = origGetConns
		authenticateInfo = origAuth
		connect.RedisSet = origRedisSet
		connect.IsValidPubSubTopic = origIsValid
		ApprovedMessageVersions = origApprovedMessageVersions
	}()

	for _, tc := range []struct {
		name           string
		setupMocks     func()
		wantStatusCode int
		messageType    string
	}{
		{
			name: "all succeed",
			setupMocks: func() {
				conns := mocks.FakeConns()
				// 1) QueryRow returns a valid org ID
				conns.Postgres.(*mocks.FakeDBExecutor).QueryRowFunc = func(query string, args ...interface{}) (map[string]interface{}, error) {
					return map[string]interface{}{"organizationidentifier": "ORG1"}, nil
				}
				connect.GetConnections = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return conns, nil
				}
				// 2) authenticateInfo ok
				authenticateInfo = func(_ connect.DatabaseExecutor, _ *pubsubdata.HeaderDetails) (string, error) {
					return "ORG1", nil
				}
				// 3) RedisSet ok
				connect.RedisSet = func(_ context.Context, _ *redis.Client, _ string, _ string) error {
					return nil
				}
				// 4) Topic exists
				connect.IsValidPubSubTopic = func(_ string, _ context.Context, _ connect.PsClient) (bool, error) {
					return true, nil
				}
			},
			wantStatusCode: http.StatusOK,
			messageType:    "rmsData",
		},
		{
			name: "rmsData redis fails",
			setupMocks: func() {
				conns := mocks.FakeConns()
				conns.Postgres.(*mocks.FakeDBExecutor).QueryRowFunc = func(query string, args ...interface{}) (map[string]interface{}, error) {
					return map[string]interface{}{"organizationidentifier": "ORG1"}, nil
				}
				connect.GetConnections = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return conns, nil
				}
				authenticateInfo = func(_ connect.DatabaseExecutor, _ *pubsubdata.HeaderDetails) (string, error) {
					return "ORG1", nil
				}
				// RedisSet fails
				connect.RedisSet = func(_ context.Context, _ *redis.Client, _ string, _ string) error {
					return errors.New("boom")
				}
				connect.IsValidPubSubTopic = func(_ string, _ context.Context, _ connect.PsClient) (bool, error) {
					return true, nil
				}
			},
			// rmsData will receive success immediately before saving to redis
			wantStatusCode: http.StatusOK,
			messageType:    "rmsData",
		},
		{
			name: "pubsub publish fails",
			setupMocks: func() {
				// Use the real fake conns, but swap out its Pubsub client
				conns := mocks.FakeConns()
				conns.Postgres.(*mocks.FakeDBExecutor).QueryRowFunc = func(query string, args ...interface{}) (map[string]interface{}, error) {
					return map[string]interface{}{"organizationidentifier": "ORG1"}, nil
				}

				connect.GetConnections = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					conns.Pubsub = &badClient{}
					return conns, nil
				}
				authenticateInfo = func(_ connect.DatabaseExecutor, _ *pubsubdata.HeaderDetails) (string, error) {
					return "ORG1", nil
				}
				connect.RedisSet = func(_ context.Context, _ *redis.Client, _ string, _ string) error {
					return nil
				}
				connect.IsValidPubSubTopic = func(_ string, _ context.Context, _ connect.PsClient) (bool, error) {
					return true, nil
				}
			},
			// rmsData will receive success immediately before saving to pubsub
			wantStatusCode: http.StatusOK,
			messageType:    "rmsData",
		},
		{
			name: "pubsub publish fails",
			setupMocks: func() {
				// Use the real fake conns, but swap out its Pubsub client
				conns := mocks.FakeConns()
				conns.Postgres.(*mocks.FakeDBExecutor).QueryRowFunc = func(query string, args ...interface{}) (map[string]interface{}, error) {
					return map[string]interface{}{"organizationidentifier": "ORG1"}, nil
				}

				connect.GetConnections = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					conns.Pubsub = &badClient{}
					return conns, nil
				}
				authenticateInfo = func(_ connect.DatabaseExecutor, _ *pubsubdata.HeaderDetails) (string, error) {
					return "ORG1", nil
				}
				connect.RedisSet = func(_ context.Context, _ *redis.Client, _ string, _ string) error {
					return nil
				}
				connect.IsValidPubSubTopic = func(_ string, _ context.Context, _ connect.PsClient) (bool, error) {
					return true, nil
				}
			},
			// Everything other than rmsData will return error if pubsub is down
			wantStatusCode: http.StatusInternalServerError,
			messageType:    "rmsEngine",
		},
		{
			name: "fail parse request",
			setupMocks: func() {
				conns := mocks.FakeConns()
				// 1) QueryRow returns a valid org ID
				conns.Postgres.(*mocks.FakeDBExecutor).QueryRowFunc = func(query string, args ...interface{}) (map[string]interface{}, error) {
					return map[string]interface{}{"organizationidentifier": "ORG1"}, nil
				}
				connect.GetConnections = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return conns, nil
				}
				// 2) authenticateInfo ok
				authenticateInfo = func(_ connect.DatabaseExecutor, _ *pubsubdata.HeaderDetails) (string, error) {
					return "ORG1", nil
				}
				// 3) RedisSet ok
				connect.RedisSet = func(_ context.Context, _ *redis.Client, _ string, _ string) error {
					return nil
				}
				// 4) Topic exists
				connect.IsValidPubSubTopic = func(_ string, _ context.Context, _ connect.PsClient) (bool, error) {
					return true, nil
				}
			},
			wantStatusCode: http.StatusUnauthorized,
			messageType:    "badMessageType",
		},
		{
			name: "fail parse request",
			setupMocks: func() {
				conns := mocks.FakeConns()
				// 1) QueryRow returns a valid org ID
				conns.Postgres.(*mocks.FakeDBExecutor).QueryRowFunc = func(query string, args ...interface{}) (map[string]interface{}, error) {
					return map[string]interface{}{"organizationidentifier": "ORG1"}, nil
				}
				connect.GetConnections = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return nil, errors.New("No connection for you")
				}
				// 2) authenticateInfo ok
				authenticateInfo = func(_ connect.DatabaseExecutor, _ *pubsubdata.HeaderDetails) (string, error) {
					return "ORG1", nil
				}
				// 3) RedisSet ok
				connect.RedisSet = func(_ context.Context, _ *redis.Client, _ string, _ string) error {
					return nil
				}
				// 4) Topic exists
				connect.IsValidPubSubTopic = func(_ string, _ context.Context, _ connect.PsClient) (bool, error) {
					return true, nil
				}
			},
			wantStatusCode: http.StatusInternalServerError,
			messageType:    "rmsData",
		},
		{
			name: "fail authenticateInfo with unauth",
			setupMocks: func() {
				conns := mocks.FakeConns()
				// 1) QueryRow returns a valid org ID
				conns.Postgres.(*mocks.FakeDBExecutor).QueryRowFunc = func(query string, args ...interface{}) (map[string]interface{}, error) {
					return map[string]interface{}{"organizationidentifier": "ORG1"}, nil
				}
				connect.GetConnections = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return conns, nil
				}
				// 2) authenticateInfo error
				authenticateInfo = func(_ connect.DatabaseExecutor, _ *pubsubdata.HeaderDetails) (string, error) {
					return "ORG1", errors.New("boom")
				}
				// 3) RedisSet ok
				connect.RedisSet = func(_ context.Context, _ *redis.Client, _ string, _ string) error {
					return nil
				}
				// 4) Topic exists
				connect.IsValidPubSubTopic = func(_ string, _ context.Context, _ connect.PsClient) (bool, error) {
					return true, nil
				}
			},
			wantStatusCode: http.StatusUnauthorized,
			messageType:    "rmsData",
		},
		{
			name: "fail authenticateInfo with internal err",
			setupMocks: func() {
				conns := mocks.FakeConns()
				// 1) QueryRow returns a valid org ID
				connect.GetConnections = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return conns, nil
				}
				// 2) authenticateInfo ok
				authenticateInfo = func(_ connect.DatabaseExecutor, _ *pubsubdata.HeaderDetails) (string, error) {
					return "ORG1", errors.New("pow")
				}
				// 3) RedisSet ok
				connect.RedisSet = func(_ context.Context, _ *redis.Client, _ string, _ string) error {
					return nil
				}
				// 4) Topic exists
				connect.IsValidPubSubTopic = func(_ string, _ context.Context, _ connect.PsClient) (bool, error) {
					return true, nil
				}
			},
			wantStatusCode: http.StatusUnauthorized,
			messageType:    "rmsData",
		},
		{
			name: "fail to parse body",
			setupMocks: func() {
				conns := mocks.FakeConns()
				// 1) QueryRow returns a valid org ID
				conns.Postgres.(*mocks.FakeDBExecutor).QueryRowFunc = func(query string, args ...interface{}) (map[string]interface{}, error) {
					return map[string]interface{}{"organizationidentifier": "ORG1"}, nil
				}
				connect.GetConnections = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return conns, nil
				}
				// 2) authenticateInfo ok
				authenticateInfo = func(_ connect.DatabaseExecutor, _ *pubsubdata.HeaderDetails) (string, error) {
					return "ORG1", nil
				}
				// 3) RedisSet ok
				connect.RedisSet = func(_ context.Context, _ *redis.Client, _ string, _ string) error {
					return nil
				}
				// 4) Topic exists
				connect.IsValidPubSubTopic = func(_ string, _ context.Context, _ connect.PsClient) (bool, error) {
					return false, nil
				}
			},
			wantStatusCode: http.StatusInternalServerError,
			messageType:    "rmsData",
		},
		{
			name: "fail isValidTopic err",
			setupMocks: func() {
				conns := mocks.FakeConns()
				// 1) QueryRow returns a valid org ID
				conns.Postgres.(*mocks.FakeDBExecutor).QueryRowFunc = func(query string, args ...interface{}) (map[string]interface{}, error) {
					return map[string]interface{}{"organizationidentifier": "ORG1"}, nil
				}
				connect.GetConnections = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return conns, nil
				}
				// 2) authenticateInfo ok
				authenticateInfo = func(_ connect.DatabaseExecutor, _ *pubsubdata.HeaderDetails) (string, error) {
					return "ORG1", nil
				}
				// 3) RedisSet ok
				connect.RedisSet = func(_ context.Context, _ *redis.Client, _ string, _ string) error {
					return nil
				}
				// 4) Topic exists
				connect.IsValidPubSubTopic = func(_ string, _ context.Context, _ connect.PsClient) (bool, error) {
					return false, errors.New("erroring IsValidPubSubTopic check")
				}
			},
			wantStatusCode: http.StatusInternalServerError,
			messageType:    "rmsData",
		},
		{
			name: "fail not isValidTopic",
			setupMocks: func() {
				conns := mocks.FakeConns()
				// 1) QueryRow returns a valid org ID
				conns.Postgres.(*mocks.FakeDBExecutor).QueryRowFunc = func(query string, args ...interface{}) (map[string]interface{}, error) {
					return map[string]interface{}{"organizationidentifier": "ORG1"}, nil
				}
				connect.GetConnections = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return conns, nil
				}
				// 2) authenticateInfo ok
				authenticateInfo = func(_ connect.DatabaseExecutor, _ *pubsubdata.HeaderDetails) (string, error) {
					return "ORG1", nil
				}
				// 3) RedisSet ok
				connect.RedisSet = func(_ context.Context, _ *redis.Client, _ string, _ string) error {
					return nil
				}
				// 4) Topic exists
				connect.IsValidPubSubTopic = func(_ string, _ context.Context, _ connect.PsClient) (bool, error) {
					return false, nil
				}
			},
			wantStatusCode: http.StatusInternalServerError,
			messageType:    "rmsData",
		},
		{
			name: "does not send to redis",
			setupMocks: func() {
				conns := mocks.FakeConns()
				// 1) QueryRow returns a valid org ID
				conns.Postgres.(*mocks.FakeDBExecutor).QueryRowFunc = func(query string, args ...interface{}) (map[string]interface{}, error) {
					return map[string]interface{}{"organizationidentifier": "ORG1"}, nil
				}
				connect.GetConnections = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return conns, nil
				}
				// 2) authenticateInfo ok
				authenticateInfo = func(_ connect.DatabaseExecutor, _ *pubsubdata.HeaderDetails) (string, error) {
					return "ORG1", nil
				}
				// 3) RedisSet ok
				connect.RedisSet = func(_ context.Context, _ *redis.Client, _ string, _ string) error {
					return nil
				}
				// 4) Topic exists
				connect.IsValidPubSubTopic = func(_ string, _ context.Context, _ connect.PsClient) (bool, error) {
					return true, nil
				}
			},
			wantStatusCode: http.StatusOK,
			messageType:    "perfStats",
		},
		{
			name: "bad approved versions",
			setupMocks: func() {
				conns := mocks.FakeConns()
				// 1) QueryRow returns a valid org ID
				conns.Postgres.(*mocks.FakeDBExecutor).QueryRowFunc = func(query string, args ...interface{}) (map[string]interface{}, error) {
					return map[string]interface{}{"organizationidentifier": "ORG1"}, nil
				}
				connect.GetConnections = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return conns, nil
				}
				// 2) authenticateInfo ok
				authenticateInfo = func(_ connect.DatabaseExecutor, _ *pubsubdata.HeaderDetails) (string, error) {
					return "ORG1", nil
				}
				// 3) RedisSet ok
				connect.RedisSet = func(_ context.Context, _ *redis.Client, _ string, _ string) error {
					return nil
				}
				// 4) Topic exists
				connect.IsValidPubSubTopic = func(_ string, _ context.Context, _ connect.PsClient) (bool, error) {
					return false, nil
				}
				ApprovedMessageVersions = []string{"not-a-version"}
			},
			wantStatusCode: http.StatusUnauthorized,
			messageType:    "perfStats",
		},
	} {
		t.Run(tc.name, func(t *testing.T) {
			tc.setupMocks()
			rec := httptest.NewRecorder()
			req := httptest.NewRequest("POST", "/", strings.NewReader(`{"foo":"bar"}`))

			// Put the options for the request body here since there can be different types
			switch tc.name {
			case "fail to parse body":
				req.Body = errorReader{}
			default:
				req.Body = io.NopCloser(strings.NewReader("1234567890abcde"))
			}

			// Required headers:
			req.Header.Set("gateway-device-id", "dev123")
			req.Header.Set("message-version", "v1")
			req.Header.Set("message-type", tc.messageType)
			req.Header.Set("x-api-key", "key123")
			req.Header.Set("tz", "UTC")

			Handler(rec, req)

			if rec.Code != tc.wantStatusCode {
				t.Fatalf("%s: got %d want %d", tc.name, rec.Code, tc.wantStatusCode)
			}
			var resp map[string]interface{}
			if err := json.Unmarshal(rec.Body.Bytes(), &resp); err != nil {
				t.Fatalf("%s: invalid JSON response: %v", tc.name, err)
			}
			if tc.wantStatusCode == http.StatusOK {
				if resp["status"] != "success" {
					t.Errorf("%s: expected success, got %v", tc.name, resp["status"])
				}
			} else {
				if resp["status"] != "error" {
					t.Errorf("%s: expected error, got %v", tc.name, resp["status"])
				}
			}
		})
	}
}

func TestParseRequest_MissingGatewayDeviceID(t *testing.T) {
	req := httptest.NewRequest("POST", "http://example.com", strings.NewReader("dummy"))
	// Set headers, but omit gateway-device-id.
	req.Header.Set("host", "example.com")
	req.Header.Set("user-agent", "test-agent")
	req.Header.Set("content-length", "10")
	req.Header.Set("content-type", "application/x-protobuf")
	req.Header.Set("message-version", "v1")
	req.Header.Set("message-type", "perfStats")
	req.Header.Set("x-api-key", "apikey123")

	hdrs, err := parseRequest(req)
	if err == nil {
		t.Fatal("expected error for missing gateway-device-id header, got nil")
	}
	expectedErr := "header gateway-device-id is not present"
	if !strings.Contains(err.Error(), expectedErr) {
		t.Fatalf("expected error %q, got %q", expectedErr, err.Error())
	}
	if hdrs.GatewayDeviceID != "" {
		t.Errorf("expected empty gateway-device-id, got %q", hdrs.GatewayDeviceID)
	}
}

func TestParseRequest_InvalidMessageType(t *testing.T) {
	req := httptest.NewRequest("POST", "http://example.com", strings.NewReader("dummy"))
	// Provide required headers.
	req.Header.Set("gateway-device-id", "device123")
	req.Header.Set("message-version", "v1")
	// Set an invalid message type; allowed types are (for example) "rmsData", "header", "rmsEngine", "monitorName", "macAddress", "logs", "perfStats".
	req.Header.Set("message-type", "invalidType")
	req.Header.Set("x-api-key", "apikey123")

	hdrs, err := parseRequest(req)
	if err == nil {
		t.Fatal("expected error for invalid message-type, got nil")
	}
	expectedErr := "invalid message-type"
	if !strings.Contains(err.Error(), expectedErr) {
		t.Fatalf("expected error to contain %q, got %q", expectedErr, err.Error())
	}
	if hdrs.MessageType != "invalidType" {
		t.Errorf("expected MessageType to be 'invalidType', got %q", hdrs.MessageType)
	}
}

func TestParseRequest_UnapprovedVersion(t *testing.T) {
	req := httptest.NewRequest("POST", "http://example.com", strings.NewReader("dummy"))
	req.Header.Set("gateway-device-id", "device123")
	// Set message-version to an unapproved version (approved version is "v1").
	req.Header.Set("message-version", "0.2")
	req.Header.Set("message-type", "perfStats") // Valid message type.
	req.Header.Set("x-api-key", "apikey123")

	hdrs, err := parseRequest(req)
	if err == nil {
		t.Fatal("expected error for unapproved message-version, got nil")
	}
	expectedErr := "is not approved"
	if !strings.Contains(err.Error(), expectedErr) {
		t.Fatalf("expected error to contain %q, got %q", expectedErr, err.Error())
	}
	if hdrs.MessageVersion != "0.2" {
		t.Errorf("expected message-version '0.2', got %q", hdrs.MessageVersion)
	}
}

func TestParseRequest_Success(t *testing.T) {
	req := httptest.NewRequest("POST", "http://example.com", strings.NewReader("dummy"))
	req.Header.Set("host", "example.com")
	req.Header.Set("user-agent", "test-agent")
	req.Header.Set("content-length", "10")
	req.Header.Set("content-type", "application/x-protobuf")
	req.Header.Set("gateway-device-id", "device123")
	req.Header.Set("message-version", "v1")     // Approved version.
	req.Header.Set("message-type", "perfStats") // Valid message type.
	req.Header.Set("x-api-key", "apikey123")

	hdrs, err := parseRequest(req)
	if err != nil {
		t.Fatalf("expected no error, got %v", err)
	}

	// Verify that the headers are correctly parsed.
	if hdrs.Host != "example.com" {
		t.Errorf("expected host 'example.com', got %q", hdrs.Host)
	}
	if hdrs.UserAgent != "test-agent" {
		t.Errorf("expected user-agent 'test-agent', got %q", hdrs.UserAgent)
	}
	if hdrs.GatewayDeviceID != "device123" {
		t.Errorf("expected gateway-device-id 'device123', got %q", hdrs.GatewayDeviceID)
	}
	if hdrs.MessageVersion != "v1" {
		t.Errorf("expected message-version 'v1', got %q", hdrs.MessageVersion)
	}
	if hdrs.MessageType != "perfStats" {
		t.Errorf("expected message-type 'perfStats', got %q", hdrs.MessageType)
	}
	if hdrs.APIKey != "apikey123" {
		t.Errorf("expected x-api-key 'apikey123', got %q", hdrs.APIKey)
	}
}

func TestIsApprovedVersion(t *testing.T) {
	tests := []struct {
		input    string
		approved bool
	}{
		// The approved version is "v1".
		{input: "v1", approved: true},
		// If input is provided in an extended or prefixed format, semver normalization should cover it.
		{input: "v1.0", approved: true},
		// Unapproved versions.
		{input: "0.2", approved: false},
		{input: "abc", approved: false},
	}

	for _, tt := range tests {
		got := isApprovedVersion(tt.input, ApprovedMessageVersions)
		if got != tt.approved {
			t.Errorf("isApprovedVersion(%q, ApprovedMessageVersions) = %v; want %v", tt.input, got, tt.approved)
		}
	}
}

// TestAuthenticateInfo_NoRows simulates the case where QueryRow returns sql.ErrNoRows.
func TestAuthenticateInfo_NoRows(t *testing.T) {
	// Create a fake DB executor where QueryRow returns sql.ErrNoRows.
	fakeDB := &mocks.FakeDBExecutor{
		QueryRowStructFunc: func(dest any, query string, args ...any) error {
			return sql.ErrNoRows
		},
	}

	fakeHeader := &pubsubdata.HeaderDetails{GatewayDeviceID: "gateway1", APIKey: "apikey1"}
	orgID, err := authenticateInfo(fakeDB, fakeHeader)
	// Expect: organization identifier is empty, error not nil
	assert.Equal(t, "", orgID)
	assert.Error(t, err, "Expected a non-nil error")
}

// TestAuthenticateInfo_QueryError simulates a generic error from QueryRow.
func TestAuthenticateInfo_QueryError(t *testing.T) {
	expectedErr := errors.New("db error")
	fakeDB := &mocks.FakeDBExecutor{
		QueryRowStructFunc: func(dest any, query string, args ...any) error {
			return expectedErr
		},
	}

	fakeHeader := &pubsubdata.HeaderDetails{GatewayDeviceID: "gateway1", APIKey: "apikey1"}
	orgID, err := authenticateInfo(fakeDB, fakeHeader)
	// Expect: organization identifier empty, error not nil
	assert.Equal(t, "", orgID)
	assert.Error(t, err, "Expected error on query failure")
}

// TestAuthenticateInfo_Success simulates a successful authentication.
func TestAuthenticateInfo_Success(t *testing.T) {
	queryResults := &dbAuthData{OrganizationIdentifier: "org123"}
	// Return a valid row with the expected "organizationidentifier" key.
	fakeDB := &mocks.FakeDBExecutor{
		QueryRowStructFunc: func(dest any, query string, args ...any) error {
			db, ok := dest.(*dbAuthData)
			if !ok {
				return errors.New("dest must be of type *dbConfig")
			}
			*db = *queryResults
			return nil
		},
	}

	fakeHeader := &pubsubdata.HeaderDetails{GatewayDeviceID: "gateway1", APIKey: "apikey1"}
	orgID, err := authenticateInfo(fakeDB, fakeHeader)
	// Expect: organization identifier "org123", error nil
	assert.Equal(t, "org123", orgID)
	assert.NoError(t, err)
}

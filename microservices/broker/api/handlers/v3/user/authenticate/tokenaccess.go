// Package authenticate encapsulates the logic dealing with access requests via a token
package authenticate

import (
	"context"
	"database/sql"
	"errors"
	"net/http"
	"time"

	jwttokens "synapse-its.com/shared/api/jwttokens"
	response "synapse-its.com/shared/api/response"
	security "synapse-its.com/shared/api/security"
	connect "synapse-its.com/shared/connect"
	logger "synapse-its.com/shared/logger"
)

// We're using a simplified DatabaseExecutor interface for our needs
// The full interface is defined in shared/connect/connect.go

// ConnectionProvider provides database connections
type ConnectionProvider func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error)

// LoggerFunc defines a function type for logging (deprecated)
// This type is kept for backward compatibility but is no longer used
type LoggerFunc func(format string, args ...interface{})

// TokenExpiryChecker checks if a token has expired
type TokenExpiryChecker func(expirationTimeStr string) (bool, error)

// J<PERSON><PERSON><PERSON> creates JWT tokens
type <PERSON><PERSON><PERSON><PERSON> func(username string, duration time.Duration, role int64, userPermissions jwttokens.UserPermissions) (string, time.Time, error)

// TokenPersister persists tokens to the database
type TokenPersister func(pg connect.DatabaseExecutor, userID int64, jwt string, expiresAt time.Time) error

// RoleValidator validates role IDs
type RoleValidator func(roleID int64) (string, error)

// TokenHandler handles token-based authentication
type TokenHandler struct {
	DBProvider       ConnectionProvider
	HasTokenExpired  TokenExpiryChecker
	JwtCreator       JWTCreator
	TokenPersister   TokenPersister
	ValidateRoleFunc RoleValidator
}

// TokenAccess takes in a token and if valid, returns a short-lived jwt and deletes the nonce
func (h *TokenHandler) TokenAccess(theToken string, w http.ResponseWriter, r *http.Request) {
	// Get the postgres connection.
	connections, err := h.DBProvider(r.Context())
	if err != nil {
		response.CreateInternalErrorResponse(w)
		logger.Errorf("%v", err) // Database connection errors are ERROR level
		return
	}

	pg := connections.Postgres

	sha256TokenValue := security.CalculateSHA256(theToken)

	// lookup the username in the db
	query := `
		SELECT
			u.Id,
			u.UserName,
			u.TokenDurationHours,
			o.OrganizationIdentifier,
			u.RoleId,
			o.APIKey,
			u.WebEnabled,
			u.WebTokenDurationSeconds,
			cast(ut.ExpirationUTC as text) as ExpirationUTC
		FROM {{User}} u
		JOIN {{UserToken}} ut ON u.Id = ut.UserId
		LEFT JOIN {{Organization}} o
			ON u.OrganizationId = o.Id
		WHERE ut.JWTTokenSha256 = $1 AND u.IsEnabled = 1`

	// Use QueryRowStruct with the dbUser struct
	user := dbUser{}
	err = pg.QueryRowStruct(&user, query, sha256TokenValue)

	switch {
	case errors.Is(err, sql.ErrNoRows):
		// token not valid -- log the failed attempt
		logger.Warnf("token (%s): %v", theToken, ErrTokenNotFound) // Authentication failures are WARN level
		response.CreateUnauthorizedResponse(w)
		return
	case err != nil:
		logger.Errorf("%v: %v", ErrDatabaseQuery, err) // Database query errors are ERROR level
		response.CreateUnauthorizedResponse(w)
		return
	default:
		// Use the struct fields directly instead of GetColumn calls
		userID := user.Id
		userName := user.UserName
		organizationID := user.OrganizationIdentifier
		roleID := user.RoleId
		apiKey := user.APIKey
		webEnabled := user.WebEnabled
		webTokenDurationSeconds := user.WebTokenDurationSeconds
		tokenExpirationUTC := user.ExpirationUTC

		// remove the token from the UserToken table - this is a one time use token!!!
		query = "DELETE FROM {{UserToken}} WHERE JWTTokenSha256 = $1"
		_, err = pg.Exec(query, sha256TokenValue)
		if err != nil {
			logger.Errorf("%v: %v", ErrTokenDeletion, err) // Database errors are ERROR level
			response.CreateUnauthorizedResponse(w)
			return
		}
		// check the token expiration, if it has expired... return unauthorized
		tokenExpired, err := h.HasTokenExpired(tokenExpirationUTC)
		if err != nil {
			logger.Errorf("%v: %v", ErrTokenExpiryCheck, err) // Token expiry check errors are ERROR level
			response.CreateUnauthorizedResponse(w)
			return
		}
		if tokenExpired {
			logger.Warnf("%v", ErrTokenExpired) // Expired token is a WARN level (expected condition)
			response.CreateUnauthorizedResponse(w)
			return
		}

		var roleName string
		if roleName, err = h.ValidateRoleFunc(roleID); err != nil {
			logger.Errorf("%v: %v", ErrRoleValidation, err) // Role validation errors are ERROR level
			response.CreateUnauthorizedResponse(w)
			return
		}

		// EMA-320
		if webEnabled != 1 { // the request was for web access; however, this user is not setup for web access.
			logger.Warnf("%v: user ID %d", ErrWebAccessNotEnabled, userID) // Configuration issue is a WARN level
			response.CreateUnauthorizedResponse(w)
			return
		}

		userDetail := &userDetailRecord{}
		userDetail.UserID = userID
		userDetail.OrganizationID = organizationID
		userDetail.Username = userName
		userDetail.APIKey = apiKey
		userDetail.Role = roleName

		// empty out any software-gateway permissions - not needed for web rest api access
		var userPermissions *jwttokens.UserPermissions
		userPermissions = &jwttokens.UserPermissions{}
		userPermissions.SoftwareGateway = []jwttokens.UserSoftwareGatewayAccess{}
		userPermissions.Device = []jwttokens.UserDeviceAccess{}

		duration := time.Duration(webTokenDurationSeconds) * time.Second

		jwt, expiresAt, err := h.JwtCreator(userName, duration, roleID, *userPermissions)
		if err != nil {
			logger.Errorf("%v: %v", ErrJWTCreation, err) // JWT creation errors are ERROR level
			response.CreateUnauthorizedResponse(w)
			return
		}

		err = h.TokenPersister(pg, userID, jwt, expiresAt)
		if err != nil {
			logger.Errorf("%v: %v", ErrTokenPersistence, err) // Token persistence errors are ERROR level
			response.CreateUnauthorizedResponse(w)
			return
		}
		jwtMsg := &dataUserResponsePayload{}
		jwtMsg.User = *userDetail
		jwtMsg.Token = jwt
		response.CreateAuthSuccessResponse(jwtMsg, w)
		return
	}
}

// NewTokenHandler creates a new TokenHandler with default dependencies
func NewTokenHandler() *TokenHandler {
	return &TokenHandler{
		DBProvider:       connect.GetConnections,
		HasTokenExpired:  hasTokenExpired,
		JwtCreator:       jwttokens.CreateJwtTokenUsingDuration,
		TokenPersister:   persistTokenToDB,
		ValidateRoleFunc: jwttokens.ValidateRoleInt,
	}
}

package schemaexecutor

import "fmt"

var (
	// Error messages for FakeDBExecutor
	ErrFakeDBExecutorExecCallLimit              = fmt.<PERSON>rrorf("Exec call limit reached")
	ErrFakeDBExecutorExecMultipleCallLimit      = fmt.Errorf("ExecMultiple call limit reached")
	ErrFakeDBExecutorQueryGenericCallLimit      = fmt.Errorf("QueryGeneric call limit reached")
	ErrFakeDBExecutorQueryGenericSliceCallLimit = fmt.Errorf("QueryGenericSlice call limit reached")
	ErrFakeDBExecutorQueryRowCallLimit          = fmt.Errorf("QueryRow call limit reached")
	ErrFakeDBExecutorQueryRowStructCallLimit    = fmt.Errorf("QueryRowStruct call limit reached")
	ErrFakeDBExecutorCloseCallLimit             = fmt.Errorf("Close call limit reached")

	// Error messages for FakeSchemaMigrationExecutor
	ErrFakeSchemaMigrationExecutorEnsureSchemaMigrationsTableCallLimit = fmt.Errorf("EnsureSchemaMigrationsTable call limit reached")
	ErrFakeSchemaMigrationExecutorQueryGenericCallLimit                = fmt.Errorf("QueryGeneric call limit reached")
	ErrFakeSchemaMigrationExecutorExecCallLimit                        = fmt.Errorf("Exec call limit reached")
	ErrFakeSchemaMigrationExecutorExecMultipleCallLimit                = fmt.Errorf("ExecMultiple call limit reached")
	ErrFakeSchemaMigrationExecutorQueryRowCallLimit                    = fmt.Errorf("QueryRow call limit reached")
)

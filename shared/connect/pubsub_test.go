package connect

import (
	"context"
	"errors"
	"testing"
	"time"

	"cloud.google.com/go/pubsub"
	"cloud.google.com/go/pubsub/pstest"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"google.golang.org/api/option"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
)

// Mock PsTopic
type mockPsTopic struct {
	mock.Mock
}

func (m *mockPsTopic) Publish(ctx context.Context, msg *pubsub.Message) PsPublishResult {
	args := m.Called(ctx, msg)
	return args.Get(0).(PsPublishResult)
}

func (m *mockPsTopic) Exists(ctx context.Context) (bool, error) {
	args := m.Called(ctx)
	return args.Bool(0), args.Error(1)
}

// Mock PsClient
type mockPsClient struct {
	mock.Mock
}

func (m *mockPsClient) Topic(name string) PsTopic {
	args := m.Called(name)
	return args.Get(0).(PsTopic)
}

func (m *mockPsClient) Subscription(name string) PsSubscription {
	panic("not implemented") // not needed for this test
}

func (m *mockPsClient) CreateTopic(ctx context.Context, name string) (PsTopic, error) {
	panic("not implemented")
}

func (m *mockPsClient) CreateSubscription(ctx context.Context, name string, cfg SubscriptionConfig) (PsSubscription, error) {
	panic("not implemented")
}

func (m *mockPsClient) Close() error {
	panic("not implemented")
}

func Test_IsValidPubSubTopic_Success(t *testing.T) {
	ctx := context.Background()

	mockTopic := new(mockPsTopic)
	mockTopic.On("Exists", ctx).Return(true, nil)

	mockClient := new(mockPsClient)
	mockClient.On("Topic", "existing-topic").Return(mockTopic)

	valid, err := IsValidPubSubTopic("existing-topic", ctx, mockClient)
	assert.NoError(t, err)
	assert.True(t, valid)

	mockTopic.AssertExpectations(t)
	mockClient.AssertExpectations(t)
}

func Test_IsValidPubSubTopic_TopicNotExistsWithError(t *testing.T) {
	ctx := context.Background()
	expectedErr := errors.New("topic does not exist")

	mockTopic := new(mockPsTopic)
	mockTopic.On("Exists", ctx).Return(false, expectedErr)

	mockClient := new(mockPsClient)
	mockClient.On("Topic", "missing-topic").Return(mockTopic)

	valid, err := IsValidPubSubTopic("missing-topic", ctx, mockClient)
	assert.Error(t, err)
	assert.False(t, valid)
	assert.Equal(t, expectedErr, err)

	mockTopic.AssertExpectations(t)
	mockClient.AssertExpectations(t)
}

func TestWrapPubsubClient(t *testing.T) {
	// Arrange
	mockPubsubClient := &pubsub.Client{}

	// Act
	psClient := WrapPubsubClient(mockPubsubClient)

	// Check if psClient is of type interface PsClient
	assert.Implements(t, (*PsClient)(nil), psClient)

	// Check the concrete type of psClient
	_, ok := psClient.(*PubsubClient)
	assert.True(t, ok, "psClient should be of type *PubsubClient")

	// Check if the wrapped client is the same as the input client
	actualClient := psClient.(*PubsubClient).c
	assert.Equal(t, mockPubsubClient, actualClient, "Wrapped client should match input client")
}

func TestPubsubClient_Topic(t *testing.T) {
	// Arrange
	mockPubsubClient := &pubsub.Client{}
	// mockTopic := &pubsub.Topic{}
	client := &PubsubClient{c: mockPubsubClient}
	topicName := "test-topic"

	// Act
	result := client.Topic(topicName)

	// Check if the result implements the PsTopic interface
	assert.Implements(t, (*PsTopic)(nil), result)

	// Check if the wrapped topic has the correct type
	pubsubTopic, ok := result.(*PubsubTopic)
	assert.True(t, ok, "Result should be of type *PubsubTopic")

	// Verify the wrapped topic has the correct type
	assert.IsType(t, &pubsub.Topic{}, pubsubTopic.t, "Internal topic should be of type *pubsub.Topic")
}

func TestPubsubClient_Subscription(t *testing.T) {
	// Arrange
	mockPubsubClient := &pubsub.Client{}
	client := &PubsubClient{c: mockPubsubClient}
	subscriptionName := "test-subscription"

	// Act
	result := client.Subscription(subscriptionName)

	// Check if the result implements the PsSubscription interface
	assert.Implements(t, (*PsSubscription)(nil), result)

	// Check the concrete type of the result
	pubsubSubscription, ok := result.(*PubsubSubscription)
	assert.True(t, ok, "Result should be of type *PubsubSubscription")

	// Check if the wrapped subscription is of the correct type
	assert.IsType(t, &pubsub.Subscription{}, pubsubSubscription.s, "Internal subscription should be of type *pubsub.Subscription")
}

func TestPubsubClient_Close(t *testing.T) {
	ctx := context.Background()

	// Start the in-memory fake pubsub server
	srv := pstest.NewServer()
	defer srv.Close()

	// Connect pubsub client to the fake server
	conn, err := grpc.NewClient(srv.Addr, grpc.WithTransportCredentials(insecure.NewCredentials()))

	assert.NoError(t, err)
	defer conn.Close()

	client, err := pubsub.NewClient(ctx, "test-project", option.WithGRPCConn(conn))
	assert.NoError(t, err)

	psClient := WrapPubsubClient(client)

	// Act: call Close
	err = psClient.Close()

	// Assert
	assert.NoError(t, err)
}

func TestPubsubTopic_Publish(t *testing.T) {
	ctx := context.Background()

	// Start fake Pub/Sub server
	srv := pstest.NewServer()
	defer srv.Close()

	// Connect pubsub client to the fake server
	conn, err := grpc.NewClient(srv.Addr, grpc.WithTransportCredentials(insecure.NewCredentials()))

	assert.NoError(t, err)
	defer conn.Close()

	client, err := pubsub.NewClient(ctx, "test-project", option.WithGRPCConn(conn))
	assert.NoError(t, err)

	// Create a topic
	topic, err := client.CreateTopic(ctx, "test-topic")
	assert.NoError(t, err)

	// Wrap topic using our abstraction
	psTopic := &PubsubTopic{t: topic}

	// Publish a message
	msg := &pubsub.Message{
		Data:       []byte("hello"),
		Attributes: map[string]string{"key": "value"},
	}
	result := psTopic.Publish(ctx, msg)

	// Assert: result should implement PsPublishResult
	id, err := result.Get(ctx)
	assert.NoError(t, err)
	assert.NotEmpty(t, id)
}

func TestPubsubTopic_Exists(t *testing.T) {
	ctx := context.Background()

	// Setup fake Pub/Sub server
	srv := pstest.NewServer()
	defer srv.Close()

	// Connect pubsub client to the fake server
	conn, err := grpc.NewClient(srv.Addr, grpc.WithTransportCredentials(insecure.NewCredentials()))

	assert.NoError(t, err)
	defer conn.Close()

	client, err := pubsub.NewClient(ctx, "test-project", option.WithGRPCConn(conn))
	assert.NoError(t, err)

	// Create a topic
	topicName := "existing-topic"
	topic, err := client.CreateTopic(ctx, topicName)
	assert.NoError(t, err)

	// Wrap topic
	psTopic := &PubsubTopic{t: topic}

	// Act & Assert
	exists, err := psTopic.Exists(ctx)
	assert.NoError(t, err)
	assert.True(t, exists)
}

func TestPubsubSubscription_Receive(t *testing.T) {
	ctx := context.Background()

	// Setup fake Pub/Sub server
	srv := pstest.NewServer()
	defer srv.Close()

	// Connect pubsub client to the fake server
	conn, err := grpc.NewClient(srv.Addr, grpc.WithTransportCredentials(insecure.NewCredentials()))

	assert.NoError(t, err)
	defer conn.Close()

	// Initialize PubSub client using this connection
	client, err := pubsub.NewClient(ctx, "test-project", option.WithGRPCConn(conn))
	assert.NoError(t, err)

	// Create topic and subscription
	topic, err := client.CreateTopic(ctx, "test-topic")
	assert.NoError(t, err)

	sub, err := client.CreateSubscription(ctx, "test-sub", pubsub.SubscriptionConfig{
		Topic:       topic,
		AckDeadline: 10 * time.Second,
	})
	assert.NoError(t, err)

	// Publish message to topic
	res := topic.Publish(ctx, &pubsub.Message{Data: []byte("hello")})
	_, err = res.Get(ctx)
	assert.NoError(t, err)

	// Wrap subscription into PubsubSubscription
	psSub := &PubsubSubscription{s: sub}

	// Setup context and callback for receiving message
	receiveCtx, cancel := context.WithCancel(ctx)
	defer cancel()

	received := false
	err = psSub.Receive(receiveCtx, func(ctx context.Context, msg *pubsub.Message) {
		assert.Equal(t, []byte("hello"), msg.Data)
		msg.Ack()
		received = true
		cancel()
	})

	// Check if message was received successfully
	assert.NoError(t, err)
	assert.True(t, received, "Message should have been received")
}

func TestPubsubSubscription_Exists(t *testing.T) {
	ctx := context.Background()

	// Setup fake Pub/Sub server
	srv := pstest.NewServer()
	defer srv.Close()

	// Connect pubsub client to the fake server
	conn, err := grpc.NewClient(srv.Addr, grpc.WithTransportCredentials(insecure.NewCredentials()))

	assert.NoError(t, err)
	defer conn.Close()

	// Initialize PubSub client using this connection
	client, err := pubsub.NewClient(ctx, "test-project", option.WithGRPCConn(conn))
	assert.NoError(t, err)

	// Create topic and subscription
	topic, err := client.CreateTopic(ctx, "test-topic")
	assert.NoError(t, err)

	sub, err := client.CreateSubscription(ctx, "test-sub", pubsub.SubscriptionConfig{
		Topic:       topic,
		AckDeadline: 10 * time.Second,
	})
	assert.NoError(t, err)

	// Wrap subscription into PubsubSubscription
	psSub := &PubsubSubscription{s: sub}

	// Define the mock behavior for the Exists method
	// Simulate a successful existence check (returns true, nil)
	exists, err := psSub.Exists(context.Background())

	// Assert that there is no error and the subscription exists
	assert.NoError(t, err)
	assert.True(t, exists)
}

func TestPubsubSubscription_Close(t *testing.T) {
	ctx := context.Background()

	// Start the in-memory fake pubsub server
	srv := pstest.NewServer()
	defer srv.Close()

	// Connect pubsub client to the fake server
	conn, err := grpc.NewClient(srv.Addr, grpc.WithTransportCredentials(insecure.NewCredentials()))

	assert.NoError(t, err)
	defer conn.Close()

	client, err := pubsub.NewClient(ctx, "test-project", option.WithGRPCConn(conn))
	assert.NoError(t, err)

	topic, err := client.CreateTopic(ctx, "test-topic")
	assert.NoError(t, err)

	sub, err := client.CreateSubscription(ctx, "test-sub", pubsub.SubscriptionConfig{
		Topic:       topic,
		AckDeadline: 10 * time.Second,
	})
	assert.NoError(t, err)

	psSub := &PubsubSubscription{s: sub}

	// Act: call Close
	err = psSub.Close()

	// Assert
	assert.NoError(t, err)
}

func TestPubsubClient_CreateTopic(t *testing.T) {
	ctx := context.Background()

	// Start fake Pub/Sub server
	srv := pstest.NewServer()
	defer srv.Close()

	// Connect pubsub client to the fake server
	conn, err := grpc.NewClient(srv.Addr, grpc.WithTransportCredentials(insecure.NewCredentials()))

	assert.NoError(t, err)
	defer conn.Close()

	// Create real pubsub client with mocked connection
	client, err := pubsub.NewClient(ctx, "test-project", option.WithGRPCConn(conn))
	assert.NoError(t, err)

	// Wrap client with our abstraction
	psClient := &PubsubClient{c: client}

	// Call the method to test
	psTopic, err := psClient.CreateTopic(ctx, "test-topic")

	// Assertions
	assert.NoError(t, err)
	assert.NotNil(t, psTopic)

	// Further: verify topic exists
	exists, err := psTopic.Exists(ctx)
	assert.NoError(t, err)
	assert.True(t, exists)
}

func TestPubsubClient_CreateTopic_Duplicate(t *testing.T) {
	ctx := context.Background()

	srv := pstest.NewServer()
	defer srv.Close()

	// Connect pubsub client to the fake server
	conn, err := grpc.NewClient(srv.Addr, grpc.WithTransportCredentials(insecure.NewCredentials()))

	assert.NoError(t, err)
	defer conn.Close()

	client, err := pubsub.NewClient(ctx, "test-project", option.WithGRPCConn(conn))
	assert.NoError(t, err)

	psClient := &PubsubClient{c: client}

	// Create topic for the first time (should succeed)
	_, err = psClient.CreateTopic(ctx, "duplicate-topic")
	assert.NoError(t, err)

	// Try to create topic again (should fail)
	topic2, err := psClient.CreateTopic(ctx, "duplicate-topic")
	assert.Error(t, err)
	assert.Nil(t, topic2)
}

func TestPubsubClient_CreateSubscription_Success(t *testing.T) {
	ctx := context.Background()
	srv := pstest.NewServer()
	defer srv.Close()

	// Connect pubsub client to the fake server
	conn, err := grpc.NewClient(srv.Addr, grpc.WithTransportCredentials(insecure.NewCredentials()))

	assert.NoError(t, err)
	defer conn.Close()

	client, err := pubsub.NewClient(ctx, "test-project", option.WithGRPCConn(conn))
	assert.NoError(t, err)

	psClient := &PubsubClient{c: client}

	// Create topic first
	topic, err := client.CreateTopic(ctx, "test-topic")
	assert.NoError(t, err)
	psTopic := &PubsubTopic{t: topic}

	// Create subscription
	cfg := SubscriptionConfig{
		Topic:       psTopic,
		AckDeadline: 10 * time.Second,
	}

	sub, err := psClient.CreateSubscription(ctx, "test-sub", cfg)
	assert.NoError(t, err)
	assert.NotNil(t, sub)
}

func TestPubsubClient_CreateSubscription_Duplicate(t *testing.T) {
	ctx := context.Background()
	srv := pstest.NewServer()
	defer srv.Close()

	// Connect pubsub client to the fake server
	conn, err := grpc.NewClient(srv.Addr, grpc.WithTransportCredentials(insecure.NewCredentials()))

	assert.NoError(t, err)
	defer conn.Close()

	client, err := pubsub.NewClient(ctx, "test-project", option.WithGRPCConn(conn))
	assert.NoError(t, err)

	psClient := &PubsubClient{c: client}

	// Create topic first
	topic, err := client.CreateTopic(ctx, "test-topic")
	assert.NoError(t, err)
	psTopic := &PubsubTopic{t: topic}

	cfg := SubscriptionConfig{
		Topic:       psTopic,
		AckDeadline: 10 * time.Second,
	}

	// First creation (should succeed)
	_, err = psClient.CreateSubscription(ctx, "test-sub", cfg)
	assert.NoError(t, err)

	// Create duplicate subscription (should fail)
	sub2, err := psClient.CreateSubscription(ctx, "test-sub", cfg)
	assert.Error(t, err)
	assert.Nil(t, sub2)
}

func TestPubSub_SuccessWithEmulator(t *testing.T) {
	ctx := context.Background()
	srv := pstest.NewServer()
	defer srv.Close()

	// Setup env
	t.Setenv("GCP_PROJECT_ID", "")
	t.Setenv("PUBSUB_EMULATOR_HOST", srv.Addr)

	client, err := PubSub(ctx)
	assert.NoError(t, err)
	assert.NotNil(t, client)

	// Ensure client can create a topic (sanity check)
	topic, err := client.CreateTopic(ctx, "demo")
	assert.NoError(t, err)
	assert.NotNil(t, topic)
}

func savePubSubVars() func() {
	origRedisNewClient := pubsubNewClient
	origtimeSleepPubSub := timeSleepPubSub
	return func() {
		pubsubNewClient = origRedisNewClient
		timeSleepPubSub = origtimeSleepPubSub
	}
}

func TestPubSub_Failure_MaxRetries(t *testing.T) {
	restore := savePubSubVars()
	defer restore()

	ctx := context.Background()

	// Unset emulator host to simulate real connection (which will fail in test env)
	t.Setenv("GCP_PROJECT_ID", "")
	t.Setenv("PUBSUB_EMULATOR_HOST", "")

	pubsubNewClient = func(ctx context.Context, projectID string, opts ...option.ClientOption) (*pubsub.Client, error) {
		return nil, errors.New("forced failure")
	}
	timeSleepPubSub = func(d time.Duration) {}

	client, err := PubSub(ctx)
	assert.Nil(t, client)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "failed to create PubSub client after 5 attempts")
}

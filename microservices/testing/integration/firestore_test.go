package integration

import (
	"context"
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"synapse-its.com/shared/connect"
)

func TestFirestoreEmulatorIntegration(t *testing.T) {
	// Skip unless the emulator is explicitly configured
	if os.Getenv("FIRESTORE_EMULATOR_HOST") == "" {
		t.Skip("Skipping integration test: FIRESTORE_EMULATOR_HOST not set")
	}
	if os.Getenv("FIRESTORE_AUTH_ENCRYPTED") == "" {
		t.Skip("Skipping integration test: FIRESTORE_AUTH_ENCRYPTED not set")
	}

	ctx := context.Background()
	client, err := connect.Firestore(ctx)
	assert.NoError(t, err, "connect.Firestore() should not error")
	if err != nil {
		return // avoid calling methods on nil
	}
	defer client.Close()

	const (
		collection = "integration-test"
		docID      = "it-doc"
	)
	doc := client.Collection(collection).Doc(docID)

	// 1) Write
	want := map[string]interface{}{
		"foo":    "bar",
		"number": 42,
	}
	_, err = doc.Set(ctx, want)
	assert.NoError(t, err, "Doc.Set() failed")

	// 2) Read back
	snap, err := doc.Get(ctx)
	assert.NoError(t, err, "Doc.Get() failed")
	assert.True(t, snap.Exists(), "Expected document %q to exist", docID)

	got := snap.Data()

	// 3) Assert fields
	assert.Equal(t, want["foo"], got["foo"], "Field foo mismatch")

	// Firestore returns numeric values as int64
	num, ok := got["number"].(int64)
	assert.True(t, ok, "Field number has wrong type: %T", got["number"])
	assert.Equal(t, int64(want["number"].(int)), num, "Field number mismatch")
}

func TestBulkWriterIntegration(t *testing.T) {
	// Skip unless the emulator is explicitly configured
	if os.Getenv("FIRESTORE_EMULATOR_HOST") == "" {
		t.Skip("Skipping integration test: FIRESTORE_EMULATOR_HOST not set")
	}
	if os.Getenv("FIRESTORE_AUTH_ENCRYPTED") == "" {
		t.Skip("Skipping integration test: FIRESTORE_AUTH_ENCRYPTED not set")
	}

	ctx := context.Background()
	client, err := connect.Firestore(ctx)
	assert.NoError(t, err, "connect.Firestore() should not error")
	defer client.Close()

	const (
		collection = "integration-test-bulk"
		docID      = "delete-me"
	)
	doc := client.Collection(collection).Doc(docID)

	// 1) Seed a document so there's something to delete
	seed := map[string]interface{}{"hello": "world"}
	_, err = doc.Set(ctx, seed)
	assert.NoError(t, err, "Doc.Set() failed")

	// confirm it exists
	snap, err := doc.Get(ctx)
	assert.NoError(t, err, "Doc.Get() pre-delete failed")
	assert.True(t, snap.Exists(), "pre-delete document should exist")

	// 2) Use BulkWriter to delete it
	bw := client.BulkWriter(ctx)
	err = bw.Delete(doc)
	assert.NoError(t, err, "BulkWriter.Delete() failed")
	err = bw.Flush()
	assert.NoError(t, err, "BulkWriter.Flush() failed")

	// 3) Verify it's gone
	snap, err = doc.Get(ctx)
	if assert.Error(t, err, "Doc.Get() post-delete should error") {
		st := status.Code(err)
		assert.Equal(t, codes.NotFound, st, "expected NotFound after delete, got %v", st)
	} else {
		// Some clients return a snapshot with Exists==false instead of an error
		assert.False(t, snap.Exists(), "post-delete snapshot.Exists should be false")
	}
}

package authenticate

import (
	"database/sql"
	"errors"
	"io"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	jwttokens "synapse-its.com/shared/api/jwttokens"
	connect "synapse-its.com/shared/connect"
	mocks "synapse-its.com/shared/mocks"
)

// Test_validateUsername_Scenarios tests the validateUsername function with various inputs
func Test_validateUsername_Scenarios(t *testing.T) {
	// Define test case structure according to coverage rules
	tests := []struct {
		name        string      // Required: descriptive test name
		credentials credentials // Input: credentials to validate
		wantErr     bool        // Whether error is expected
		expectedErr error       // Expected error if any
	}{
		{
			name:        "username_too_short",
			credentials: credentials{Username: "ab", Password: "dummy"},
			wantErr:     true,
			expectedErr: ErrAPIUnauthorized,
		},
		{
			name:        "username_too_long",
			credentials: credentials{Username: strings.Repeat("a", MaxUsernameLength+1), Password: "dummy"},
			wantErr:     true,
			expectedErr: ErrAPIUnauthorized,
		},
		{
			name:        "valid_username",
			credentials: credentials{Username: "validUser", Password: "dummy"},
			wantErr:     false,
			expectedErr: nil,
		},
	}

	// Execute all test cases
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Execute test
			err := validateUsername(tt.credentials)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err, "validateUsername should return an error for invalid username")
				assert.Equal(t, tt.expectedErr, err, "Error should match expected error")
			} else {
				assert.NoError(t, err, "validateUsername should not return an error for valid username")
			}
		})
	}
}

// Test_validatePassword_Scenarios tests the validatePassword function with various inputs
func Test_validatePassword_Scenarios(t *testing.T) {
	// Define test case structure according to coverage rules
	tests := []struct {
		name        string      // Required: descriptive test name
		credentials credentials // Input: credentials to validate
		wantErr     bool        // Whether error is expected
		expectedErr error       // Expected error if any
	}{
		{
			name:        "password_too_short",
			credentials: credentials{Username: "dummy", Password: "12345"},
			wantErr:     true,
			expectedErr: ErrAPIUnauthorized,
		},
		{
			name:        "password_too_long",
			credentials: credentials{Username: "dummy", Password: strings.Repeat("a", MaxUserPasswordLength+1)},
			wantErr:     true,
			expectedErr: ErrAPIUnauthorized,
		},
		{
			name:        "valid_password",
			credentials: credentials{Username: "dummy", Password: "securePass"},
			wantErr:     false,
			expectedErr: nil,
		},
	}

	// Execute all test cases
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Execute test
			err := validatePassword(tt.credentials)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err, "validatePassword should return an error for invalid password")
				assert.Equal(t, tt.expectedErr, err, "Error should match expected error")
			} else {
				assert.NoError(t, err, "validatePassword should not return an error for valid password")
			}
		})
	}
}

// Test_persistTokenToDB_Scenarios tests the persistTokenToDB function with various inputs
func Test_persistTokenToDB_Scenarios(t *testing.T) {
	// Define test case structure according to coverage rules
	tests := []struct {
		name        string                          // Required: descriptive test name
		setupFn     func() connect.DatabaseExecutor // Setup function to create mock DB
		userID      int64                           // Input: user ID
		jwt         string                          // Input: JWT token
		expiresAt   time.Time                       // Input: expiration time
		wantErr     bool                            // Whether error is expected
		expectedErr string                          // Expected error message if any
	}{
		{
			name: "successful_token_persistence",
			setupFn: func() connect.DatabaseExecutor {
				return &mocks.FakeDBExecutor{
					ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
						if !strings.Contains(query, "INSERT INTO") {
							return nil, errors.New("unexpected query")
						}
						return nil, nil
					},
				}
			},
			userID:      int64(1),
			jwt:         "dummyjwt",
			expiresAt:   time.Now().UTC().Add(1 * time.Hour),
			wantErr:     false,
			expectedErr: "",
		},
		{
			name: "database_error",
			setupFn: func() connect.DatabaseExecutor {
				return &mocks.FakeDBExecutor{
					ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
						return nil, errors.New("db error")
					},
				}
			},
			userID:      int64(1),
			jwt:         "dummyjwt",
			expiresAt:   time.Now().UTC().Add(1 * time.Hour),
			wantErr:     true,
			expectedErr: "db error",
		},
	}

	// Execute all test cases
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup: Create mock database
			fakeDB := tt.setupFn()

			// Execute test
			err := persistTokenToDB(fakeDB, tt.userID, tt.jwt, tt.expiresAt)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err, "persistTokenToDB should return an error when database operation fails")
				assert.Contains(t, err.Error(), tt.expectedErr, "Error message should contain expected text")
			} else {
				assert.NoError(t, err, "persistTokenToDB should not return an error for successful operation")
			}
		})
	}
}

// Test_createUserPermissions_Scenarios tests the createUserPermissions function with various inputs
func Test_createUserPermissions_Scenarios(t *testing.T) {
	// Define test case structure according to coverage rules
	tests := []struct {
		name        string                                       // Required: descriptive test name
		setupFn     func() connect.DatabaseExecutor              // Setup function to create mock DB
		userID      int64                                        // Input: user ID
		wantErr     bool                                         // Whether error is expected
		expectedErr string                                       // Expected error message if any
		validateFn  func(*testing.T, *jwttokens.UserPermissions) // Function to validate the result
	}{
		{
			name: "successful_permissions_retrieval",
			setupFn: func() connect.DatabaseExecutor {
				return &mocks.FakeDBExecutor{
					QueryGenericFunc: func(query string, args ...interface{}) ([]map[string]interface{}, error) {
						row := map[string]interface{}{
							"id": "gateway1",
						}
						return []map[string]interface{}{row}, nil
					},
				}
			},
			userID:      int64(1),
			wantErr:     false,
			expectedErr: "",
			validateFn: func(t *testing.T, perms *jwttokens.UserPermissions) {
				assert.NotNil(t, perms, "Permissions should not be nil")
				assert.Len(t, perms.SoftwareGateway, 1, "Should have one software gateway")
				assert.Equal(t, "gateway1", perms.SoftwareGateway[0].SoftwareGateway, "Gateway ID should match")
			},
		},
		{
			name: "query_error",
			setupFn: func() connect.DatabaseExecutor {
				return &mocks.FakeDBExecutor{
					QueryGenericFunc: func(query string, args ...interface{}) ([]map[string]interface{}, error) {
						return nil, errors.New("query error")
					},
				}
			},
			userID:      int64(1),
			wantErr:     true,
			expectedErr: "query error",
			validateFn:  nil,
		},
		{
			name: "column_extraction_error",
			setupFn: func() connect.DatabaseExecutor {
				return &mocks.FakeDBExecutor{
					QueryGenericFunc: func(query string, args ...interface{}) ([]map[string]interface{}, error) {
						// Return a row with missing "id" column to trigger GetColumn error
						row := map[string]interface{}{
							"wrong_column": "gateway1",
						}
						return []map[string]interface{}{row}, nil
					},
				}
			},
			userID:      int64(1),
			wantErr:     true,
			expectedErr: "expected column",
			validateFn:  nil,
		},
	}

	// Execute all test cases
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup: Create mock database
			fakeDB := tt.setupFn()

			// Execute test
			perms, err := createUserPermissions(fakeDB, tt.userID)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err, "createUserPermissions should return an error for failure cases")
				assert.Contains(t, err.Error(), tt.expectedErr, "Error message should contain expected text")
			} else {
				assert.NoError(t, err, "createUserPermissions should not return an error for successful operation")
				if tt.validateFn != nil {
					tt.validateFn(t, perms)
				}
			}
		})
	}
}

// Test_extractUserNameAndPassword_Scenarios tests the extractUserNameAndPassword function with various inputs
func Test_extractUserNameAndPassword_Scenarios(t *testing.T) {
	// Define test case structure according to coverage rules
	tests := []struct {
		name        string // Required: descriptive test name
		jsonInput   string // Input: JSON string to parse
		wantErr     bool   // Whether error is expected
		expectedErr string // Expected error message if any
		username    string // Expected username output
		password    string // Expected password output
	}{
		{
			name:        "valid_credentials",
			jsonInput:   `{"username": "testuser", "password": "securePass"}`,
			wantErr:     false,
			expectedErr: "",
			username:    "testuser",
			password:    "securePass",
		},
		{
			name:        "invalid_json",
			jsonInput:   `{"username": "testuser", "password":}`,
			wantErr:     true,
			expectedErr: "error parsing username and password",
			username:    "",
			password:    "",
		},
		{
			name:        "username_too_short",
			jsonInput:   `{"username": "test", "password": "securePass"}`,
			wantErr:     true,
			expectedErr: "unauthorized",
			username:    "",
			password:    "",
		},
		{
			name:        "password_too_short",
			jsonInput:   `{"username": "testuser", "password": "short"}`,
			wantErr:     true,
			expectedErr: "unauthorized",
			username:    "",
			password:    "",
		},
	}

	// Execute all test cases
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup: Create reader from JSON string
			reader := io.NopCloser(strings.NewReader(tt.jsonInput))

			// Execute test
			username, password, err := extractUserNameAndPassword(reader)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err, "extractUserNameAndPassword should return an error for invalid inputs")
				assert.Contains(t, err.Error(), tt.expectedErr, "Error message should contain expected text")
			} else {
				assert.NoError(t, err, "extractUserNameAndPassword should not return an error for valid inputs")
				assert.Equal(t, tt.username, username, "Username should match expected value")
				assert.Equal(t, tt.password, password, "Password should match expected value")
			}
		})
	}
}

// Test_generateNonce_Scenarios tests the generateNonce function with various inputs
func Test_generateNonce_Scenarios(t *testing.T) {
	t.Run("successful_nonce_generation", func(t *testing.T) {
		// Test parameters
		size := 16

		// Execute test
		nonce, err := generateNonce(size)

		// Assert results
		assert.NoError(t, err, "generateNonce should not return an error for successful operation")
		assert.NotNil(t, nonce, "Nonce should not be nil")
		assert.Equal(t, size, len(nonce), "Nonce length should match requested size")
	})

	t.Run("rand_read_error", func(t *testing.T) {
		// Store the original randRead function to restore it after tests
		originalRandRead := randRead
		// Setup: Mock randRead to return an error
		randRead = func(b []byte) (n int, err error) {
			return 0, errors.New("random generation failed")
		}
		// Cleanup: Restore original function
		defer func() { randRead = originalRandRead }()

		// Test parameters
		size := 16

		// Execute test
		nonce, err := generateNonce(size)

		// Assert results
		assert.Error(t, err, "generateNonce should return an error when random generation fails")
		assert.Contains(t, err.Error(), "failed to generate nonce", "Error message should contain expected text")
		assert.Nil(t, nonce, "Nonce should be nil when error occurs")
	})
}

// Test_hasTokenExpired_Scenarios tests the hasTokenExpired function with various inputs
func Test_hasTokenExpired_Scenarios(t *testing.T) {
	// Define test case structure according to coverage rules
	tests := []struct {
		name           string // Required: descriptive test name
		expirationTime string // Input: expiration time string
		wantErr        bool   // Whether error is expected
		expectedErr    string // Expected error message if any
		expectedResult bool   // Expected result (has expired)
	}{
		{
			name:           "future_time_not_expired",
			expirationTime: time.Now().UTC().Add(1 * time.Hour).Format(time.DateTime),
			wantErr:        false,
			expectedErr:    "",
			expectedResult: false,
		},
		{
			name:           "past_time_expired",
			expirationTime: time.Now().UTC().Add(-1 * time.Hour).Format(time.DateTime),
			wantErr:        true,
			expectedErr:    "token passed in has expired",
			expectedResult: true,
		},
		{
			name:           "invalid_time_format",
			expirationTime: "invalid date",
			wantErr:        true,
			expectedErr:    "parsing time",
			expectedResult: true,
		},
	}

	// Execute all test cases
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Execute test
			expired, err := hasTokenExpired(tt.expirationTime)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err, "hasTokenExpired should return an error for invalid inputs")
				assert.Contains(t, err.Error(), tt.expectedErr, "Error message should contain expected text")
			} else {
				assert.NoError(t, err, "hasTokenExpired should not return an error for valid inputs")
			}
			assert.Equal(t, tt.expectedResult, expired, "Expired status should match expected value")
		})
	}
}

package helper

import (
	"bytes"
	"compress/flate"
	"errors"
	"io"
	"strings"
	"testing"

	"github.com/google/uuid"
)

func Test_CompressBytes_Scenarios(t *testing.T) {
	// Store original writer function to restore after test
	origWriter := flateNewWriter
	defer func() { flateNewWriter = origWriter }()

	// Define test cases
	tests := []struct {
		name        string
		input       []byte
		mockFn      func() // Optional: mock setup
		expected    []byte // Expected output (not used for error cases)
		wantErr     bool
		expectedErr string
	}{
		{
			name:    "success_empty_input",
			input:   []byte{},
			wantErr: false,
		},
		{
			name:    "success_small_input",
			input:   []byte("hello, world"),
			wantErr: false,
		},
		{
			name:  "error_new_writer_fails",
			input: []byte("anything"),
			mockFn: func() {
				// Mock the flateNewWriter function to simulate a failure
				flateNewWriter = func(w io.Writer, level int) (*flate.Writer, error) {
					return nil, errors.New("whoops: new writer failed")
				}
			},
			wantErr:     true,
			expectedErr: "whoops: new writer failed",
		},
		{
			name:  "error_write_operation_fails",
			input: []byte("hello"),
			mockFn: func() {
				// Mock the flateNewWriter function to return a writer that fails on Write
				flateNewWriter = func(w io.Writer, level int) (*flate.Writer, error) {
					return flate.NewWriter(errWriter{}, level)
				}
			},
			wantErr:     true,
			expectedErr: "boom: write failed",
		},
		{
			name:  "error_close_operation_fails",
			input: bytes.Repeat([]byte("A"), 128),
			mockFn: func() {
				// Mock the flateNewWriter function to return a writer that fails on Close
				flateNewWriter = func(w io.Writer, level int) (*flate.Writer, error) {
					return flate.NewWriter(&errOnFlushWriter{}, level)
				}
			},
			wantErr:     true,
			expectedErr: "zap: flush failed",
		},
	}

	// Execute test cases
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Reset to default before each test
			flateNewWriter = origWriter

			// Optional: Mock setup
			if tt.mockFn != nil {
				tt.mockFn()
			}

			// Execute test
			out, err := CompressBytes(tt.input)

			// Assert results
			if tt.wantErr {
				if err == nil {
					t.Fatalf("CompressBytes(%v) expected error but got nil", tt.input)
				}
				if !strings.Contains(err.Error(), tt.expectedErr) {
					t.Fatalf("CompressBytes(%v) expected error containing %q, got %v",
						tt.input, tt.expectedErr, err)
				}
				return
			}

			if err != nil {
				t.Fatalf("CompressBytes(%v) unexpected error: %v", tt.input, err)
			}
			if len(out) == 0 {
				t.Fatal("CompressBytes() expected non-empty compressed output")
			}

			// Verify round-trip for success cases
			dec, err := DecompressBytes(out)
			if err != nil {
				t.Fatalf("DecompressBytes() error during round-trip verification: %v", err)
			}
			if !bytes.Equal(dec, tt.input) {
				t.Errorf("Round-trip decompression = %v, want %v", dec, tt.input)
			}
		})
	}
}

func Test_DecompressBytes_Scenarios(t *testing.T) {
	// Store original reader function to restore after test
	origReader := flateNewReader
	defer func() { flateNewReader = origReader }()

	// Prepare one valid compressed blob for testing
	original := []byte("some test data")
	compressed, err := CompressBytes(original)
	if err != nil {
		t.Fatalf("CompressBytes setup failed: %v", err)
	}

	// Define test cases
	tests := []struct {
		name        string
		input       []byte
		mockFn      func() // Optional: mock setup
		expected    []byte // Expected output (not used for error cases)
		wantErr     bool
		expectedErr string
	}{
		{
			name:        "error_invalid_data",
			input:       []byte("not really compressed"),
			wantErr:     true,
			expectedErr: "", // We don't check specific error message for invalid data
		},
		{
			name:     "success_valid_compressed_data",
			input:    compressed,
			wantErr:  false,
			expected: original,
		},
		{
			name:  "error_close_operation_fails",
			input: compressed,
			mockFn: func() {
				// Mock the flateNewReader function to return a reader that fails on Close
				flateNewReader = func(r io.Reader) io.ReadCloser {
					return badCloseReadCloser{flate.NewReader(r)}
				}
			},
			wantErr:     true,
			expectedErr: "whoops: close failed",
		},
	}

	// Execute test cases
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Reset to default before each test
			flateNewReader = origReader

			// Optional: Mock setup
			if tt.mockFn != nil {
				tt.mockFn()
			}

			// Execute test
			out, err := DecompressBytes(tt.input)

			// Assert results
			if tt.wantErr {
				if err == nil {
					t.Fatalf("DecompressBytes(%v) expected error but got nil", tt.input)
				}
				if tt.expectedErr != "" && !strings.Contains(err.Error(), tt.expectedErr) {
					t.Fatalf("DecompressBytes(%v) expected error containing %q, got %v",
						tt.input, tt.expectedErr, err)
				}
				return
			}

			if err != nil {
				t.Fatalf("DecompressBytes(%v) unexpected error: %v", tt.input, err)
			}

			if !bytes.Equal(out, tt.expected) {
				t.Errorf("DecompressBytes(%v) = %v, want %v", tt.input, out, tt.expected)
			}
		})
	}
}

func Test_CreateGuid_UniquenessAndFormat(t *testing.T) {
	// Test that CreateGuid generates valid UUIDs and doesn't duplicate
	seen := map[string]bool{}

	// Generate multiple GUIDs to check for uniqueness
	for i := 0; i < 5; i++ {
		// Execute test
		guid := CreateGuid()

		// Assert valid UUID format
		_, err := uuid.Parse(guid)
		if err != nil {
			t.Fatalf("CreateGuid() generated invalid UUID %q: %v", guid, err)
		}

		// Assert uniqueness
		if seen[guid] {
			t.Fatalf("CreateGuid() generated duplicate GUID: %q", guid)
		}

		// Mark this GUID as seen
		seen[guid] = true
	}
}

// ——— Helpers for error injection ———

type errWriter struct{}

func (errWriter) Write(p []byte) (int, error) {
	return 0, errors.New("boom: write failed")
}

type errOnFlushWriter struct{ calls int }

func (w *errOnFlushWriter) Write(p []byte) (int, error) {
	w.calls++
	if w.calls > 1 {
		return 0, errors.New("zap: flush failed")
	}
	return len(p), nil
}

type badCloseReadCloser struct{ io.ReadCloser }

func (b badCloseReadCloser) Close() error {
	return errors.New("whoops: close failed")
}

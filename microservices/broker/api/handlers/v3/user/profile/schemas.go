package profile

import "time"

// The user profile data structure.
type UserProfileRecord struct {
	UserID                 int64     `db:"id" json:"user_id"`
	Username               string    `db:"username" json:"username"`
	Firstname              string    `db:"firstname" json:"first_name"`
	Lastname               string    `db:"lastname" json:"last_name"`
	Email                  string    `db:"email" json:"email"`
	Mobile                 string    `db:"mobile" json:"mobile"`
	NotificationSmsEnabled bool      `db:"notificationsmsenabled" json:"notification_sms_enabled"`
	LastLogin              time.Time `db:"lastloginutc" json:"last_login"`
	Description            string    `db:"description" json:"description"`
}

// The application version configuration.
type AppVersions struct {
	EDIFieldServiceApp struct {
		Dev          AppVersion `json:"dev"`
		NotPublished AppVersion `json:"notpublished"`
		Published    AppVersion `json:"published"`
	} `json:"EDIFieldServiceApp"`
}

// The version information for different platforms.
type AppVersion struct {
	Android string `json:"android"`
	IOS     string `json:"ios"`
	Windows string `json:"windows"`
}

// The response structure for the profile endpoint.
type ResponsePayload struct {
	User       UserProfileRecord `json:"user_profile"`
	AppVersion AppVersions       `json:"app_version"`
}

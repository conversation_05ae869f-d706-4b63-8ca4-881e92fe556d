// src/environments/environment.ts
declare global {
  interface Window { env: { [key: string]: string } }
}

export const environment = {
  production: false,  // not used for OIDC, but <PERSON><PERSON> still wants it
  oidc: {
    issuer: window.env['OIDC_ISSUER_URL'],
    clientId: window.env['OIDC_CLIENT_ID'],
    redirectUri: window.env['OIDC_REDIRECT_URI'],
    scope: window.env['OIDC_SCOPE'],
    responseType: 'code',
  }
};

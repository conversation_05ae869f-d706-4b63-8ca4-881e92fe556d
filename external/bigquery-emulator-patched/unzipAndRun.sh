#!/usr/bin/env bash
set -euo pipefail

# Paths
A="/app/bqbin.gz"
B="/app/bin/bqbin.gz"
C="/app/bin/bqbin"

# If B exists, has the same size as A, and B is not older than A, skip copy/unzip
if [[ -f "$B" ]] && \
   [[ $(stat -c%s "$A") -eq $(stat -c%s "$B") ]] && \
   [[ ! "$A" -nt "$B" ]]; then
  echo "$B is up-to-date; skipping copy/unzip."
else
  echo "Updating $B from $A and regenerating $C..."
  cp "$A" "$B"
  gunzip -c "$B" > "$C"
fi

chmod +x "$C"
exec "$C" "$@"

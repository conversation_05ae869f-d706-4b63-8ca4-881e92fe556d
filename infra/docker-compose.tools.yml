services:
  go:
    image: golang:1.24
    container_name: go
    working_dir: /app
    volumes:
      - ../:/app
    entrypoint: bash
    stdin_open: true
    tty: true
    network_mode: host
  gcp:
    image: gcr.io/google.com/cloudsdktool/google-cloud-cli:latest
    container_name: gcp
    working_dir: /app
    volumes:
      - ../:/app
    entrypoint: bash
    stdin_open: true
    tty: true
    network_mode: host

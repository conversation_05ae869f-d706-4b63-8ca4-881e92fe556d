package schemaexecutor

import (
	"database/sql"
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"synapse-its.com/shared/connect"
)

// fakeResultMigrationExecutor is a minimal sql.Result implementation for testing.
type fakeResultMigrationExecutor struct{}

func (r *fakeResultMigrationExecutor) LastInsertId() (int64, error) { return 0, nil }
func (r *fakeResultMigrationExecutor) RowsAffected() (int64, error) { return 0, nil }

var errCustomFunc = fmt.Errorf("custom function error")

func TestFakeSchemaMigrationExecutor_EnsureSchemaMigrationsTable(t *testing.T) {
	tests := []struct {
		name     string
		executor *FakeSchemaMigrationExecutor
		calls    int
		wantErrs []error
	}{
		{"default", &FakeSchemaMigrationExecutor{}, 1, []error{nil}},
		{"custom", &FakeSchemaMigrationExecutor{EnsureSchemaMigrationsTableFunc: func() error { return errCustomFunc }}, 1, []error{errCustomFunc}},
		{"fail0", &FakeSchemaMigrationExecutor{EnableFailAfter: true, EnsureSchemaMigrationsTableCallFailAfter: 0}, 1, []error{ErrFakeSchemaMigrationExecutorEnsureSchemaMigrationsTableCallLimit}},
		{"fail2", &FakeSchemaMigrationExecutor{EnableFailAfter: true, EnsureSchemaMigrationsTableCallFailAfter: 2}, 3, []error{nil, nil, ErrFakeSchemaMigrationExecutorEnsureSchemaMigrationsTableCallLimit}},
		{"never fail", &FakeSchemaMigrationExecutor{EnableFailAfter: true, EnsureSchemaMigrationsTableCallFailAfter: -1}, 3, []error{nil, nil, nil}},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			for i := 0; i < tc.calls; i++ {
				err := tc.executor.EnsureSchemaMigrationsTable()
				if tc.wantErrs[i] == nil {
					require.NoError(t, err)
				} else {
					assert.ErrorIs(t, err, tc.wantErrs[i])
				}
				assert.Equal(t, i+1, tc.executor.EnsureSchemaMigrationsTableCallCount)
			}
		})
	}
}

func TestFakeSchemaMigrationExecutor_QueryGeneric(t *testing.T) {
	tests := []struct {
		name     string
		executor *FakeSchemaMigrationExecutor
		calls    int
		wantErrs []error
	}{
		{"default", &FakeSchemaMigrationExecutor{}, 1, []error{nil}},
		{"custom", &FakeSchemaMigrationExecutor{QueryGenericFunc: func(query string, args ...interface{}) ([]map[string]interface{}, error) { return nil, errCustomFunc }}, 1, []error{errCustomFunc}},
		{"fail0", &FakeSchemaMigrationExecutor{EnableFailAfter: true, QueryGenericCallFailAfter: 0}, 1, []error{ErrFakeSchemaMigrationExecutorQueryGenericCallLimit}},
		{"fail2", &FakeSchemaMigrationExecutor{EnableFailAfter: true, QueryGenericCallFailAfter: 2}, 3, []error{nil, nil, ErrFakeSchemaMigrationExecutorQueryGenericCallLimit}},
		{"never fail", &FakeSchemaMigrationExecutor{EnableFailAfter: true, QueryGenericCallFailAfter: -1}, 3, []error{nil, nil, nil}},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			for i := 0; i < tc.calls; i++ {
				_, err := tc.executor.QueryGeneric("Q")
				if tc.wantErrs[i] == nil {
					require.NoError(t, err)
				} else {
					assert.ErrorIs(t, err, tc.wantErrs[i])
				}
				assert.Equal(t, i+1, tc.executor.QueryGenericCallCount)
			}
		})
	}
}

func TestFakeSchemaMigrationExecutor_Exec(t *testing.T) {
	tests := []struct {
		name       string
		executor   *FakeSchemaMigrationExecutor
		calls      int
		wantErrs   []error
		wantResNil bool
	}{
		{"default", &FakeSchemaMigrationExecutor{}, 1, []error{nil}, true},
		{"custom", &FakeSchemaMigrationExecutor{ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
			return &fakeResultMigrationExecutor{}, errCustomFunc
		}}, 1, []error{errCustomFunc}, false},
		{"fail0", &FakeSchemaMigrationExecutor{EnableFailAfter: true, ExecCallFailAfter: 0}, 1, []error{ErrFakeSchemaMigrationExecutorExecCallLimit}, true},
		{"fail2", &FakeSchemaMigrationExecutor{EnableFailAfter: true, ExecCallFailAfter: 2}, 3, []error{nil, nil, ErrFakeSchemaMigrationExecutorExecCallLimit}, true},
		{"never fail", &FakeSchemaMigrationExecutor{EnableFailAfter: true, ExecCallFailAfter: -1}, 3, []error{nil, nil, nil}, true},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			for i := 0; i < tc.calls; i++ {
				res, err := tc.executor.Exec("Q")
				if tc.wantErrs[i] == nil {
					require.NoError(t, err)
				} else {
					assert.ErrorIs(t, err, tc.wantErrs[i])
				}
				if tc.wantResNil {
					assert.Nil(t, res)
				} else {
					assert.NotNil(t, res)
				}
				assert.Equal(t, i+1, tc.executor.ExecCallCount)
			}
		})
	}
}

func TestFakeSchemaMigrationExecutor_ExecMultiple(t *testing.T) {
	tests := []struct {
		name     string
		executor *FakeSchemaMigrationExecutor
		calls    int
		wantErrs []error
	}{
		{"default", &FakeSchemaMigrationExecutor{}, 1, []error{nil}},
		{"custom", &FakeSchemaMigrationExecutor{ExecMultipleFunc: func(q string) error { return errCustomFunc }}, 1, []error{errCustomFunc}},
		{"fail0", &FakeSchemaMigrationExecutor{EnableFailAfter: true, ExecMultipleCallFailAfter: 0}, 1, []error{ErrFakeSchemaMigrationExecutorExecMultipleCallLimit}},
		{"fail2", &FakeSchemaMigrationExecutor{EnableFailAfter: true, ExecMultipleCallFailAfter: 2}, 3, []error{nil, nil, ErrFakeSchemaMigrationExecutorExecMultipleCallLimit}},
		{"never fail", &FakeSchemaMigrationExecutor{EnableFailAfter: true, ExecMultipleCallFailAfter: -1}, 3, []error{nil, nil, nil}},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			for i := 0; i < tc.calls; i++ {
				err := tc.executor.ExecMultiple("Q")
				if tc.wantErrs[i] == nil {
					require.NoError(t, err)
				} else {
					assert.ErrorIs(t, err, tc.wantErrs[i])
				}
				assert.Equal(t, i+1, tc.executor.ExecMultipleCallCount)
			}
		})
	}
}

func TestFakeSchemaMigrationExecutor_QueryRow(t *testing.T) {
	tests := []struct {
		name     string
		executor *FakeSchemaMigrationExecutor
		calls    int
		wantErrs []error
	}{
		{"default", &FakeSchemaMigrationExecutor{}, 1, []error{nil}},
		{"custom", &FakeSchemaMigrationExecutor{QueryRowFunc: func(q string, args ...interface{}) (map[string]interface{}, error) { return nil, errCustomFunc }}, 1, []error{errCustomFunc}},
		{"fail0", &FakeSchemaMigrationExecutor{EnableFailAfter: true, QueryRowCallFailAfter: 0}, 1, []error{ErrFakeSchemaMigrationExecutorQueryRowCallLimit}},
		{"fail2", &FakeSchemaMigrationExecutor{EnableFailAfter: true, QueryRowCallFailAfter: 2}, 3, []error{nil, nil, ErrFakeSchemaMigrationExecutorQueryRowCallLimit}},
		{"never fail", &FakeSchemaMigrationExecutor{EnableFailAfter: true, QueryRowCallFailAfter: -1}, 3, []error{nil, nil, nil}},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			for i := 0; i < tc.calls; i++ {
				_, err := tc.executor.QueryRow("Q")
				if tc.wantErrs[i] == nil {
					require.NoError(t, err)
				} else {
					assert.ErrorIs(t, err, tc.wantErrs[i])
				}
				assert.Equal(t, i+1, tc.executor.QueryRowCallCount)
			}
		})
	}
}

func TestFakeSchemaMigrationExecutor_Config(t *testing.T) {
	tests := []struct {
		name       string
		executor   *FakeSchemaMigrationExecutor
		calls      int
		wantConfig connect.DatabaseConfig
	}{
		{"default", &FakeSchemaMigrationExecutor{}, 1, connect.DatabaseConfig{}},
		{"custom", &FakeSchemaMigrationExecutor{
			ConfigFunc: func() connect.DatabaseConfig {
				return connect.DatabaseConfig{Environment: "foo"}
			},
		}, 1, connect.DatabaseConfig{Environment: "foo"}},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			for i := 0; i < tc.calls; i++ {
				cfg := tc.executor.Config()
				// verify returned config
				assert.Equal(t, tc.wantConfig, cfg, "call %d: config should match", i+1)
				// verify call count incremented
				assert.Equal(t, i+1, tc.executor.ConfigCallCount, "call %d: ConfigCallCount should increment", i+1)
			}
		})
	}
}

package handlers

import (
	"encoding/json"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestFooHandler(t *testing.T) {
	// Create a request to pass to our handler (we don't care about method or URL here)
	req := httptest.NewRequest("GET", "/foo", nil)
	rr := httptest.NewRecorder()

	// Invoke the handler
	<PERSON><PERSON><PERSON><PERSON><PERSON>(rr, req)

	// 1. Should return HTTP 200 OK
	assert.Equal(t, 200, rr.Code, "status code")

	// 2. Should set Content-Type to application/json
	assert.Equal(t, "application/json", rr.Header().Get("Content-Type"), "Content-Type header")

	// 3. The body should be the JSON array of objects
	var got []map[string]string
	err := json.Unmarshal(rr.Body.Bytes(), &got)
	assert.NoError(t, err, "body should be valid JSON")

	// 4. Verify the contents
	want := []map[string]string{
		{"orgIden": "org123", "apiKey": "key-abc-123", "name": "OEM Alpha"},
		{"orgIden": "org456", "apiKey": "key-def-456", "name": "OEM Beta"},
		{"orgIden": "org789", "apiKey": "key-ghi-789", "name": "OEM Gamma"},
	}
	assert.Equal(t, want, got, "response JSON")
}

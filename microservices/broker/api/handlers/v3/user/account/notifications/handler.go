package notifications

import (
	"context"
	"encoding/json"
	"net/http"

	"github.com/google/uuid"
	"synapse-its.com/shared/api/authorizer"
	"synapse-its.com/shared/api/response"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/logger"
)

// HandlerDeps bundles dependencies for injection and testing.
type HandlerDeps struct {
	UserInfoFromContext func(ctx context.Context) (*authorizer.UserInfo, bool)
	UUIDGenerator       func() (uuid.UUID, error)
	GetConnections      func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error)
	GetRepository       func(conns *connect.Connections) Repository
}

// HandlerWithDeps returns an http.HandlerFunc with injected deps.
func HandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Get user info
		userInfo, ok := deps.UserInfoFromContext(ctx)
		if !ok {
			logger.Error("Unable to retrieve user info from request context")
			response.CreateInternalErrorResponse(w)
			return
		}

		// Get connections
		conns, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("error - getting connections. err: (%v)", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Parse request
		var request Request
		err = json.NewDecoder(r.Body).Decode(&request)
		if err != nil {
			logger.Errorf("error - parsing request body. err: (%v)", err)
			response.CreateBadRequestResponse(w)
			return
		}

		repository := deps.GetRepository(conns)
		err = repository.UpdateNotificationSmsEnabled(userInfo.ID, request.NotificationSmsEnabled)
		if err != nil {
			logger.Errorf("error - updating notification sms for user failed. user_id: (%v), err: (%v)", userInfo.ID, err)
			response.CreateInternalErrorResponse(w)
			return
		}

		response.CreateSuccessResponse(Response{
			UserID: int(userInfo.ID),
			TraceID: func() string {
				trace, err := deps.UUIDGenerator()
				if err != nil {
					logger.Errorf("error - generating uuid. err: (%v)\n", err)
					return "unexpected"
				}
				return trace.String()
			}(),
			NotificationSmsEnabled: request.NotificationSmsEnabled,
		}, w)
	}
}

// Handler is the production-ready HTTP handler using default deps.
var Handler = HandlerWithDeps(HandlerDeps{
	UserInfoFromContext: authorizer.UserInfoFromContext,
	UUIDGenerator:       uuid.NewRandom,
	GetConnections:      connect.GetConnections,
	GetRepository:       getV3UserAccountNotificationsRepository,
})

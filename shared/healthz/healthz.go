package healthz

import (
	"context"
	"net"
	"net/http"
	"strings"
	"sync/atomic"
	"time"

	"synapse-its.com/shared/httplogger"
)

// Interface so it can be mocked
type HealthzServer interface {
	ListenAndServe() error
	Shutdown(ctx context.Context) error
	SetBootComplete()
	SetReady()
	SetNotReady()
	SetCustomReadinessCheck(fn func() error)
}

// Server governs a healthz HTTP service.
type Server struct {
	addr string       // e.g. ":8081" or ":0" for random
	srv  *http.Server // HTTP server bound to s.listener
	// internal listener if you want to control shutdown
	listener net.Listener

	boot   atomic.Bool  // startup status
	ready  atomic.Bool  // readiness status
	custom atomic.Value // optional custom readiness check: func() error
}

// NewServer constructs a healthz server that will listen on "addr" (":0" means ephemeral).
func NewServer(addr string) HealthzServer {
	s := &Server{addr: normalizePort(addr)}
	mux := http.NewServeMux()
	mux.HandleFunc("/livez", s.livez)
	mux.HandleFunc("/startupz", s.startupz)
	mux.HandleFunc("/readyz", s.readyz)
	s.srv = &http.Server{
		Handler:      httplogger.LoggingMiddleware(mux),
		ReadTimeout:  2 * time.Second,
		WriteTimeout: 2 * time.Second,
	}
	return s
}

// ListenAndServe starts listening on s.addr. If s.addr == ":0", OS chooses a free port.
// After calling ListenAndServe, s.ActualAddr() returns the real "host:port".
func (s *Server) ListenAndServe() error {
	ln, err := net.Listen("tcp", s.addr)
	if err != nil {
		return err
	}
	s.listener = ln
	go s.srv.Serve(ln)
	return nil
}

// Shutdown gracefully stops the HTTP server (max 5s).
func (s *Server) Shutdown(ctx context.Context) error {
	if s.srv == nil {
		return nil
	}
	if s.listener != nil {
		if err := s.srv.Shutdown(ctx); err != nil {
			return err
		}
	}
	return nil
}

// ActualAddr returns the address (host:port) on which the server is listening.
// Only valid after ListenAndServe() returns nil.
func (s *Server) ActualAddr() string {
	if s.listener != nil {
		return s.listener.Addr().String()
	}
	return ""
}

// SetBootComplete flips /startupz -> 200
func (s *Server) SetBootComplete() {
	s.boot.Store(true)
}

// SetReady flips /readyz -> 200
func (s *Server) SetReady() {
	s.ready.Store(true)
}

// SetNotReady flips /readyz -> 503
func (s *Server) SetNotReady() {
	s.ready.Store(false)
}

// SetCustomReadinessCheck optionally installs a custom check for /readyz.
func (s *Server) SetCustomReadinessCheck(fn func() error) {
	s.custom.Store(fn)
}

// --- HTTP handlers (methods on *Server) ---

func (s *Server) livez(w http.ResponseWriter, _ *http.Request) {
	w.WriteHeader(http.StatusOK)
}

func (s *Server) startupz(w http.ResponseWriter, _ *http.Request) {
	if s.boot.Load() {
		w.WriteHeader(http.StatusOK)
	} else {
		http.Error(w, "starting up", http.StatusServiceUnavailable)
	}
}

func (s *Server) readyz(w http.ResponseWriter, _ *http.Request) {
	if !s.ready.Load() {
		http.Error(w, "not ready", http.StatusServiceUnavailable)
		return
	}
	if fn, ok := s.custom.Load().(func() error); ok {
		if err := fn(); err != nil {
			http.Error(w, "not ready: "+err.Error(), http.StatusServiceUnavailable)
			return
		}
	}
	w.WriteHeader(http.StatusOK)
}

// Used to set default port if none set
func normalizePort(port string) string {
	// default to 8081 if not set
	if port == "" {
		port = "8081"
	}
	port = strings.TrimLeft(port, ":")
	return ":" + port
}

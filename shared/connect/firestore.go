package connect

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"os"

	"cloud.google.com/go/firestore"
	"google.golang.org/api/option"

	"synapse-its.com/shared/api/security"
)

var (
	firestoreNewClient = firestore.NewClient
	jsonMarshal        = json.Marshal
)

// FirestoreClientInterface abstracts *firestore.Client
type FirestoreClientInterface interface {
	Collection(name string) CollectionRefInterface
	BulkWriter(ctx context.Context) BulkWriterInterface
	Close() error
}

// CollectionRefInterface abstracts firestore.CollectionRef
type CollectionRefInterface interface {
	Doc(id string) DocumentRefInterface
}

// DocumentRefInterface abstracts firestore.DocumentRef
type DocumentRefInterface interface {
	Get(ctx context.Context) (DocumentSnapshotInterface, error)
	Set(ctx context.Context, data any) (*WriteResult, error)
}

// DocumentSnapshotInterface abstracts firestore.DocumentSnapshot
type DocumentSnapshotInterface interface {
	Data() map[string]any
	Exists() bool
}

// BulkWriterInterface abstracts firestore.BulkWriter
type BulkWriterInterface interface {
	Delete(DocumentRefInterface) error
	Flush() error
}

// firestoreClientWrapper implements FirestoreClientInterface
type firestoreClientWrapper struct {
	inner *firestore.Client
}

func (w *firestoreClientWrapper) Collection(name string) CollectionRefInterface {
	return &collectionRefWrapper{inner: w.inner.Collection(name)}
}

func (w *firestoreClientWrapper) BulkWriter(ctx context.Context) BulkWriterInterface {
	return &bulkWriterWrapper{inner: w.inner.BulkWriter(ctx)}
}

func (w *firestoreClientWrapper) Close() error {
	return w.inner.Close()
}

// collectionRefWrapper implements CollectionRefInterface
type collectionRefWrapper struct {
	inner *firestore.CollectionRef
}

func (c *collectionRefWrapper) Doc(id string) DocumentRefInterface {
	return &documentRefWrapper{inner: c.inner.Doc(id)}
}

// documentRefWrapper implements DocumentRefInterface
type documentRefWrapper struct {
	inner *firestore.DocumentRef
}

func (d *documentRefWrapper) Get(ctx context.Context) (DocumentSnapshotInterface, error) {
	snap, err := d.inner.Get(ctx)
	if err != nil {
		return nil, err
	}
	return &documentSnapshotWrapper{inner: snap}, nil
}

func (d *documentRefWrapper) Set(ctx context.Context, data any) (*WriteResult, error) {
	wr, err := d.inner.Set(ctx, data)
	return (*WriteResult)(wr), err
}

// documentSnapshotWrapper implements DocumentSnapshotInterface
type documentSnapshotWrapper struct {
	inner *firestore.DocumentSnapshot
}

func (s *documentSnapshotWrapper) Data() map[string]any { return s.inner.Data() }
func (s *documentSnapshotWrapper) Exists() bool         { return s.inner.Exists() }

// bulkWriterWrapper implements BulkWriterInterface
type bulkWriterWrapper struct {
	inner *firestore.BulkWriter
}

func (b *bulkWriterWrapper) Delete(ref DocumentRefInterface) error {
	// unwrap to the real DocumentRef
	dr := ref.(*documentRefWrapper)
	_, err := b.inner.Delete(dr.inner)
	return err
}

func (b *bulkWriterWrapper) Flush() error {
	b.inner.Flush()
	return nil
}

// WriteResult is real
type WriteResult = firestore.WriteResult

// FirestoreClient returns a *firestore.Client, wired to the emulator if
// FIRESTORE_EMULATOR_HOST is set.
func Firestore(ctx context.Context) (FirestoreClientInterface, error) {
	// Get ENV variable containing connection info
	enc := os.Getenv("FIRESTORE_AUTH_ENCRYPTED")
	if enc == "" {
		return nil, errors.New("FIRESTORE_AUTH_ENCRYPTED must be set")
	}

	// Decrypt service account and connection info from ENV variable
	settings, err := security.FirebaseAuthDecrypt(enc)
	if err != nil {
		return nil, fmt.Errorf("decrypting FIRESTORE_AUTH_ENCRYPTED: %w", err)
	}

	// Prep to connect to actual endpoint
	credsJSON, err := jsonMarshal(settings)
	if err != nil {
		return nil, fmt.Errorf("json.Marshal GoogleSettings: %w", err)
	}

	var raw *firestore.Client
	// If emulator enviromental variable is set, use it
	if emu := os.Getenv("FIRESTORE_EMULATOR_HOST"); emu != "" {
		raw, err = firestoreNewClient(ctx, settings.ProjectId,
			option.WithEndpoint(emu), // point at emulator
			option.WithoutAuthentication(),
		)
		// If emulator ENV variable is not set, use full credentials
	} else {
		raw, err = firestoreNewClient(ctx, settings.ProjectId,
			option.WithCredentialsJSON(credsJSON),
		)
	}
	if err != nil {
		return nil, err
	}

	return &firestoreClientWrapper{inner: raw}, nil
}

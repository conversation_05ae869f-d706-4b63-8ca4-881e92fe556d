package security

import (
	"crypto/aes"
	"crypto/cipher"
	"encoding/base64"
	"encoding/json"
	"fmt"
)

// allow for dependency injection for testing
var (
	aesNewCipher = aes.NewCipher
	gcmNew       = cipher.NewGCM
)

const key = "6MwP8wwU3i7olyg0kdHg2ngYVa4xU8Ug"

// GoogleSettings is the encrypted json struct put into the FIREBASE_AUTH_ENCRYPTED used
// to connect to the PROD instance
type GoogleSettings struct {
	Type                     string `json:"type"`
	ProjectId                string `json:"project_id"`
	PrivateKeyID             string `json:"private_key_id"`
	PrivateKey               string `json:"private_key"`
	ClientEmail              string `json:"client_email"`
	ClientId                 string `json:"clientId"`
	AuthUri                  string `json:"auth_uri"`
	TokenUri                 string `json:"token_uri"`
	AuthProviderX509CcertUrl string `json:"auth_provider_x509_cert_url"`
	ClientX509CertUrl        string `json:"client_x509_cert_url"`
	UniverseDomain           string `json:"universe_domain"`
}

// firebaseAuthDecrypt takes the encoded FIREBASE_AUTH_ENCRYPTED and returns a *GoogleSettings
// used to create the *firestore.Client
func FirebaseAuthDecrypt(googleConfigBase64Encrypted string) (*GoogleSettings, error) {
	googleSettings := &GoogleSettings{}

	googleConfigStr, err := decryptBase64EncodedString(googleConfigBase64Encrypted)
	if err != nil {
		return nil, err
	}

	if err := json.Unmarshal([]byte(googleConfigStr), googleSettings); err != nil {
		return nil, err
	}

	return googleSettings, nil
}

// decryptBase64EncodedString is used to decrypt the FIREBASE_AUTH_ENCRYPTED
func decryptBase64EncodedString(encoded string) (string, error) {
	data, err := base64.StdEncoding.DecodeString(encoded)
	if err != nil {
		return "", err
	}

	// Create an AES cipher
	block, err := aesNewCipher([]byte(key))
	if err != nil {
		return "", err
	}

	// Galois Counter Mode (GCM)
	gcm, err := gcmNew(block)
	if err != nil {
		return "", err
	}

	// Extract the nonce
	if len(data) < gcm.NonceSize() {
		return "", fmt.Errorf("ciphertext too short")
	}
	nonce, ciphertext := data[:gcm.NonceSize()], data[gcm.NonceSize():]

	// Decrypt the data
	decrypted, err := gcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return "", err
	}

	return string(decrypted), nil
}

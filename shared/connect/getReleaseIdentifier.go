package connect

import (
	"errors"
	"os"
	"strings"
	"sync"
)

var (
	cachedIdentifier string
	cachedError      error
	identifierOnce   sync.Once
)

// GetReleaseIdentifier attempts to read "identifier.txt" and returns its trimmed contents.
// If that fails or is empty, it falls back to the CURRENT_TIMESTAMP environment variable.
// If both are empty or unset, it returns an error.
// The result is cached so that subsequent calls return the same value.
var GetReleaseIdentifier = func() (string, error) {
	identifierOnce.Do(func() {
		// Try reading from the file
		data, err := os.ReadFile("identifier.txt")
		if err == nil {
			identifier := strings.TrimSpace(string(data))
			if identifier != "" {
				cachedIdentifier = identifier
				return
			}
		}

		// Fallback to the CURRENT_TIMESTAMP environment variable
		ts := strings.TrimSpace(os.Getenv("CURRENT_TIMESTAMP"))
		if ts != "" {
			cachedIdentifier = ts
			return
		}

		// If neither is available, set an error
		cachedError = errors.New("failed to obtain release identifier: neither identifier.txt nor CURRENT_TIMESTAMP provided a valid value")
	})

	return cachedIdentifier, cachedError
}

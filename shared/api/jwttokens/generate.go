package jwttokens

import (
	"crypto/rand"
	"crypto/rsa"
	"crypto/x509"
	"encoding/base64"
	"encoding/pem"
	"fmt"
)

// generateKeys creates a new RSA key pair, PEM-encodes them, and returns their Base64 strings.
// Use it for generating the public and private keys for the JWT_PRIVATE_KEY and JWT_PUBLIC_KEY env variables
func generateJwtKeys() (string, string, error) {
	// Generate an RSA private key.
	priv, err := rsa.GenerateKey(rand.Reader, 2048)
	if err != nil {
		return "", "", fmt.Errorf("failed to generate private key: %v", err)
	}

	// Marshal the private key to PKCS#1 DER format.
	privDER := x509.MarshalPKCS1PrivateKey(priv)
	// Create a PEM block for the private key.
	privBlock := &pem.Block{
		Type:  "RSA PRIVATE KEY",
		Bytes: privDER,
	}
	// PEM encode and then Base64 encode the private key.
	privPEM := pem.EncodeToMemory(privBlock)
	privBase64 := base64.StdEncoding.EncodeToString(privPEM)

	// Marshal the public key to PKIX, ASN.1 DER format.
	pubDER, err := x509.MarshalPKIXPublicKey(&priv.PublicKey)
	if err != nil {
		return "", "", fmt.Errorf("failed to marshal public key: %v", err)
	}
	// Create a PEM block for the public key.
	pubBlock := &pem.Block{
		Type:  "PUBLIC KEY",
		Bytes: pubDER,
	}
	// PEM encode and then Base64 encode the public key.
	pubPEM := pem.EncodeToMemory(pubBlock)
	pubBase64 := base64.StdEncoding.EncodeToString(pubPEM)

	return privBase64, pubBase64, nil
}

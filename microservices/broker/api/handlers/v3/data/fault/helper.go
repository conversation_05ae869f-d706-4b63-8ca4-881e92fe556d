package fault

import (
	"encoding/json"
	"time"

	ediHelper "synapse-its.com/shared/devices/edi/helper"
	devicesHelper "synapse-its.com/shared/devices/helper"
	"synapse-its.com/shared/schemas"
)

// helper to translate slices
var translateSlice = func(bools []bool, key string) []string {
	tr := ediHelper.EtlTranslators[key]
	out := make([]string, len(bools))
	for j, v := range bools {
		out[j] = tr.String(v)
	}
	return out
}

// ConvertSchemasToFault builds a UnifiedLogData from BigQuery schemas.
func ConvertSchemasTo16leip(
	messageTime time.Time,
	schemaName string,
	deviceID string,
	detail Logs_16leip_DeviceDetail,

	monitorReset schemas.LogMonitorReset,
	prevFail schemas.LogPreviousFail,
	config schemas.LogConfiguration,
	acEvent schemas.LogACLineEvent,
	faultSeq schemas.LogFaultSignalSequence,
) Logs_16leip_UnifiedLogData {
	// 1) Map LogMonitorReset
	mr := make([]Logs_16leip_LogMonitorResetRecord, len(monitorReset.Records))
	for i, r := range monitorReset.Records {
		mr[i] = Logs_16leip_LogMonitorResetRecord{
			DateTime:  r.EventTimestamp,
			ResetType: r.ResetType,
		}
	}

	// 2) Map LogPreviousFail
	pf := make([]Logs_16leip_LogPreviousFailRecord, len(prevFail.Records))
	for i, r := range prevFail.Records {
		pf[i] = Logs_16leip_LogPreviousFailRecord{
			DateTime:                          r.DateTime,
			Fault:                             r.Fault,
			ACLine:                            r.ACLine,
			T48VDCSignalBus:                   r.T48VDCSignalBus,
			RedEnable:                         r.RedEnable,
			MCCoilEE:                          r.MCCoilEE,
			SpecialFunction1:                  r.SpecialFunction1,
			SpecialFunction2:                  r.SpecialFunction2,
			WDTMonitor:                        r.WDTMonitor,
			T24VDCInput:                       r.T24VDCInput,
			Temperature:                       r.Temperature,
			LsFlashBit:                        ediHelper.EtlTranslators["LsFlashBit"].String(r.LsFlashBit),
			FaultStatus:                       translateSlice(r.FaultStatus, "FaultStatus"),
			ChannelGreenStatus:                translateSlice(r.ChannelGreenStatus, "ChannelGreenStatus"),
			ChannelYellowStatus:               translateSlice(r.ChannelYellowStatus, "ChannelYellowStatus"),
			ChannelRedStatus:                  translateSlice(r.ChannelRedStatus, "ChannelRedStatus"),
			ChannelWalkStatus:                 translateSlice(r.ChannelWalkStatus, "ChannelWalkStatus"),
			ChannelGreenFieldCheckStatus:      translateSlice(r.ChannelGreenFieldCheckStatus, "ChannelGreenFieldCheckStatus"),
			ChannelYellowFieldCheckStatus:     translateSlice(r.ChannelYellowFieldCheckStatus, "ChannelYellowFieldCheckStatus"),
			ChannelRedFieldCheckStatus:        translateSlice(r.ChannelRedFieldCheckStatus, "ChannelRedFieldCheckStatus"),
			ChannelWalkFieldCheckStatus:       translateSlice(r.ChannelWalkFieldCheckStatus, "ChannelWalkFieldCheckStatus"),
			ChannelGreenRecurrentPulseStatus:  translateSlice(r.ChannelGreenRecurrentPulseStatus, "ChannelGreenRecurrentPulseStatus"),
			ChannelYellowRecurrentPulseStatus: translateSlice(r.ChannelYellowRecurrentPulseStatus, "ChannelYellowRecurrentPulseStatus"),
			ChannelRedRecurrentPulseStatus:    translateSlice(r.ChannelRedRecurrentPulseStatus, "ChannelRedRecurrentPulseStatus"),
			ChannelWalkRecurrentPulseStatus:   translateSlice(r.ChannelWalkRecurrentPulseStatus, "ChannelWalkRecurrentPulseStatus"),
			ChannelGreenRMSVoltage:            r.ChannelGreenRmsVoltage,
			ChannelYellowRMSVoltage:           r.ChannelYellowRmsVoltage,
			ChannelRedRMSVoltage:              r.ChannelRedRmsVoltage,
			ChannelWalkRMSVoltage:             r.ChannelWalkRmsVoltage,
			NextConflictingChannels:           translateSlice(r.NextConflictingChannels, "NextConflictingChannels"),
			ChannelRedCurrentStatus:           translateSlice(r.ChannelRedCurrentStatus, "ChannelRedCurrentStatus"),
			ChannelYellowCurrentStatus:        translateSlice(r.ChannelYellowCurrentStatus, "ChannelYellowCurrentStatus"),
			ChannelGreenCurrentStatus:         translateSlice(r.ChannelGreenCurrentStatus, "ChannelGreenCurrentStatus"),
			ChannelRedRMSCurrent:              r.ChannelRedRmsCurrent,
			ChannelYellowRMSCurrent:           r.ChannelYellowRmsCurrent,
			ChannelGreenRMSCurrent:            r.ChannelGreenRmsCurrent,
		}
	}

	// 3) Map LogConfigurationChange with translation
	cc := make([]Logs_16leip_ConfigurationChangeLogRecord, len(config.Record))
	for i, r := range config.Record {
		// translate scalar bools
		translations, _ := devicesHelper.TranslateConfig(&r, ediHelper.EtlTranslators)

		cc[i] = Logs_16leip_ConfigurationChangeLogRecord{
			DateTime:        r.DateTime,
			Ch1Permissives:  r.Ch01Permissives,
			Ch2Permissives:  r.Ch02Permissives,
			Ch3Permissives:  r.Ch03Permissives,
			Ch4Permissives:  r.Ch04Permissives,
			Ch5Permissives:  r.Ch05Permissives,
			Ch6Permissives:  r.Ch06Permissives,
			Ch7Permissives:  r.Ch07Permissives,
			Ch8Permissives:  r.Ch08Permissives,
			Ch9Permissives:  r.Ch09Permissives,
			Ch10Permissives: r.Ch10Permissives,
			Ch11Permissives: r.Ch11Permissives,
			Ch12Permissives: r.Ch12Permissives,
			Ch13Permissives: r.Ch13Permissives,
			Ch14Permissives: r.Ch14Permissives,
			Ch15Permissives: r.Ch15Permissives,
			Ch16Permissives: r.Ch16Permissives,
			Ch17Permissives: r.Ch17Permissives,
			Ch18Permissives: r.Ch18Permissives,
			Ch19Permissives: r.Ch19Permissives,
			Ch20Permissives: r.Ch20Permissives,
			Ch21Permissives: r.Ch21Permissives,
			Ch22Permissives: r.Ch22Permissives,
			Ch23Permissives: r.Ch23Permissives,
			Ch24Permissives: r.Ch24Permissives,
			Ch25Permissives: r.Ch25Permissives,
			Ch26Permissives: r.Ch26Permissives,
			Ch27Permissives: r.Ch27Permissives,
			Ch28Permissives: r.Ch28Permissives,
			Ch29Permissives: r.Ch29Permissives,
			Ch30Permissives: r.Ch30Permissives,
			Ch31Permissives: r.Ch31Permissives,

			RedFailEnable:                   translateSlice(r.RedFailEnable, "RedFailEnable"),
			GreenYellowDualEnable:           translateSlice(r.GreenYellowDualEnable, "GreenYellowDualEnable"),
			YellowRedDualEnable:             translateSlice(r.YellowRedDualEnable, "YellowRedDualEnable"),
			GreenRedDualEnable:              translateSlice(r.GreenRedDualEnable, "GreenRedDualEnable"),
			MinimumYellowClearanceEnable:    translateSlice(r.MinimumYellowClearanceEnable, "MinimumYellowClearanceEnable"),
			MinimumYellowRedClearanceEnable: translateSlice(r.MinimumYellowRedClearanceEnable, "MinimumYellowRedClearanceEnable"),
			FieldCheckEnableGreen:           translateSlice(r.FieldCheckEnableGreen, "FieldCheckEnableGreen"),
			FieldCheckEnableYellow:          translateSlice(r.FieldCheckEnableYellow, "FieldCheckEnableYellow"),
			FieldCheckEnableRed:             translateSlice(r.FieldCheckEnableRed, "FieldCheckEnableRed"),
			YellowEnable:                    translateSlice(r.YellowEnable, "YellowEnable"),

			WalkEnableTs1:        translations["WalkEnableTs1"],
			RedFaultTiming:       r.RedFaultTiming,
			RecurrentPulse:       translations["RecurrentPulse"],
			WatchdogTiming:       r.WatchdogTiming,
			WatchdogEnableSwitch: translations["WatchdogEnableSwitch"],
			ProgramCardMemory:    translations["ProgramCardMemory"],

			GYEnable:         translations["GYEnable"],
			MinimumFlashTime: r.MinimumFlashTime,
			CvmLatchEnable:   translations["CvmLatchEnable"],
			LogCvmFaults:     translations["LogCvmFaults"],

			X24VIiInputThreshold: r.X24VIiInputThreshold,
			X24VLatchEnable:      translations["X24VLatchEnable"],
			X24VoltInhibit:       translations["X24VoltInhibit"],
			Port1Disable:         translations["Port_1Disable"],

			TypeMode:           r.TypeMode,
			LEDGuardThresholds: translations["LEDguardThresholds"],
			ForceType16Mode:    translations["ForceType_16Mode"],
			Type12WithSdlcMode: translations["Type_12WithSdlcMode"],

			VmCvm24V3XDayLatch:          translations["VmCvm_24V_3XdayLatch"],
			RedFailEnabledBySSM:         translations["RedFailEnabledBySSM"],
			DualIndicationFaultTiming:   r.DualIndicationFaultTiming,
			WDTErrorClearOnPU:           translations["WDTErrorClearOnPU"],
			MinimumFlash:                translations["MinimumFlash"],
			ConfigChangeFault:           translations["ConfigChangeFault"],
			RedCableFault:               translations["RedCableFault"],
			AcLineBrownout:              r.AcLineBrownout,
			PinEEPolarity:               r.PinEEPolarity,
			FlashingYellowArrows:        r.FlashingYellowArrows,
			FyaRedAndYellowEnable:       r.FyaRedAndYellowEnable,
			FyaRedAndGreenDisable:       r.FyaRedAndGreenDisable,
			FyaYellowTrapDetection:      translations["FyaYellowTrapDetection"],
			FYAFlashRateFault:           translations["FYAFlashRateFault"],
			FyaFlashRateDetection:       translations["FyaFlashRateDetection"],
			Pplt5Suppression:            r.Pplt5Suppression,
			CheckValue:                  r.CheckValue,
			ChangeSource:                r.ChangeSource,
			RedVirtualChannel:           convertVS(r.RedVirtualChannel),
			YellowVirtualChannel:        convertVS(r.YellowVirtualChannel),
			GreenVirtualChannel:         convertVS(r.GreenVirtualChannel),
			CurrentSenseRedEnabled:      translateSlice(r.CurrentSenseRedEnabled, "CurrentSenseRedEnabled"),
			CurrentSenseYellowEnabled:   translateSlice(r.CurrentSenseYellowEnabled, "CurrentSenseYellowEnabled"),
			CurrentSenseGreenEnabled:    translateSlice(r.CurrentSenseGreenEnabled, "CurrentSenseGreenEnabled"),
			CurrentSenseRedThreshold:    r.CurrentSenseRedThreshold,
			CurrentSenseYellowThreshold: r.CurrentSenseYellowThreshold,
			CurrentSenseGreenThreshold:  r.CurrentSenseGreenThreshold,
			DarkChannelX01:              r.DarkChannelX01,
			DarkChannelX02:              r.DarkChannelX02,
			DarkChannelX03:              r.DarkChannelX03,
			DarkChannelX04:              r.DarkChannelX04,
		}
	}

	// 4) Map LogAcLineEvent
	var voltageType string
	if acEvent.VoltageType == 1 {
		voltageType = "AC"
	} else {
		voltageType = "DC"
	}
	ae := make([]Logs_16leip_AcLineEventRecord, len(acEvent.Record))
	for i, r := range acEvent.Record {
		ae[i] = Logs_16leip_AcLineEventRecord{
			EventType:       r.EventType,
			DateTime:        r.DateTime,
			LineVoltageRms:  r.LineVoltageRms,
			LineFrequencyHz: r.LineFrequencyHz,
		}
	}

	// 5) Map LogFaultSignalSequence
	fsRecs := make([]Logs_16leip_FaultSignalSequenceRecord, len(faultSeq.Records))
	for i, seqRec := range faultSeq.Records {
		var buffers []Logs_16leip_FaultSignalBuffer
		// For each TraceBuffer (seqRec.Records), append one FaultSignalBuffer per channel bit
		for _, tb := range seqRec.Records {
			// tb.Reds (and tb.Yellows, tb.Greens, ...) are all length 16 slices
			for range tb.Reds {
				buffers = append(buffers, Logs_16leip_FaultSignalBuffer{
					BufferRawBytes: tb.BufferRawBytes,
					Timestamp:      tb.Timestamp,
					Reds:           tb.Reds,
					Yellow:         tb.Yellows,
					Greens:         tb.Greens,
					Walks:          tb.Walks,
					EeSfRe:         tb.EE_SF_RE,
					AcVoltage:      tb.AcVoltage,
				})
			}
		}
		fsRecs[i] = Logs_16leip_FaultSignalSequenceRecord{
			FaultType: seqRec.FaultType,
			Buffers:   buffers,
		}
	}

	return Logs_16leip_UnifiedLogData{
		MessageTime:  messageTime,
		DeviceID:     deviceID,
		Schema:       schemaName,
		DeviceDetail: detail,

		LogMonitorReset:        Logs_16leip_LogMonitorReset{Record: mr},
		LogPreviousFail:        Logs_16leip_LogPreviousFail{Record: pf},
		LogConfigurationChange: Logs_16leip_LogConfigurationChange{Record: cc},
		LogAcLineEvent:         Logs_16leip_LogAcLineEvent{Record: ae, VoltageType: voltageType},
		LogFaultSignalSequence: Logs_16leip_LogFaultSignalSequence{Record: fsRecs},
	}
}

// ReplaceNullsInJSON takes any JSON blob and replaces all JSON nulls
// with empty slices (and leaves other values intact), then pretty-prints.
func ReplaceNullsInJSON(input []byte) ([]byte, error) {
	var data interface{}
	if err := json.Unmarshal(input, &data); err != nil {
		return nil, err
	}

	cleaned := replaceNulls(data)
	return json.MarshalIndent(cleaned, "", "  ")
}

// replaceNulls recursively replaces JSON nulls with empty slices
func replaceNulls(v interface{}) interface{} {
	switch x := v.(type) {
	case map[string]interface{}:
		for k, val := range x {
			if val == nil {
				x[k] = []interface{}{}
			} else {
				x[k] = replaceNulls(val)
			}
		}
		return x

	case []interface{}:
		for i, item := range x {
			if item == nil {
				x[i] = []interface{}{}
			} else {
				x[i] = replaceNulls(item)
			}
		}
		return x

	default:
		return x
	}
}

func convertVS(src []schemas.VirtualSetting) []Logs_16leip_VirtualSetting {
	dst := make([]Logs_16leip_VirtualSetting, len(src))
	for i, v := range src {
		dst[i] = Logs_16leip_VirtualSetting{
			Color:         v.Color,
			Enabled:       v.Enabled,
			SourceChannel: v.SourceChannel,
			SourceColor:   v.SourceColor,
		}
	}
	return dst
}

// ConvertSchemasToECL2010FPlus builds the JSON structs for an ECL2010 F-Plus log.
func ConvertSchemasToECL2010FPlus(
	messageTime time.Time,
	schemaName string,
	deviceID string,
	detail *Logs_ECL2010_FPlus_LogData_DeviceDetail,

	monitor schemas.LogMonitorReset,
	prevFail schemas.LogPreviousFail,
	config schemas.LogConfiguration,
	acLine schemas.LogACLineEvent,
	faultSeq schemas.LogFaultSignalSequence,
) *Logs_ECL2010_FPlus {
	out := &Logs_ECL2010_FPlus{
		LogData: &Logs_ECL2010_FPlus_LogData{
			MessageTime:  messageTime,
			DeviceId:     deviceID,
			Schema:       schemaName,
			DeviceDetail: detail,
		},
	}

	// — LogMonitorReset
	mr := make([]*Logs_ECL2010_FPlus_LogData_LogMonitorReset_LogMonitorResetRecord, len(monitor.Records))
	for i, r := range monitor.Records {
		mr[i] = &Logs_ECL2010_FPlus_LogData_LogMonitorReset_LogMonitorResetRecord{
			Datetime: r.EventTimestamp,
		}
	}
	out.LogData.LogMonitorReset = &Logs_ECL2010_FPlus_LogData_LogMonitorReset{Record: mr}

	// — LogPreviousFail
	pf := make([]*Logs_ECL2010_FPlus_LogData_LogPreviousFail_LogPreviousFailRecord, len(prevFail.Records))
	for i, r := range prevFail.Records {
		pf[i] = &Logs_ECL2010_FPlus_LogData_LogPreviousFail_LogPreviousFailRecord{
			Datetime:                          r.DateTime,
			Fault:                             r.Fault,
			AcLine:                            r.ACLine,
			X48VdcSignalBus:                   r.T48VDCSignalBus,
			RedEnable:                         r.RedEnable,
			McCoilEe:                          r.MCCoilEE,
			SpecialFunction1:                  r.SpecialFunction1,
			SpecialFunction2:                  r.SpecialFunction2,
			WdtMonitor:                        r.WDTMonitor,
			X24VdcInput:                       r.T24VDCInput,
			Temperature:                       r.Temperature,
			FaultStatus:                       translateSlice(r.FaultStatus, "FaultStatus"),
			ChannelGreenStatus:                translateSlice(r.ChannelGreenStatus, "ChannelGreenStatus"),
			ChannelYellowStatus:               translateSlice(r.ChannelYellowStatus, "ChannelYellowStatus"),
			ChannelRedStatus:                  translateSlice(r.ChannelRedStatus, "ChannelRedStatus"),
			ChannelWalkStatus:                 translateSlice(r.ChannelWalkStatus, "ChannelWalkStatus"),
			ChannelGreenFieldCheckStatus:      translateSlice(r.ChannelGreenFieldCheckStatus, "ChannelGreenFieldCheckStatus"),
			ChannelYellowFieldCheckStatus:     translateSlice(r.ChannelYellowFieldCheckStatus, "ChannelYellowFieldCheckStatus"),
			ChannelRedFieldCheckStatus:        translateSlice(r.ChannelRedFieldCheckStatus, "ChannelRedFieldCheckStatus"),
			ChannelWalkFieldCheckStatus:       translateSlice(r.ChannelWalkFieldCheckStatus, "ChannelWalkFieldCheckStatus"),
			ChannelGreenRecurrentPulseStatus:  translateSlice(r.ChannelGreenRecurrentPulseStatus, "ChannelGreenRecurrentPulseStatus"),
			ChannelYellowRecurrentPulseStatus: translateSlice(r.ChannelYellowRecurrentPulseStatus, "ChannelYellowRecurrentPulseStatus"),
			ChannelRedRecurrentPulseStatus:    translateSlice(r.ChannelRedRecurrentPulseStatus, "ChannelRedRecurrentPulseStatus"),
			ChannelWalkRecurrentPulseStatus:   translateSlice(r.ChannelWalkRecurrentPulseStatus, "ChannelWalkRecurrentPulseStatus"),
			ChannelGreenRmsVoltage:            r.ChannelGreenRmsVoltage,
			ChannelYellowRmsVoltage:           r.ChannelYellowRmsVoltage,
			ChannelRedRmsVoltage:              r.ChannelRedRmsVoltage,
			ChannelWalkRmsVoltage:             r.ChannelWalkRmsVoltage,
		}
	}
	out.LogData.LogPreviousFail = &Logs_ECL2010_FPlus_LogData_LogPreviousFail{Record: pf}

	// — LogConfigurationChange
	cc := make([]*Logs_ECL2010_FPlus_LogData_LogConfigurationChange_ConfigurationChangeLogRecord, len(config.Record))
	for i, r := range config.Record {
		translations, _ := devicesHelper.TranslateConfig(&r, ediHelper.EtlTranslators)
		cc[i] = &Logs_ECL2010_FPlus_LogData_LogConfigurationChange_ConfigurationChangeLogRecord{
			Datetime:                     r.DateTime,
			Ch_1Permissives:              r.Ch01Permissives,
			Ch_2Permissives:              r.Ch02Permissives,
			Ch_3Permissives:              r.Ch03Permissives,
			Ch_4Permissives:              r.Ch04Permissives,
			Ch_5Permissives:              r.Ch05Permissives,
			Ch_6Permissives:              r.Ch06Permissives,
			Ch_7Permissives:              r.Ch07Permissives,
			Ch_8Permissives:              r.Ch08Permissives,
			Ch_9Permissives:              r.Ch09Permissives,
			Ch_10Permissives:             r.Ch10Permissives,
			Ch_11Permissives:             r.Ch11Permissives,
			Ch_12Permissives:             r.Ch12Permissives,
			Ch_13Permissives:             r.Ch13Permissives,
			Ch_14Permissives:             r.Ch14Permissives,
			Ch_15Permissives:             r.Ch15Permissives,
			RedFailEnable:                translateSlice(r.RedFailEnable, "RedFailEnable"),
			GreenYellowDualEnable:        translateSlice(r.GreenYellowDualEnable, "GreenYellowDualEnable"),
			YellowRedDualEnable:          translateSlice(r.YellowRedDualEnable, "YellowRedDualEnable"),
			GreenRedDualEnable:           translateSlice(r.GreenRedDualEnable, "GreenRedDualEnable"),
			MinimumYellowClearanceEnable: translateSlice(r.MinimumYellowClearanceEnable, "MinimumYellowClearanceEnable"),
			YellowDisable:                translateSlice(r.YellowEnable, "YellowEnable"),
			RedFaultTiming:               r.RedFaultTiming,
			RecurrentPulse:               translations["RecurrentPulse"],
			// …and any additional fields from your BigQuery config struct…
		}
	}
	out.LogData.LogConfigurationChange = &Logs_ECL2010_FPlus_LogData_LogConfigurationChange{Record: cc}

	// — LogACLineEvent
	ae := &Logs_ECL2010_FPlus_LogData_LogACLineEvent{
		VoltageType: acLine.VoltageType,
	}
	ae.Events = make([]*Logs_ECL2010_FPlus_LogData_LogACLineEvent_LogACLineEventRecord, len(acLine.Record))
	for i, r := range acLine.Record {
		ae.Events[i] = &Logs_ECL2010_FPlus_LogData_LogACLineEvent_LogACLineEventRecord{
			EventType:       r.EventType,
			Datetime:        r.DateTime,
			LineVoltageVrms: r.LineVoltageRms,
			LineFrequencyHz: r.LineFrequencyHz,
		}
	}
	out.LogData.LogAcLineEvent = ae

	// — LogFaultSignalSequence
	// — LogFaultSignalSequence (updated for new nested schema)
	fsOut := make([]*Logs_ECL2010_FPlus_LogData_LogFaultSignalSequence_LogFaultSignalSequenceRecord, len(faultSeq.Records))
	for i, seqRec := range faultSeq.Records {
		buf := make([]*Logs_ECL2010_FPlus_LogData_LogFaultSignalSequence_LogFaultSignalSequenceRecord_TraceLogBuffer, len(seqRec.Records))
		for j, tb := range seqRec.Records {
			buf[j] = &Logs_ECL2010_FPlus_LogData_LogFaultSignalSequence_LogFaultSignalSequenceRecord_TraceLogBuffer{
				BufferRawBytes: tb.BufferRawBytes,
				Timestamp:      tb.Timestamp,
				Reds:           tb.Reds,
				Yellows:        tb.Yellows,
				Greens:         tb.Greens,
				Walks:          tb.Walks,
				EeSfRe:         tb.EE_SF_RE,
				AcVoltage:      tb.AcVoltage,
			}
		}

		fsOut[i] = &Logs_ECL2010_FPlus_LogData_LogFaultSignalSequence_LogFaultSignalSequenceRecord{
			FaultType: seqRec.FaultType,
			Buffers:   buf,
		}
	}
	out.LogData.LogFaultSignalSequence = &Logs_ECL2010_FPlus_LogData_LogFaultSignalSequence{Record: fsOut}

	return out
}

// ConvertSchemasToCMU2212Base builds the JSON structs for a CMU2212-Base log.
func ConvertSchemasToCMU2212Base(
	messageTime time.Time,
	schemaName string,
	deviceID string,
	detail *Logs_CMU2212_Base_LogData_DeviceDetail,

	monitor schemas.LogMonitorReset,
	prevFail schemas.LogPreviousFail,
	config schemas.LogConfiguration,
	acLine schemas.LogACLineEvent,
	faultSeq schemas.LogFaultSignalSequence,
) *Logs_CMU2212_Base {
	out := &Logs_CMU2212_Base{
		LogData: &Logs_CMU2212_Base_LogData{
			MessageTime:  messageTime,
			DeviceId:     deviceID,
			Schema:       schemaName,
			DeviceDetail: detail,
		},
	}

	// — LogMonitorReset
	mr := make([]*Logs_CMU2212_Base_LogData_LogMonitorReset_LogMonitorResetRecord, len(monitor.Records))
	for i, r := range monitor.Records {
		mr[i] = &Logs_CMU2212_Base_LogData_LogMonitorReset_LogMonitorResetRecord{
			Datetime: r.EventTimestamp,
		}
	}
	out.LogData.LogMonitorReset = &Logs_CMU2212_Base_LogData_LogMonitorReset{Record: mr}

	// — LogPreviousFail
	pf := make([]*Logs_CMU2212_Base_LogData_LogPreviousFail_LogPreviousFailRecord, len(prevFail.Records))
	for i, r := range prevFail.Records {
		pf[i] = &Logs_CMU2212_Base_LogData_LogPreviousFail_LogPreviousFailRecord{
			Datetime: r.DateTime,
			Fault:    r.Fault,
			AcLine:   r.ACLine,
		}
	}
	out.LogData.LogPreviousFail = &Logs_CMU2212_Base_LogData_LogPreviousFail{Record: pf}

	// — LogConfigurationChange
	cc := make([]*Logs_CMU2212_Base_LogData_LogConfigurationChange_ConfigurationChangeLogRecord, len(config.Record))
	for i, r := range config.Record {
		cc[i] = &Logs_CMU2212_Base_LogData_LogConfigurationChange_ConfigurationChangeLogRecord{
			Datetime: r.DateTime,
			// no other fields in this schema
		}
	}
	out.LogData.LogConfigurationChange = &Logs_CMU2212_Base_LogData_LogConfigurationChange{Record: cc}

	// — LogACLineEvent
	ae := &Logs_CMU2212_Base_LogData_LogACLineEvent{
		VoltageType: acLine.VoltageType,
	}
	ae.Events = make([]*Logs_CMU2212_Base_LogData_LogACLineEvent_LogACLineEventRecord, len(acLine.Record))
	for i, r := range acLine.Record {
		ae.Events[i] = &Logs_CMU2212_Base_LogData_LogACLineEvent_LogACLineEventRecord{
			Datetime:        r.DateTime,
			EventType:       r.EventType,
			LineVoltageVrms: r.LineVoltageRms,
		}
	}
	out.LogData.LogAcLineEvent = ae

	// — LogFaultSignalSequence
	// CMU2212_Base has an empty record struct; we skip any buffers.
	out.LogData.LogFaultSignalSequence = &Logs_CMU2212_Base_LogData_LogFaultSignalSequence{
		Record: []*Logs_CMU2212_Base_LogData_LogFaultSignalSequence_LogFaultSignalSequenceRecord{},
	}

	return out
}

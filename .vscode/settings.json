{
  "editor.tabSize": 2,
  "editor.insertSpaces": true,
  "editor.detectIndentation": false,
  "files.eol": "\n",
  "editor.formatOnSave": true,
  "editor.trimAutoWhitespace": true,
  "terminal.integrated.copyOnSelection": true,
  "[dockerfile]": {
    "editor.formatOnSave": false,
    "editor.defaultFormatter": null
  },
  "go.delveConfig": {
    "substitutePath": [
      {
        "to": "/shared",
        "from": "/workspace/shared"
      },
      {
        "to": "/schemas",
        "from": "/workspace/schemas"
      }
    ],
  }
}
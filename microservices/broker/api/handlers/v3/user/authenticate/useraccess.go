// Package authenticate encapsulates the logic dealing with user access requests
package authenticate

import (
	"database/sql"
	"errors"
	"net/http"
	"time"

	jwttokens "synapse-its.com/shared/api/jwttokens"
	response "synapse-its.com/shared/api/response"
	security "synapse-its.com/shared/api/security"
	connect "synapse-its.com/shared/connect"
	logger "synapse-its.com/shared/logger"
)

// PasswordHasher hashes passwords
type PasswordHasher func(password string) string

// UserPermissionsCreator creates user permissions
type UserPermissionsCreator func(pg connect.DatabaseExecutor, userID int64) (*jwttokens.UserPermissions, error)

// TimeProvider provides the current time
type TimeProvider func() time.Time

// NonceGenerator generates a nonce
type NonceGenerator func(length int) ([]byte, error)

// User<PERSON>and<PERSON> handles user authentication
type UserHandler struct {
	DBProvider             ConnectionProvider
	PasswordHasher         PasswordHasher
	JwtCreator             JWTCreator
	TokenPersister         TokenPersister
	ValidateRoleFunc       RoleValidator
	UserPermissionsCreator UserPermissionsCreator
	TimeProvider           TimeProvider
	NonceGenerator         NonceGenerator
}

// UserAccess takes a username and password and returns a payload which includes a jwt to the caller
func (h *UserHandler) UserAccess(userName, password string, w http.ResponseWriter, r *http.Request) {
	// Create hash of the password - all passwords are stored hashed in the db
	hashedPassword := h.PasswordHasher(password)

	// Get the postgres connection
	connections, err := h.DBProvider(r.Context())
	if err != nil {
		response.CreateInternalErrorResponse(w)
		logger.Errorf("%v", err) // Database connection errors are ERROR level
		return
	}

	pg := connections.Postgres

	// Lookup the username in the db
	query := `
		SELECT
			u.Id,
			u.TokenDurationHours,
			o.OrganizationIdentifier,
			u.RoleId,
			o.APIKey
		FROM {{User}} u
		LEFT JOIN {{Organization}} o
			ON u.OrganizationId = o.Id
		WHERE u.username = $1 AND u.password = $2 AND u.IsEnabled = 1`

	// Use QueryRowStruct with the dbUser struct
	user := dbUser{}
	err = pg.QueryRowStruct(&user, query, userName, hashedPassword)

	switch {
	case errors.Is(err, sql.ErrNoRows):
		// Username and password combo not found -- update the failed attempts for the user name and return not-authorized
		// Could be the username is bogus -- that's fine - no records will be updated
		query = `
			UPDATE
				{{User}}
			SET FailedLoginAttempts = FailedLoginAttempts + 1,
				IsEnabled =
					CASE
						WHEN FailedLoginAttempts >= 3 THEN 0
						ELSE IsEnabled
					END
			WHERE UserName = $1`
		_, err = pg.Exec(query, userName)

		logger.Warnf("%v: username: %s, err: %v", ErrInvalidCredentials, userName, err) // Authentication failures are WARN level
		response.CreateUnauthorizedResponse(w)
		return
	case err != nil:
		logger.Errorf("%v: %v", ErrDatabaseQuery, err) // Database query errors are ERROR level
		response.CreateUnauthorizedResponse(w)
		return
	default:
		var roleName string

		// Use the struct fields directly instead of GetColumn calls
		userID := user.Id
		tokenExpirationInHours := user.TokenDurationHours
		organizationID := user.OrganizationIdentifier
		roleID := user.RoleId
		apiKey := user.APIKey

		if roleName, err = h.ValidateRoleFunc(roleID); err != nil {
			logger.Errorf("%v: %v", ErrRoleValidation, err) // Role validation errors are ERROR level
			response.CreateUnauthorizedResponse(w)
			return
		}

		// Reset failed login attempts and update last login time
		query = "UPDATE {{User}} SET FailedLoginAttempts = 0, LastLoginUTC = $1 WHERE UserName = $2"
		_, err = pg.Exec(query, h.TimeProvider().UTC(), userName)
		if err != nil {
			logger.Warnf("%v: user %s, err: %v", ErrLoginTimeUpdate, userName, err) // Non-critical errors are WARN level
			// Continue despite this error - it's not critical
		}

		// Create user detail record
		userDetail := &userDetailRecord{}
		userDetail.UserID = userID
		userDetail.OrganizationID = organizationID
		userDetail.Username = userName
		userDetail.APIKey = apiKey
		userDetail.Role = roleName

		// Get user permissions
		var userPermissions *jwttokens.UserPermissions
		if userPermissions, err = h.UserPermissionsCreator(pg, userID); err != nil {
			logger.Errorf("%v: user ID %d, err: %v", ErrUserPermissions, userID, err) // User permissions errors are ERROR level
			response.CreateUnauthorizedResponse(w)
			return
		}

		// Create JWT token
		duration := time.Duration(tokenExpirationInHours) * time.Hour
		jwt, expiresAt, err := h.JwtCreator(userName, duration, roleID, *userPermissions)
		if err != nil {
			logger.Errorf("%v: %v", ErrJWTCreation, err) // JWT creation errors are ERROR level
			response.CreateUnauthorizedResponse(w)
			return
		}

		// Persist token to database
		err = h.TokenPersister(pg, userID, jwt, expiresAt)
		if err != nil {
			logger.Errorf("%v: %v", ErrTokenPersistence, err) // Token persistence errors are ERROR level
			response.CreateUnauthorizedResponse(w)
			return
		}

		// Create and send response
		jwtMsg := &dataUserResponsePayload{}
		jwtMsg.User = *userDetail
		jwtMsg.Token = jwt
		response.CreateAuthSuccessResponse(jwtMsg, w)
		return
	}
}

// NewUserHandler creates a new UserHandler with default dependencies
func NewUserHandler() *UserHandler {
	return &UserHandler{
		DBProvider:             connect.GetConnections,
		PasswordHasher:         security.CalculateSHA256,
		JwtCreator:             jwttokens.CreateJwtTokenUsingDuration,
		TokenPersister:         persistTokenToDB,
		ValidateRoleFunc:       jwttokens.ValidateRoleInt,
		UserPermissionsCreator: createUserPermissions,
		TimeProvider:           time.Now,
		NonceGenerator:         generateNonce,
	}
}

package faultNotification

import (
	"context"
	"errors"
	"testing"
	"time"

	gatewayv1 "bitbucket.org/synapse-its/protobuf-schemas/protomessages/gateway/v1"
	"cloud.google.com/go/pubsub"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/proto"
	"synapse-its.com/shared/bqbatch"
	connect "synapse-its.com/shared/connect"
	edihelper "synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/mocks"
	"synapse-its.com/shared/pubsubdata"
)

// --- defaultUpsertDeviceFault table tests ---

func TestDefaultUpsertDeviceFault(t *testing.T) {
	now := time.Now().UTC()
	rec := &edihelper.RmsStatusRecord{
		IsFaulted:           true,
		Fault:               "No Error",
		FaultStatus:         "OK",
		ChannelGreenStatus:  []bool{true, false},
		ChannelYellowStatus: []bool{false, true},
		ChannelRedStatus:    []bool{false, false},
		MonitorTime:         now,
		Temperature:         42,
		VoltagesGreen:       []int64{1, 1},
		VoltagesYellow:      []int64{2, 2},
		VoltagesRed:         []int64{3, 3},
		DeviceModel:         "EDIMMU16LE",
	}

	cases := []struct {
		name       string
		enableFail bool
		failAfter  int
		wantErr    bool
	}{
		{"success", false, 0, false},
		{"db error", true, 0, true},
	}

	for _, tc := range cases {
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()

			// Setup fake database executor
			fdb := &mocks.FakeDBExecutor{}
			if tc.enableFail {
				fdb.EnableFailAfter = true
				fdb.ExecCallFailAfter = tc.failAfter
			}

			// Execute function under test
			err := upsertDeviceFault(fdb, "device-123", rec)

			// Assert results
			if tc.wantErr {
				assert.Error(t, err, "expected error but got nil")
				assert.Error(t, err, "should return wrapped upsert error")
			} else {
				assert.NoError(t, err, "unexpected error")
				assert.Equal(t, 1, fdb.ExecCallCount, "should execute query exactly once")
			}
		})
	}
}

// --- HandlerWithDeps table tests ---

func TestHandlerWithDeps(t *testing.T) {
	ctx := context.Background()
	now := time.Now()

	cases := []struct {
		name         string
		connErr      error
		recvErr      error
		attrErr      error
		unmarshalErr error
		batchErr     error
		processErr   bool
		batchAddErr  error
		upsertErr    bool
		marshalErr   error
		dlqErr       error

		wantDLQ       int
		wantExecCalls int
		wantBatchAdds int
	}{
		{
			name:    "connector error",
			connErr: errors.New("no conn"),
			wantDLQ: 0, wantExecCalls: 0, wantBatchAdds: 0,
		},
		{
			name:    "receive error",
			recvErr: errors.New("recv fail"),
			wantDLQ: 0, wantExecCalls: 0, wantBatchAdds: 0,
		},
		{
			name:    "attribute parse error",
			attrErr: errors.New("fail attr parse"),
			wantDLQ: 1, wantExecCalls: 0, wantBatchAdds: 0,
		},
		{
			name:         "unmarshal error",
			unmarshalErr: errors.New("bad proto"),
			wantDLQ:      1, wantExecCalls: 0, wantBatchAdds: 0,
		},
		{
			name:         "get batch error",
			unmarshalErr: nil,
			batchErr:     errors.New("no batch"),
			// on GetBatch error we Nack but do not DLQ
			wantDLQ: 0, wantExecCalls: 0, wantBatchAdds: 0,
		},
		{
			name:       "process error",
			processErr: true,
			wantDLQ:    1, wantExecCalls: 0, wantBatchAdds: 0,
		},
		{
			name:        "batch add error",
			batchAddErr: errors.New("add fail"),
			wantDLQ:     1, wantExecCalls: 1, wantBatchAdds: 0,
		},
		{
			name:      "upsert error",
			upsertErr: true,
			wantDLQ:   1, wantExecCalls: 1, wantBatchAdds: 0,
		},
		{
			name:        "marshal error",
			batchAddErr: errors.New("any"),
			marshalErr:  errors.New("marshal fail"),
			wantDLQ:     0, wantExecCalls: 1, wantBatchAdds: 0,
		},
		{
			name:    "JSON parse + DLQ error", // TODO: check for nack
			attrErr: errors.New("fail attr parse"),
			dlqErr:  errors.New("dlq failed"),
			wantDLQ: 1, wantExecCalls: 0, wantBatchAdds: 0,
		},
		{
			name:         "proto unmarshal + DLQ error", // TODO: check for nack
			unmarshalErr: errors.New("bad proto"),
			dlqErr:       errors.New("dlq failed"),
			wantDLQ:      1, wantExecCalls: 0, wantBatchAdds: 0,
		},
		{
			name:        "final DLQ error path", // TODO: check for ack
			batchAddErr: errors.New("any"),      // forces unprocessed path
			dlqErr:      errors.New("dlq failed"),
			wantDLQ:     1, wantExecCalls: 1, wantBatchAdds: 0,
		},
		{
			name:    "happy path",
			wantDLQ: 0, wantExecCalls: 1, wantBatchAdds: 1,
		},
	}

	for _, tc := range cases {
		tc := tc
		t.Run(tc.name, func(t *testing.T) {
			// --- setup fakes ---
			conns := mocks.FakeConns()
			psc := conns.Pubsub.(*mocks.FakePubsubClient)
			psc.ReceiveError = tc.recvErr

			// create subscription
			topic := psc.Topic("topic-" + tc.name)
			_, err := psc.CreateSubscription(ctx, "sub-"+tc.name, connect.SubscriptionConfig{Topic: topic})
			if err != nil {
				t.Fatal(err)
			}

			// publish message if connector & receive ok
			if tc.connErr == nil && tc.recvErr == nil {
				msg := &pubsub.Message{ID: "m-" + tc.name, Data: []byte{1, 2, 3}, PublishTime: now}
				topic.Publish(ctx, msg)
			}

			// swap in FakeDBExecutor and configure for upsert error
			fdb := &mocks.FakeDBExecutor{}
			if tc.upsertErr {
				fdb.EnableFailAfter = true
				fdb.ExecCallFailAfter = 0
			}
			conns.Postgres = fdb

			// fake batcher
			added := 0
			fakeBatch, _ := mocks.FakeBatch(ctx)
			fakeBatch.(*mocks.FakeBatcher).AddFn = func(row interface{}) error {
				if tc.batchAddErr != nil {
					return tc.batchAddErr
				}
				added++
				return nil
			}

			// collect DLQ calls
			dlq := 0

			// --- build deps ---
			deps := HandlerDeps{
				Connector: func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
					return conns, tc.connErr
				},
				SendToDLQ: func(ctx context.Context, client connect.PsClient, m *pubsub.Message, reason string) error {
					dlq++
					return tc.dlqErr
				},
				UnmarshalDevice: func(_ []byte) (*gatewayv1.DeviceData, error) {
					if tc.unmarshalErr != nil {
						return nil, tc.unmarshalErr
					}
					// default single‐message
					return &gatewayv1.DeviceData{Messages: []*gatewayv1.DeviceEntry{
						{DeviceId: "dev1", Message: []byte("p")},
					}}, nil
				},
				GetBatch: func(ctx context.Context) (bqbatch.Batcher, error) {
					return fakeBatch, tc.batchErr
				},
				ProcessRMS: func(_ *pubsubdata.HeaderDetails, _ []byte) (*edihelper.RmsStatusRecord, *edihelper.HeaderRecord, error) {
					if tc.processErr {
						return nil, nil, errors.New("proc fail")
					}
					return &edihelper.RmsStatusRecord{
							ChannelGreenStatus:  []bool{true},
							ChannelYellowStatus: []bool{false},
							ChannelRedStatus:    []bool{true},
							MonitorTime:         time.Now().UTC(),
							Temperature:         0,
							VoltagesGreen:       []int64{},
							VoltagesYellow:      []int64{},
							VoltagesRed:         []int64{},
							DeviceModel:         "M",
						},
						&edihelper.HeaderRecord{}, nil
				},
				ToBQ:          edihelper.RmsStatusToFaultNotification,
				UpsertDevice:  upsertDeviceFault,
				MarshalDevice: func(msg proto.Message) ([]byte, error) { return []byte{1, 2, 3}, tc.marshalErr },
				ParseAttributes: func(attrs map[string]string) (pubsubdata.CommonAttributes, pubsubdata.HeaderDetails, error) {
					if tc.attrErr != nil {
						return pubsubdata.CommonAttributes{}, pubsubdata.HeaderDetails{}, tc.attrErr
					}
					return pubsubdata.CommonAttributes{}, pubsubdata.HeaderDetails{}, nil
				},
			}

			// --- invoke handler ---
			h := HandlerWithDeps(deps)
			h(ctx, "sub-"+tc.name)

			// --- asserts ---
			assert.Equal(t, tc.wantDLQ, dlq, "DLQ calls should match expected")
			assert.Equal(t, tc.wantExecCalls, fdb.ExecCallCount, "ExecCallCount should match expected")
			assert.Equal(t, tc.wantBatchAdds, added, "batch.Add calls should match expected")
		})
	}
}

# healthz

---

## Overview

The `healthz` package provides HTTP liveness (`/livez`), startup (`/startupz`), and readiness (`/readyz`) endpoints backed by atomic flags, suitable for Kubernetes and health checking.

---

## Package Layout

```
shared/
├── healthz/
│   └── healthz.go
```

---

## Features

- **Liveness Probe**: `/livez` always returns 200 OK  
- **Startup Probe**: `/startupz` returns 503 until `SetBootComplete()` is called  
- **Readiness Probe**: `/readyz` returns 503 until `SetReady()` is called  
- **Graceful Shutdown**: `Shutdown(ctx)` with error propagation  
- **Configurable Port**: `Start(port)` with default `:8081` and robust port validation  

---

## Usage

```go
func main() {
    // Start on default port :8081 or use os.Getenv("HEALTH_PORT")
    healthz.Start("")

    // Application boot logic...
    healthz.SetBootComplete()
    // Application ready logic...
    healthz.SetReady()

    // Block or serve main HTTP
    select {}

    // On exit, shutdown healthz
    ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
    defer cancel()
    if err := healthz.Shutdown(ctx); err != nil {
        log.Fatalf("healthz shutdown failed: %v", err)
    }
}
```

---

## API Reference

```go
func Start(port string)
func Shutdown(ctx context.Context) error
func SetBootComplete()
func SetReady()
func SetNotReady()
```

---

## Internals

- **Atomic Flags**: `bootComplete`, `readyToServe`  
- **HTTP Server**: `http.Server` with 2s read/write timeouts  
- **Router**: package-level `http.ServeMux`  
- **Error Channel**: captures `ListenAndServe` errors  

---

## Example

```bash
curl -i http://localhost:8081/livez
curl -i http://localhost:8081/startupz
curl -i http://localhost:8081/readyz
```

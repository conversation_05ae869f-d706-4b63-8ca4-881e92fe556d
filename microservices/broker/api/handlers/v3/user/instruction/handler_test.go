package instruction

import (
	"context"
	"database/sql"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/api/authorizer"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/mocks/dbexecutor"
)

// FakeResult implements sql.Result for testing
type FakeResult struct {
	AffectedRows    int64
	RowsAffectedErr error
}

func (r *FakeResult) LastInsertId() (int64, error) {
	return 0, nil
}

func (r *FakeResult) RowsAffected() (int64, error) {
	if r.RowsAffectedErr != nil {
		return 0, r.RowsAffectedErr
	}
	return r.AffectedRows, nil
}

func Test_HandlerWithDeps(t *testing.T) {
	// Define common mock data
	validRequestBody := `{"instruction": "get_device_logs", "device_id": 123}`
	invalidInstructionBody := `{"instruction": "invalid_instruction", "device_id": 123}`
	invalidJSONBody := `{"instruction": "get_device_logs", "device_id": 123`

	t.Parallel()

	tests := []struct {
		name           string
		setupMocks     func(*dbexecutor.FakeDBExecutor)
		requestBody    string
		expectedStatus int
		expectedBody   map[string]interface{}
	}{
		{
			name: "successful_instruction_insertion",
			setupMocks: func(mdb *dbexecutor.FakeDBExecutor) {
				mdb.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return &FakeResult{AffectedRows: 1}, nil
				}
			},
			requestBody:    validRequestBody,
			expectedStatus: http.StatusOK,
			expectedBody: map[string]interface{}{
				"status":  "success",
				"data":    "",
				"message": "",
				"code":    float64(http.StatusOK),
			},
		},
		{
			name: "user_info_not_found",
			setupMocks: func(mdb *dbexecutor.FakeDBExecutor) {
				// No setup needed as we'll mock UserInfoFromContext to return false
			},
			requestBody:    validRequestBody,
			expectedStatus: http.StatusInternalServerError,
			expectedBody: map[string]interface{}{
				"status":  "error",
				"data":    nil,
				"message": "Internal server error",
				"code":    float64(http.StatusInternalServerError),
			},
		},
		{
			name: "invalid_instruction",
			setupMocks: func(mdb *dbexecutor.FakeDBExecutor) {
				// No setup needed as we'll test invalid instruction validation
			},
			requestBody:    invalidInstructionBody,
			expectedStatus: http.StatusInternalServerError,
			expectedBody: map[string]interface{}{
				"status":  "error",
				"data":    nil,
				"message": "Internal server error",
				"code":    float64(http.StatusInternalServerError),
			},
		},
		{
			name: "invalid_json_body",
			setupMocks: func(mdb *dbexecutor.FakeDBExecutor) {
				// No setup needed as we'll test invalid JSON parsing
			},
			requestBody:    invalidJSONBody,
			expectedStatus: http.StatusInternalServerError,
			expectedBody: map[string]interface{}{
				"status":  "error",
				"data":    nil,
				"message": "Internal server error",
				"code":    float64(http.StatusInternalServerError),
			},
		},
		{
			name: "connection_error",
			setupMocks: func(mdb *dbexecutor.FakeDBExecutor) {
				// No setup needed as we'll mock GetConnections to return error
			},
			requestBody:    validRequestBody,
			expectedStatus: http.StatusInternalServerError,
			expectedBody: map[string]interface{}{
				"status":  "error",
				"data":    nil,
				"message": "Internal server error",
				"code":    float64(http.StatusInternalServerError),
			},
		},
		{
			name: "insert_instruction_db_error",
			setupMocks: func(mdb *dbexecutor.FakeDBExecutor) {
				mdb.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return nil, assert.AnError
				}
			},
			requestBody:    validRequestBody,
			expectedStatus: http.StatusInternalServerError,
			expectedBody: map[string]interface{}{
				"status":  "error",
				"data":    nil,
				"message": "Internal server error",
				"code":    float64(http.StatusInternalServerError),
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create mock instances
			mockDB := new(dbexecutor.FakeDBExecutor)

			// Setup mocks
			tt.setupMocks(mockDB)

			// Create handler with mocked dependencies
			handler := HandlerWithDeps(HandlerDeps{
				UserInfoFromContext: func(ctx context.Context) (*authorizer.UserInfo, bool) {
					if tt.name == "user_info_not_found" {
						return nil, false
					}
					return &authorizer.UserInfo{ID: 1}, true
				},
				GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					if tt.name == "connection_error" {
						return nil, assert.AnError
					}
					return &connect.Connections{Postgres: mockDB}, nil
				},
				ParseRequest:      parseRequest,
				InsertInstruction: insertInstruction,
			})

			// Create test request
			reqBodyReader := strings.NewReader(tt.requestBody)
			req := httptest.NewRequest(http.MethodPost, "/instruction", reqBodyReader)
			w := httptest.NewRecorder()

			// Execute request
			handler(w, req)

			// Assert response
			assert.Equal(t, tt.expectedStatus, w.Code)
		})
	}
}

func Test_parseRequest(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name          string
		requestBody   string
		expectedError error
	}{
		{
			name:          "valid_request",
			requestBody:   `{"instruction": "get_device_logs", "device_id": 123}`,
			expectedError: nil,
		},
		{
			name:          "invalid_instruction",
			requestBody:   `{"instruction": "invalid_instruction", "device_id": 123}`,
			expectedError: ErrInvalidInstructionRequest,
		},
		{
			name:          "invalid_json",
			requestBody:   `{"instruction": "get_device_logs", "device_id": 123`,
			expectedError: ErrDecodeRequest,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			req := httptest.NewRequest(http.MethodPost, "/instruction", strings.NewReader(tt.requestBody))
			result, err := parseRequest(req)

			if tt.expectedError != nil {
				assert.ErrorIs(t, err, tt.expectedError)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, "get_device_logs", result.Instruction)
				assert.Equal(t, int64(123), result.DeviceID)
			}
		})
	}
}

func Test_insertInstruction(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name          string
		userID        int64
		instruction   string
		deviceID      int64
		setupMocks    func(*dbexecutor.FakeDBExecutor)
		expectedError error
	}{
		{
			name:        "successful_insertion",
			userID:      1,
			instruction: "get_device_logs",
			deviceID:    123,
			setupMocks: func(mdb *dbexecutor.FakeDBExecutor) {
				mdb.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return &FakeResult{AffectedRows: 1}, nil
				}
			},
			expectedError: nil,
		},
		{
			name:        "exec_error",
			userID:      1,
			instruction: "get_device_logs",
			deviceID:    123,
			setupMocks: func(mdb *dbexecutor.FakeDBExecutor) {
				mdb.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return nil, assert.AnError
				}
			},
			expectedError: assert.AnError,
		},
		{
			name:        "rows_affected_error",
			userID:      1,
			instruction: "get_device_logs",
			deviceID:    123,
			setupMocks: func(mdb *dbexecutor.FakeDBExecutor) {
				mdb.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return &FakeResult{RowsAffectedErr: assert.AnError}, nil
				}
			},
			expectedError: assert.AnError,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			mockDB := new(dbexecutor.FakeDBExecutor)
			tt.setupMocks(mockDB)

			err := insertInstruction(mockDB, tt.userID, tt.instruction, tt.deviceID)
			if tt.expectedError != nil {
				assert.ErrorIs(t, err, tt.expectedError)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

# Synapse ITS Go Microservices - Coding Rules and Style Guide

## Project Overview

This is a Go-based microservices architecture project with the following structure:
- **Microservices**: Individual services (broker, coordinator, etl, onramp, testing)
- **Shared Libraries**: Common functionality across services (api, bqbatch, connect, devices, etc.)
- **Infrastructure**: Docker, BigQuery, PostgreSQL, Redis, PubSub integration

## 1. Project Structure and Organization

### 1.1 Directory Structure
```
/
├── microservices/          # Individual microservices
│   ├── broker/            # Message broker service
│   ├── coordinator/       # Coordination service
│   ├── etl/              # Extract, Transform, Load service
│   ├── onramp/           # Data ingestion service
│   └── testing/          # Testing utilities
├── shared/               # Shared libraries and utilities
│   ├── api/             # API utilities (middleware, response, auth)
│   ├── bqbatch/         # BigQuery batch operations
│   ├── connect/         # Database and service connections
│   ├── devices/         # Device-specific logic
│   ├── healthz/         # Health check utilities
│   ├── logger/          # Centralized logging
│   └── ...
├── schemas/             # Database schemas
├── infra/              # Infrastructure configuration
└── scripts/            # Build and deployment scripts
```

### 1.2 Module Organization
- Each microservice has its own `go.mod` file
- Shared libraries have individual `go.mod` files
- Use local path replacements for shared dependencies
- Domain-specific packages are grouped logically (e.g., `api/handlers/v3/data/device/`)

## 2. Naming Conventions

### 2.1 Package Names
- **Lowercase, single word**: `logger`, `connect`, `bqbatch`
- **Descriptive and concise**: `healthz` (not `health`), `pubsubdata`
- **No underscores or hyphens**: Use camelCase for multi-word concepts

### 2.2 Variable Names
- **camelCase**: `timeSleep`, `healthzNewServer`, `loggerFatalf`
- **Descriptive**: `maxRetries`, `backoff`, `connStr`
- **Abbreviations allowed for common terms**: `ctx` (context), `db` (database), `srv` (server)
- **Interface variables end with interface name**: `exec BigQueryExecutorInterface`

### 2.3 Function Names
- **PascalCase for exported**: `NewConnections`, `GetConnections`, `DefaultServer`
- **camelCase for unexported**: `parseRequest`, `getPgDeviceInfo`, `getRedisDeviceStatus`
- **Descriptive action verbs**: `Register`, `Add`, `Shutdown`, `Close`
- **Constructor pattern**: `New*` for constructors, `Default*` for default implementations

### 2.4 Type Names
- **PascalCase**: `Server`, `Connections`, `DatabaseExecutor`
- **Interface suffix**: `DatabaseExecutor`, `BigQueryExecutorInterface`, `PsClient`
- **Struct names are nouns**: `Batcher`, `QueueConfig`, `FailedBatch`

### 2.5 Constant Names
- **PascalCase for exported**: `ConnectionsKey`, `BatchKey`, `PubsubDLQTopic`
- **ALL_CAPS for package-level config**: `MaxRows`, `MaxWait`, `MaxAttempts`

## 3. Code Structure and Patterns

### 3.1 Main Function Pattern
```go
func main() {
    Run(
        context.Background(),
        os.Getenv("HEALTH_PORT"),
        ":8080",
        connect.NewConnections,
        bqbatch.New,
        DefaultServer,
        DefaultSignalChan,
    )
}
```
- Keep `main()` minimal - delegate to testable `Run()` function
- Use dependency injection for testability
- Pass functions as parameters for mocking

### 3.2 Dependency Injection Pattern
```go
func Run(
    ctx context.Context,
    healthPort string,
    addr string,
    newConns func(context.Context) *connect.Connections,
    newBatch func(connect.BigQueryExecutorInterface, connect.PsClient) bqbatch.Batcher,
    newServer func(string, http.Handler) Server,
    newSignals func() <-chan os.Signal,
) error
```
- Functions accept dependencies as parameters
- Use function types for factory methods
- Enable easy mocking in tests

### 3.3 Interface Definition
```go
type Server interface {
    ListenAndServe() error
    Shutdown(context.Context) error
}
```
- Define minimal interfaces with only required methods
- Use interfaces for testability and abstraction
- Place interfaces close to their usage, not implementation

### 3.4 Error Handling
```go
// Define package-level errors
var (
    ErrConvertQueryParam = errors.New("unable to convert query string parameter deviceid to int")
    ErrInvalidUrlQuery   = errors.New("invalid query url, could not fetch device_id")
    ErrUnexpectedUsage   = errors.New("unexpected endpoint usage")
)

// Error handling with context
if err != nil {
    logger.Errorf("failed to connect: %v", err)
    return fmt.Errorf("%w after %d attempts: %v", ErrPostgresConnection, maxRetries, err)
}
```
- Use `errors.New()` for package-level error definitions
- Wrap errors with context using `fmt.Errorf()` and `%w` verb
- Log errors with appropriate level and context
- Use `errors.Is()` for error comparison

## 4. Logging Standards

### 4.1 Logging Levels
- **Debug**: Detailed debugging information
- **Info**: General operational messages
- **Warn**: Warning conditions that don't stop operation
- **Error**: Error conditions with stack traces
- **Fatal**: Critical errors that cause program termination

### 4.2 Logging Patterns
```go
logger.Info("Starting broker...")
logger.Infof("Broker running on %s", addr)
logger.Errorf("failed to connect: %v", err)
logger.Debug("processing record", zap.String("deviceId", deviceId))
```
- Use structured logging with zap fields when possible
- Include relevant context in log messages
- Use formatted logging (`*f` methods) for simple interpolation
- Automatic source location and stack traces for errors

## 5. Testing Conventions

### 5.1 Test Structure
```go
func TestRun_AllPaths(t *testing.T) {
    tests := []struct {
        name                    string
        ctxFunc                 func() context.Context
        signalDelay             time.Duration
        expectBatchLogErr       bool
        expectHealthzLogErr     bool
    }{
        {
            name:        "signal_shutdown",
            ctxFunc:     func() context.Context { return context.Background() },
            signalDelay: 10 * time.Millisecond,
        },
        // ... more test cases
    }
    
    for _, tc := range tests {
        tc := tc // capture
        t.Run(tc.name, func(t *testing.T) {
            // test implementation
        })
    }
}
```
- Use table-driven tests for multiple scenarios
- Capture loop variables in subtests
- Descriptive test names that explain the scenario

### 5.2 Mocking and Dependency Injection
```go
// Override globals for testing
var (
    timeSleep        = time.Sleep
    healthzNewServer = healthz.NewServer
    loggerFatalf     = logger.Fatalf
)
```
- Use variable assignment for mockable dependencies
- Override global functions in tests
- Restore original values with defer

## 6. HTTP and API Patterns

### 6.1 Domain-Nested REST API Structure

The project follows a **Domain-Nested REST API Structure** pattern based on the `/workspace/shared/api/` organization:

#### 6.1.1 Directory Structure Pattern
```
microservices/{service}/api/handlers/
├── v3/                           # API version
│   ├── data/                     # Data domain
│   │   ├── device/              # Resource
│   │   │   ├── handler.go       # Main handler
│   │   │   ├── handler_test.go  # Handler tests
│   │   │   ├── helper.go        # Helper functions
│   │   │   ├── helper_test.go   # Helper tests
│   │   │   ├── schema.go        # Data schemas
│   │   │   └── errors.go        # Domain-specific errors
│   │   └── fault/               # Another resource
│   ├── gateway/                 # Gateway domain
│   │   ├── authenticate/        # Resource/action
│   │   ├── ingest/             # Resource/action
│   │   └── update/             # Resource/action
│   └── user/                    # User domain
│       ├── account/             # Sub-domain
│       │   ├── close/          # Specific action
│       │   └── notifications/   # Specific action
│       ├── authenticate/        # Resource/action
│       ├── instruction/         # Resource/action
│       └── profile/            # Resource/action
```

#### 6.1.2 URL Pattern Mapping
```go
// Standard API endpoints (v3)
router.HandleFunc("/api/v3/gateway/authenticate", V3GatewayAuthenticate.Handler)
router.HandleFunc("/api/v3/gateway/ingest", V3GatewayIngest.Handler)
router.HandleFunc("/api/v3/data/device", V3DataDevice.Handler)
router.HandleFunc("/api/v3/data/fault", V3DataFault.Handler)
router.HandleFunc("/api/v3/user/account/close", V3UserAccountClose.Handler)
router.HandleFunc("/api/v3/user/account/notifications", V3UserAccountNotifications.Handler)
router.HandleFunc("/api/v3/user/authenticate", V3UserAuthenticate.Handler)
router.HandleFunc("/api/v3/user/profile", V3UserProfile.Handler)

// Legacy endpoints (for backward compatibility)
router.HandleFunc("/data/v2/device", V3DataDevice.Handler)
router.HandleFunc("/user/v2/profile", V3UserProfile.Handler)
```

#### 6.1.3 Import Naming Convention
```go
import (
    V3DataDevice "synapse-its.com/broker/api/handlers/v3/data/device"
    V3DataFault "synapse-its.com/broker/api/handlers/v3/data/fault"
    V3GatewayAuthenticate "synapse-its.com/broker/api/handlers/v3/gateway/authenticate"
    V3UserAccountClose "synapse-its.com/broker/api/handlers/v3/user/account/close"
    V3UserProfile "synapse-its.com/broker/api/handlers/v3/user/profile"
)
```
- Use descriptive import aliases that reflect the full path
- Format: `V{version}{Domain}{Resource}{Action}` (e.g., `V3UserAccountClose`)
- Maintain consistency across all handler imports

#### 6.1.4 Handler Package Structure
Each handler package should contain:
- **`handler.go`**: Main HTTP handler function
- **`handler_test.go`**: Comprehensive handler tests
- **`helper.go`**: Business logic and utility functions
- **`helper_test.go`**: Helper function tests
- **`schema.go`**: Request/response data structures
- **`errors.go`**: Domain-specific error definitions
- **`repository.go`** (optional): Data access layer

#### 6.1.5 Shared API Components
Located in `/workspace/shared/api/`:
- **`authorizer/`**: JWT token validation and user context
- **`middleware/`**: Common middleware (auth, logging, connections)
- **`response/`**: Standardized HTTP response functions
- **`security/`**: Security utilities (hashing, certificates)
- **`handlers/defaultapi/`**: Default/health check endpoints

### 6.2 Handler Structure
```go
func Handler(w http.ResponseWriter, r *http.Request) {
    ctx := r.Context()

    // Get user info from context
    userInfo, ok := userInfoFromContext(ctx)
    if !ok {
        logger.Error("Unable to retrieve user info from request context")
        response.CreateInternalErrorResponse(w)
        return
    }

    // Parse request
    allDevices, deviceId, err := parseRequest(r)
    if err != nil {
        logger.Infof("Unable to parse API request: %v", err)
        response.CreateUnauthorizedResponse(w)
        return
    }

    // Business logic...

    response.CreateSuccessResponse(data, w)
}
```
- Extract context and validate early
- Use helper functions for parsing and validation
- Consistent error response patterns
- Log errors with appropriate context

### 6.3 Response Patterns
```go
response.CreateSuccessResponse(data, w)
response.CreateUnauthorizedResponse(w)
response.CreateInternalErrorResponse(w)
response.CreateForbiddenResponse(w)
```
- Use standardized response functions from `shared/api/response`
- Consistent JSON response format
- Appropriate HTTP status codes

### 6.4 API Versioning Strategy
- **Current Standard**: `/api/v3/` for all new endpoints
- **Legacy Support**: Maintain `/data/v2/` and `/user/v2/` for backward compatibility
- **Version in Path**: Include version in URL path, not headers
- **Handler Reuse**: Legacy endpoints can reuse v3 handlers for consistency

## 7. Concurrency and Resource Management

### 7.1 Graceful Shutdown
```go
// Wait for shutdown signal
select {
case <-ctx.Done():
    // no-op for test coverage
    _ = true
case <-signals:
    // no-op for test coverage
    _ = true
}

logger.Info("Shutdown signal received...")
healthzsrv.SetNotReady()

// Allow in-flight to finish processing
timeSleep(2 * time.Second)

// Graceful HTTP shutdown
shutdownCtx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
defer cancel()
if err := srv.Shutdown(shutdownCtx); err != nil {
    logger.Errorf("server shutdown error: %v", err)
}
```
- Use context cancellation for shutdown coordination
- Allow in-flight requests to complete
- Set timeouts for shutdown operations
- Update health status during shutdown

### 7.2 Resource Cleanup
```go
defer conns.Close()
defer func() {
    if err := batch.Shutdown(); err != nil {
        loggerFatalf("batch shutdown error: %v", err)
    }
}()
```
- Use defer for resource cleanup
- Handle errors in cleanup operations
- Close resources in reverse order of creation

## 8. Configuration and Environment

### 8.1 Environment Variables
```go
healthPort := os.Getenv("HEALTH_PORT")
logLevel := os.Getenv("LOG_LEVEL")
maxRows := os.Getenv("BQBATCH_MAX_ROWS")
```
- Use environment variables for configuration
- Provide sensible defaults
- Validate configuration at startup

### 8.2 Configuration Validation
```go
if maxRows := os.Getenv("BQBATCH_MAX_ROWS"); maxRows != "" {
    maxRowsInt, err := strconv.Atoi(maxRows)
    if err == nil && maxRowsInt > 0 {
        MaxRows = maxRowsInt
    }
}
```
- Validate environment variables
- Use defaults for invalid values
- Log configuration issues

## 9. Database and External Service Patterns

### 9.1 Connection Management
```go
// Connection retry logic
for i := 0; i < maxRetries; i++ {
    db, err = sqlOpen("postgres", connStr)
    if err == nil {
        err = db.PingContext(ctx)
        if err == nil {
            break
        }
    }
    logger.Infof("Attempt %d/%d: Failed to connect, retrying in %s: %v", i+1, maxRetries, backoff, err)
    timeSleep(backoff)
    backoff *= 2
}
```
- Implement retry logic with exponential backoff
- Log retry attempts with context
- Use context for cancellation
- Validate connections with ping/health checks

### 9.2 Query Patterns
```go
query := `SELECT
    COALESCE(df.MonitorTime, '1970-01-01 00:00:00'::timestamptz) AS MonitorTime,
    COALESCE(df.FaultStatus, '') AS FaultStatus,
    d.Id,
    d.DeviceIdentifier
FROM {{UserDevice}} as ud
JOIN {{Device}} as d ON ud.DeviceId = d.Id
WHERE ud.UserId = $1`
```
- Use parameterized queries
- Use COALESCE for null handling
- Use table aliases for readability
- Use placeholder syntax for table namespacing

## 10. Documentation Standards

### 10.1 Package Documentation
- Each package should have a README.md
- Include purpose, usage examples, and API reference
- Document configuration options and environment variables

### 10.2 Code Comments
```go
// Server is the subset of http.Server needed for run
type Server interface {
    ListenAndServe() error
    Shutdown(context.Context) error
}

// DefaultSignalChan returns a buffered channel that will get
// os.Interrupt and syscall.SIGTERM notifications.
func DefaultSignalChan() <-chan os.Signal {
```
- Document exported types and functions
- Explain the purpose and behavior
- Include parameter and return value descriptions
- Document any side effects or special behavior

---

*This document reflects the coding standards and patterns observed in the Synapse ITS Go microservices codebase. Follow these guidelines to maintain consistency and code quality across the project.*

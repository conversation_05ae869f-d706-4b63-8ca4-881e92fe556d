#!/usr/bin/env bash
# Description: Execute an arbitrary SQL query, replacing {{table}} placeholders with fully-qualified table names
#   bqe q <SQL query>
#     Example: bqe q "SELECT * FROM {{table}} WHERE id = 1"

set -euo pipefail

if [[ $# -lt 1 ]]; then
  echo "Usage: bqe q <SQL query>" >&2
  exit 1
fi

# 1) fetch namespace
ns=$(bqe-get namespace)
if [[ -z "$ns" ]]; then
  echo "Error: no namespace set. Run 'bqe namespace' to list or 'bqe namespace <name>' to set one." >&2
  exit 1
fi

dataset=$(bqe-dataset)
if [[ -z "$dataset" ]]; then
  echo "Error: no dataset set. Run 'bqe dataset' to list or 'bqe dataset <name>' to set one." >&2
  exit 1
fi
raw_sql="$*"

# 2) replace every {{foo}} with `dataset.NS__foo`
#    single-quoting around the sed script means backticks are literal;
#    we break out to insert $dataset and $ns into the replacement.
final_sql=$(
  printf '%s\n' "$raw_sql" |
  sed -E 's/\{\{([[:alnum:]_]+)\}\}/`'"$dataset"'.'"$ns"'__\1`/g'
)

# 3) hand off to BigQuery
bq query --use_legacy_sql=false "$final_sql"

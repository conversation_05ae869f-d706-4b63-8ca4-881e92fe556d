package helper

import "errors"

var (
	ErrInvalidTimezone = errors.New("invalid timezone")

	ErrValidateChecksumLen = errors.New("checksum failure - empty message")
	ErrValidateChecksum    = errors.New("checksum failure")

	ErrConvertToIntFromBcd = errors.New("invalid BCD byte")

	ErrValidateDateTimePartsMon  = errors.New("bad month - month reported")
	ErrValidateDateTimePartsDay  = errors.New("bad day - day reported")
	ErrValidateDateTimePartsHour = errors.New("bad hour - hour reported")
	ErrValidateDateTimePartsMin  = errors.New("bad minute - minute reported")
	ErrValidateDateTimePartsSec  = errors.New("bad second - second reported")

	ErrGetHeaderBytesFromByteMsg = errors.New("data too short: want at least 7 bytes")

	ErrParseHeaderBytesIntoStructLen = errors.New("headerResponse length != 7")

	ErrMsgByteLen                  = errors.New("error byte length")
	ErrMsgByteChecksum             = errors.New("error validating the checksum")
	ErrRMSEngineDataHeaderMismatch = errors.New("error headers do not match")
	ErrorValidateDateTimeParts     = errors.New("error validating datetime parts ")
	ErrMsgHeaderRecordNil          = errors.New("headerRecord cannot be nil")
	ErrUnsupportedDevice           = errors.New("unsupported device")

	ErrNotEnoughChannels = errors.New("channelCount must be at least 2")
	ErrNotEnoughBits     = errors.New("not enough bytes supplied to fill channel count")
)

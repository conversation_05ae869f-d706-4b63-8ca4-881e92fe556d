# Notification Service Implementation Plan

## Overview

This document outlines the implementation plan for a Pub/Sub-driven notification handler within the ETL microservice. The handler will send SMS alerts via <PERSON>wilio, with an extensible design to support additional notification channels (email, push, etc.) in the future.

## Architecture

The implementation follows a clean architecture approach with:
- Clear separation of concerns
- Dependency injection for testability
- Context-based client sharing
- Interface-based design for extensibility

## Implementation Details

### 1. Twilio Client Initialization Package

**Location**: `/microservices/etl/processors/handlers/notifications/twilio/client.go`

This package will encapsulate Twilio client creation with the following exported functions:

```go
// NewClient reads TWILIO_API_KEY and TWILIO_API_SECRET from the environment
// and returns an initialized *twilio.RestClient, or an error if credentials are missing
func NewClient(ctx context.Context) (*twilio.RestClient, error)

// WithClient stores the *twilio.RestClient in a context under a private key
func WithClient(ctx context.Context, client *twilio.RestClient) context.Context

// FromContext retrieves the *twilio.RestClient from the context
func FromContext(ctx context.Context) *twilio.RestClient
```

Implementation approach:
- Use a private context key for client storage
- Validate environment variables before client creation
- Return meaningful errors for missing credentials

### 2. Twilio Client Injection in Main

**Location**: `/microservices/etl/main.go`

Modify the `Run()` function to:
1. Create a Twilio client using `twilio.NewClient(ctx)`
2. Store the client in context with `twilio.WithClient(ctx, twClient)`
3. Fail fast if Twilio credentials are invalid

```go
// Run function modification
func Run(
    parentCtx context.Context,
    healthPort string,
    newConns func(context.Context) *connect.Connections,
    newBatch func(connect.BigQueryExecutorInterface, connect.PsClient) bqbatch.Batcher,
    subsList []subscriptions.Subscription,
    newSignals func() <-chan os.Signal,
    newTwilioClient func(context.Context) (*twilio.RestClient, error),
) error {
    // Existing code...
    
    // Initialize Twilio client
    twClient, err := newTwilioClient(ctx)
    if err != nil {
        loggerFatalf("Failed to initialize Twilio client: %v", err)
        return err
    }
    
    // Store client in context
    ctx = twilio.WithClient(ctx, twClient)
    
    // Existing code...
}
```

Update the `main()` function to pass the new dependency:

```go
func main() {
    Run(
        context.Background(),
        os.Getenv("HEALTH_PORT"),
        connect.NewConnections,
        bqbatch.NewDefault,
        subscriptions.DefaultSubscriptions(),
        DefaultSignalChan,
        twilio.NewClient,
    )
}
```

### 3. Notification Service Interface

**Location**: `/microservices/etl/processors/handlers/notifications/service.go`

Define an interface that allows for future extension:

```go
// NotificationService defines methods for sending notifications across different channels
type NotificationService interface {
    // SendSMS sends an SMS message to the specified phone number
    SendSMS(ctx context.Context, toPhone string, messageBody string) error
    
    // Future methods can be added for email, push notifications, etc.
    // SendEmail(ctx context.Context, toEmail string, subject string, body string) error
    // SendPushNotification(ctx context.Context, deviceToken string, payload interface{}) error
}
```

### 4. Twilio Implementation of NotificationService

**Location**: `/microservices/etl/processors/handlers/notifications/twilio/service.go`

Implement the `NotificationService` interface using Twilio:

```go
// Service implements the NotificationService interface using Twilio
type Service struct{}

// NewService creates a new Twilio notification service
func NewService() *Service {
    return &Service{}
}

// SendSMS sends an SMS message using Twilio
func (s *Service) SendSMS(ctx context.Context, toPhone string, messageBody string) error {
    client := FromContext(ctx)
    if client == nil {
        return errors.New("twilio client not found in context")
    }
    
    fromPhone := os.Getenv("TWILIO_FROM_PHONE")
    if fromPhone == "" {
        return errors.New("TWILIO_FROM_PHONE environment variable not set")
    }
    
    // Call Twilio API to send SMS
    // Return any errors for handler to decide between Ack/Nack
}
```

### 5. Pub/Sub Subscription Handler

**Location**: `/microservices/etl/processors/handlers/notifications/handler.go`

Create a handler function that processes notification messages:

```go
// NotificationMessage represents the structure of incoming notification messages
type NotificationMessage struct {
    Type     string                 `json:"type"`
    Payload  map[string]interface{} `json:"payload"`
    Metadata map[string]interface{} `json:"metadata"`
}

// HandlerDeps bundles all external dependencies
type HandlerDeps struct {
    Connector       ConnectorFunc
    ParseAttributes ParseAttributesFunc
    SendToDLQ       DLQSender
    NewService      func() notifications.NotificationService
}

// HandlerWithDeps returns a handler function with injected dependencies
func HandlerWithDeps(deps HandlerDeps) func(ctx context.Context, subscriptionName string) {
    return func(ctx context.Context, subscriptionName string) {
        // Get connections
        connections, err := deps.Connector(ctx)
        if err != nil {
            logger.Errorf("Error getting connections: %v", err)
            return
        }
        
        // Create notification service
        notificationService := deps.NewService()
        
        // Set up subscription
        sub := connections.Pubsub.Subscription(subscriptionName)
        err = sub.Receive(ctx, func(ctx context.Context, msg *pubsub.Message) {
            logger.Debugf("Received notification message: %s", string(msg.Data))
            
            // Parse message
            var notif NotificationMessage
            if err := json.Unmarshal(msg.Data, &notif); err != nil {
                logger.Errorf("Failed to parse notification message: %v", err)
                // Send to DLQ
                err = deps.SendToDLQ(ctx, connections.Pubsub, msg, fmt.Sprintf("Failed to parse notification: %v", err))
                if err != nil {
                    logger.Errorf("Error sending message to DLQ: %v", err)
                    msg.Nack()
                    return
                }
                msg.Ack()
                return
            }
            
            // Process based on notification type
            switch notif.Type {
            case "sms":
                // Extract SMS-specific fields
                to, ok := notif.Payload["to"].(string)
                if !ok {
                    logger.Errorf("Missing 'to' field in SMS notification")
                    msg.Ack() // Invalid payload, no retry
                    return
                }
                
                message, ok := notif.Payload["message"].(string)
                if !ok {
                    logger.Errorf("Missing 'message' field in SMS notification")
                    msg.Ack() // Invalid payload, no retry
                    return
                }
                
                // Send SMS
                err := notificationService.SendSMS(ctx, to, message)
                if err != nil {
                    logger.Errorf("Failed to send SMS: %v", err)
                    // Determine if error is transient
                    if isTransientError(err) {
                        msg.Nack() // Retry
                        return
                    }
                    // Permanent error, send to DLQ
                    err = deps.SendToDLQ(ctx, connections.Pubsub, msg, fmt.Sprintf("Failed to send SMS: %v", err))
                    if err != nil {
                        logger.Errorf("Error sending message to DLQ: %v", err)
                        msg.Nack()
                        return
                    }
                    msg.Ack()
                    return
                }
                
                logger.Infof("Successfully sent SMS to %s", to)
                msg.Ack()
                
            default:
                logger.Errorf("Unsupported notification type: %s", notif.Type)
                msg.Ack() // Unknown type, no retry
            }
        })
        
        if err != nil {
            logger.Errorf("Error receiving messages: %v", err)
        }
    }
}

// Helper function to determine if an error is transient
func isTransientError(err error) bool {
    // Implement logic to identify transient errors
    // For example, network timeouts, rate limits, etc.
    return false
}

// Handler is the production-ready Pub/Sub processor using real dependencies
var Handler = HandlerWithDeps(HandlerDeps{
    Connector:       connect.GetConnections,
    ParseAttributes: pubsubdata.ParseAttributes,
    SendToDLQ:       etlShared.SendToDLQ,
    NewService:      twilio.NewService,
})
```

### 6. Register Subscription in ETL

**Location**: `/microservices/etl/processors/subscriptions/subscriptions.go`

Add the new subscription to the list of default subscriptions:

```go
// DefaultSubscriptions returns the list of subscriptions to process
func DefaultSubscriptions() []Subscription {
    return []Subscription{
        // Existing subscriptions...
        
        // Add notification subscription
        {
            Name:    "notification-alerts-sub",
            Handler: notifications.Handler,
        },
    }
}
```

### 7. Environment Variables

Add the following environment variables to `/infra/.env`:

```
TWILIO_API_KEY=<mock_auth>
TWILIO_API_SECRET=<mock_auth>
TWILIO_FROM_PHONE=<mock_auth>
```

## Testing Strategy

### Unit Tests

1. **Twilio Client Package**
   - Test context storage and retrieval
   - Test error handling for missing environment variables

2. **Notification Service**
   - Test SMS sending with mocked Twilio client
   - Test error handling

3. **Handler**
   - Test message parsing
   - Test type switching logic
   - Test error handling and Ack/Nack decisions

### Integration Tests

1. **End-to-End Flow**
   - Test complete flow from Pub/Sub message to Twilio API call
   - Use mocked Twilio API for verification

## Future Extensions

The architecture is designed to support additional notification channels:

1. **Email Notifications**
   - Add `SendEmail` method to `NotificationService` interface
   - Create email implementation package
   - Add email case to handler switch statement

2. **Push Notifications**
   - Add `SendPushNotification` method to interface
   - Implement for various platforms (iOS, Android)
   - Add push notification case to handler

## Implementation Timeline

1. Create Twilio client package
2. Define notification service interface
3. Implement Twilio service
4. Create Pub/Sub handler
5. Update main.go for dependency injection
6. Register subscription
7. Write tests
8. Documentation
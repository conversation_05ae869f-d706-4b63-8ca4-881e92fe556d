// This file was generated to reach 100% coverage mock.go

package authenticate

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

// Test_MockDatabaseExecutor tests the MockDatabaseExecutor methods
func Test_MockDatabaseExecutor(t *testing.T) {
	t.Parallel()
	// Create a new mock database executor
	mockDB := new(MockDatabaseExecutor)

	// Test Close method
	err := mockDB.Close()
	assert.NoError(t, err, "Close should not return an error")

	// Test ExecMultiple method
	err = mockDB.ExecMultiple("query1; query2")
	assert.NoError(t, err, "ExecMultiple should not return an error")

	// Test QueryGeneric method
	results, err := mockDB.QueryGeneric("query", "param1", "param2")
	assert.NoError(t, err, "QueryGeneric should not return an error")
	assert.Nil(t, results, "QueryGeneric should return nil results")

	// Test QueryGenericSlice method
	var dest []interface{}
	err = mockDB.QueryGenericSlice(dest, "query", "param1", "param2")
	assert.NoError(t, err, "QueryGenericSlice should not return an error")

	// Test QueryRow method
	_, err = mockDB.QueryRow("query", "param1", "param2")
	assert.NoError(t, err, "QueryRow should not return an error")

	// Test EscapeIdentifier method
	result := mockDB.EscapeIdentifier("identifier")
	assert.Equal(t, "identifier", result, "EscapeIdentifier should return the input")

	// Test ReplaceNamespace method
	result = mockDB.ReplaceNamespace("query")
	assert.Equal(t, "query", result, "ReplaceNamespace should return the input")
}

// Test_MockSQLResult tests the MockSQLResult methods
func Test_MockSQLResult(t *testing.T) {
	t.Parallel()
	// Create a new mock SQL result
	mockResult := new(MockSQLResult)

	// Setup expectations for LastInsertId
	mockResult.On("LastInsertId").Return(int64(42), nil)

	// Test LastInsertId method
	id, err := mockResult.LastInsertId()
	assert.NoError(t, err, "LastInsertId should not return an error")
	assert.Equal(t, int64(42), id, "LastInsertId should return the mocked value")

	// Setup expectations for RowsAffected
	mockResult.On("RowsAffected").Return(int64(7), nil)

	// Test RowsAffected method
	rows, err := mockResult.RowsAffected()
	assert.NoError(t, err, "RowsAffected should not return an error")
	assert.Equal(t, int64(7), rows, "RowsAffected should return the mocked value")
}

// Test_FakeResult tests the fakeResult methods
func Test_FakeResult(t *testing.T) {
	t.Parallel()
	// Create a new fake result
	fakeRes := fakeResult{}

	// Test LastInsertId method
	id, err := fakeRes.LastInsertId()
	assert.NoError(t, err, "LastInsertId should not return an error")
	assert.Equal(t, int64(0), id, "LastInsertId should return 0")

	// Test RowsAffected method
	rows, err := fakeRes.RowsAffected()
	assert.NoError(t, err, "RowsAffected should not return an error")
	assert.Equal(t, int64(1), rows, "RowsAffected should return 1")
}

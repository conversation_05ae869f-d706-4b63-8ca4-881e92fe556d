{
  "name": "Go Dev Container",
  "build": {
    "dockerfile": "Dockerfile"
  },
  "containerEnv": {
    "CLOUDSDK_AUTH_ACCESS_TOKEN": "dummy"
  },
  "workspaceFolder": "/workspace",
  "workspaceMount": "source=${localWorkspaceFolder},target=/workspace,type=bind,consistency=cached",
  "mounts": [
    "source=${localWorkspaceFolder}/shared,target=/shared,type=bind,consistency=cached",
    "source=${localWorkspaceFolder}/schemas,target=/schemas,type=bind,consistency=cached",
    "source=go-cache-devcontainer,target=/go/pkg/mod,type=volume,consistency=cached",
    "source=go-build-cache-devcontainer,target=/root/.cache/go-build,type=volume,consistency=cached",
    "source=infra_npm-cache-devcontainer,target=/home/<USER>/.npm,type=volume,consistency=cached",
    "source=infra_onramp-ui-node-modules,target=/workspace/microservices/onramp/.ui/node_modules,type=volume,consistency=cached",
    "source=infra_onramp-ui-e2e-node-modules,target=/workspace/microservices/onramp/.e2e/node_modules,type=volume,consistency=cached",
    "source=infra_onramp-ui-angular-cache,target=/workspace/microservices/onramp/.ui/.angular,type=volume,consistency=cached",
    "source=vscode-extensions,target=/home/<USER>/.vscode-server/extensions,type=volume,consistency=cached"
  ],
  "remoteUser": "root",
  "overrideCommand": false,
  "runArgs": [
    "--env-file",
    "${localWorkspaceFolder}/infra/.env",
    "--network=infra_test-network"
  ],
  "customizations": {
    "vscode": {
      "extensions": [
        "golang.Go",
        "ms-azuretools.vscode-docker",
        "ms-vscode.powershell"
      ],
      "settings": {
        // Go tooling
        "go.useLanguageServer": true,
        "go.lintTool": "golangci-lint",
        "go.lintOnSave": "package",
        "go.lintFlags": [
          "--fast",
          "--config=./.golangci.yml" // This should be /workspace/.golangci.yml but too many issues are caused if it is set
        ],
        // Formatting: goimports with two local prefixes
        "go.formatTool": "goimports",
        "go.formatFlags": [
          "-local",
          "synapse-its.com",
          "-local",
          "bitbucket.org/synapse-its"
        ],
        // On‑save actions for Go files only:
        "[go]": {
          // Format (goimports) on save
          "editor.formatOnSave": true,
          // Organize imports (goimports) on save
          "editor.codeActionsOnSave": {
            "source.organizeImports": "always"
          }
        },
        // gopls-specific analysis & formatting
        "gopls": {
          "formatting.gofumpt": true,
          "analyses": {
            "unusedparams": true,
            "shadow": true
          },
          "staticcheck": true
        },
        "go.coveragePromptFile": "/workspace/microservices/testing/coverage/final_coverage.out"
      }
    }
  }
}
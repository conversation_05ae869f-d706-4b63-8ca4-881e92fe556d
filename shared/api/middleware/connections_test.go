package middleware

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/mocks"
)

func TestConnectionsMiddleware(t *testing.T) {
	// Use the fake connections returned by mocks.FakeConns().
	// It should return a non-nil *connect.Connections with fake Redis, Pubsub,
	// Postgres, and Bigquery connections.
	fakeConns := mocks.FakeConns()

	// Create a dummy handler that asserts the connections are present in the context.
	nextHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		_, err := connect.GetConnections(r.Context())
		if err != nil {
			http.Error(w, "connections not found", http.StatusInternalServerError)
			return
		}

		w.WriteHeader(http.StatusOK)
		w.Write([]byte("middleware works"))
	})

	// Build the middleware handler using the fake connections.
	mw := ConnectionsMiddleware(fakeConns)(nextHandler)

	// Create a dummy HTTP request.
	req := httptest.NewRequest("GET", "http://example.com", nil)

	// Create a ResponseRecorder to capture the response.
	rr := httptest.NewRecorder()

	// Serve the HTTP request through the middleware.
	mw.ServeHTTP(rr, req)

	// Check the result.
	if rr.Code != http.StatusOK {
		t.Errorf("expected status code 200, got %d", rr.Code)
	}

	expectedBody := "middleware works"
	if rr.Body.String() != expectedBody {
		t.Errorf("expected response body %q, got %q", expectedBody, rr.Body.String())
	}
}

package setup

import (
	"context"
	"fmt"
	"time"

	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/logger"
	"synapse-its.com/shared/pubsubdata"
)

func PubSub(ctx context.Context, client connect.PsClient) error {
	// List of topics to ensure exist.
	for _, topicName := range pubsubdata.Topics {
		topic := client.Topic(topicName)
		exists, err := topic.Exists(ctx)
		if err != nil {
			logger.Debugf("Error checking if topic %q exists: %v", topicName, err)
			continue
		}
		if !exists {
			logger.Debugf("Topic %q does not exist. Creating...", topicName)
			_, err = client.CreateTopic(ctx, topicName)
			if err != nil {
				return fmt.Errorf("failed to create topic %q: %v", topicName, err)
			}
			logger.Debugf("Created topic: %q", topicName)
		} else {
			logger.Debugf("Topic %q already exists", topicName)
		}
	}

	for subName, topicName := range pubsubdata.PubsubSubscriptions {
		sub := client.Subscription(subName)
		exists, err := sub.Exists(ctx)
		if err != nil {
			logger.Debugf("Error checking if subscription %q exists: %v", subName, err)
			continue
		}
		if !exists {
			logger.Debugf("Subscription %q does not exist. Creating...", subName)
			topic := client.Topic(topicName)
			_, err = client.CreateSubscription(ctx, subName, connect.SubscriptionConfig{
				Topic:       topic,
				AckDeadline: 300 * time.Second,
			})
			if err != nil {
				return fmt.Errorf("failed to create subscription %q: %v", subName, err)
			}
			logger.Debugf("Created subscription: %q", subName)
		} else {
			logger.Debugf("Subscription %q already exists", subName)
		}
	}
	return nil
}

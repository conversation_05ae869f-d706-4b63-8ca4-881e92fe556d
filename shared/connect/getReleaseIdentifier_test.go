package connect

import (
	"os"
	"sync"
	"testing"
)

// helper to reset package‐level cache between tests
func reset() {
	identifierOnce = sync.Once{}
	cachedIdentifier = ""
	cachedError = nil
}

// helper to get a pointer to a string literal
func strPtr(s string) *string { return &s }

func TestGetReleaseIdentifier(t *testing.T) {
	origWd, err := os.Getwd()
	if err != nil {
		t.Fatalf("unable to get working directory: %v", err)
	}

	tests := []struct {
		name        string
		fileContent *string // nil = no file, non‐nil = write this content (possibly empty/whitespace)
		envValue    string  // if empty, CURRENT_TIMESTAMP is unset
		want        string
		wantErr     bool
	}{
		{
			name:        "file present non‐empty",
			fileContent: strPtr("  file-id  "),
			envValue:    "",
			want:        "file-id",
			wantErr:     false,
		},
		{
			name:        "file present empty, env set",
			fileContent: strPtr("   "),
			envValue:    "env-ts",
			want:        "env-ts",
			wantErr:     false,
		},
		{
			name:        "file absent, env set",
			fileContent: nil,
			envValue:    "env-ts2",
			want:        "env-ts2",
			wantErr:     false,
		},
		{
			name:        "file present empty, env empty",
			fileContent: strPtr(" "),
			envValue:    "",
			want:        "",
			wantErr:     true,
		},
		{
			name:        "file absent, env empty",
			fileContent: nil,
			envValue:    "",
			want:        "",
			wantErr:     true,
		},
	}

	for _, tc := range tests {
		tc := tc // capture range variable
		t.Run(tc.name, func(t *testing.T) {
			// reset the sync.Once cache
			reset()

			// switch to a clean temp directory
			tmp := t.TempDir()
			if err := os.Chdir(tmp); err != nil {
				t.Fatalf("chdir to temp dir: %v", err)
			}
			defer os.Chdir(origWd)

			// set up identifier.txt if requested
			if tc.fileContent != nil {
				if err := os.WriteFile("identifier.txt", []byte(*tc.fileContent), 0o644); err != nil {
					t.Fatalf("write identifier.txt: %v", err)
				}
			}

			// set or unset CURRENT_TIMESTAMP
			if tc.envValue != "" {
				os.Setenv("CURRENT_TIMESTAMP", tc.envValue)
			} else {
				os.Unsetenv("CURRENT_TIMESTAMP")
			}

			got, err := GetReleaseIdentifier()
			if tc.wantErr {
				if err == nil {
					t.Errorf("expected error, got none")
				}
				return
			}
			if err != nil {
				t.Fatalf("unexpected error: %v", err)
			}
			if got != tc.want {
				t.Errorf("expected %q, got %q", tc.want, got)
			}
		})
	}
}

func TestGetReleaseIdentifier_Caching(t *testing.T) {
	origWd, err := os.Getwd()
	if err != nil {
		t.Fatalf("unable to get working directory: %v", err)
	}

	// reset state and switch to temp dir
	reset()
	tmp := t.TempDir()
	if err := os.Chdir(tmp); err != nil {
		t.Fatalf("chdir to temp dir: %v", err)
	}
	defer os.Chdir(origWd)

	// initial setup: write a file and set env (env should be ignored if file present)
	if err := os.WriteFile("identifier.txt", []byte("initial"), 0o644); err != nil {
		t.Fatalf("write identifier.txt: %v", err)
	}
	os.Setenv("CURRENT_TIMESTAMP", "ignored")

	// first call populates the cache
	first, err1 := GetReleaseIdentifier()
	if err1 != nil {
		t.Fatalf("first call unexpected error: %v", err1)
	}
	if first != "initial" {
		t.Fatalf("first call expected %q, got %q", "initial", first)
	}

	// mutate both file and env
	os.WriteFile("identifier.txt", []byte("changed"), 0o644)
	os.Setenv("CURRENT_TIMESTAMP", "changed-env")

	// second call should still return the cached "initial"
	second, err2 := GetReleaseIdentifier()
	if err2 != nil {
		t.Errorf("second call unexpected error: %v", err2)
	}
	if second != first {
		t.Errorf("second call expected cached %q, got %q", first, second)
	}
}

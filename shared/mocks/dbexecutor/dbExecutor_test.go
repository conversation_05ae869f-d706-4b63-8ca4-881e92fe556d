package dbexecutor

import (
	"database/sql"
	"fmt"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// fakeResultDBExecutor is a minimal sql.Result implementation for testing.
type fakeResultDBExecutor struct{}

func (r *fakeResultDBExecutor) LastInsertId() (int64, error) { return 0, nil }
func (r *fakeResultDBExecutor) RowsAffected() (int64, error) { return 0, nil }

var errDBCustomFunc = fmt.Errorf("custom function error")

func TestFakeDBExecutor_Exec(t *testing.T) {
	tests := []struct {
		name       string
		executor   *FakeDBExecutor
		calls      int
		wantErrs   []error
		wantResNil bool
	}{
		{
			name:       "default behavior",
			executor:   &FakeDBExecutor{},
			calls:      1,
			wantErrs:   []error{nil},
			wantResNil: true,
		},
		{
			name: "custom ExecFunc",
			executor: &FakeDBExecutor{
				ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
					return &fakeResultDBExecutor{}, errDBCustomFunc
				},
			},
			calls:      1,
			wantErrs:   []error{errDBCustomFunc},
			wantResNil: false,
		},
		{
			name:       "fail after threshold 0",
			executor:   &FakeDBExecutor{EnableFailAfter: true, ExecCallFailAfter: 0},
			calls:      1,
			wantErrs:   []error{ErrFakeDBExecutorExecCallLimit},
			wantResNil: true,
		},
		{
			name:       "fail after threshold 2",
			executor:   &FakeDBExecutor{EnableFailAfter: true, ExecCallFailAfter: 2},
			calls:      3,
			wantErrs:   []error{nil, nil, ErrFakeDBExecutorExecCallLimit},
			wantResNil: true,
		},
		{
			name:       "never fail when threshold negative",
			executor:   &FakeDBExecutor{EnableFailAfter: true, ExecCallFailAfter: -1},
			calls:      3,
			wantErrs:   []error{nil, nil, nil},
			wantResNil: true,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			for i := 0; i < tc.calls; i++ {
				res, err := tc.executor.Exec("Q")
				if tc.wantErrs[i] == nil {
					require.NoError(t, err)
				} else {
					assert.ErrorIs(t, err, tc.wantErrs[i])
				}
				if tc.wantResNil {
					assert.Nil(t, res)
				} else {
					assert.NotNil(t, res)
				}
				assert.Equal(t, i+1, tc.executor.ExecCallCount)
			}
		})
	}
}

func TestFakeDBExecutor_ExecMultiple(t *testing.T) {
	tests := []struct {
		name     string
		executor *FakeDBExecutor
		calls    int
		wantErrs []error
	}{
		{"default behavior", &FakeDBExecutor{}, 1, []error{nil}},
		{"custom ExecMultipleFunc", &FakeDBExecutor{ExecMultipleFunc: func(q string) error { return errDBCustomFunc }}, 1, []error{errDBCustomFunc}},
		{"fail after 0", &FakeDBExecutor{EnableFailAfter: true, ExecMultipleCallFailAfter: 0}, 1, []error{ErrFakeDBExecutorExecMultipleCallLimit}},
		{"fail after 2", &FakeDBExecutor{EnableFailAfter: true, ExecMultipleCallFailAfter: 2}, 3, []error{nil, nil, ErrFakeDBExecutorExecMultipleCallLimit}},
		{"never fail", &FakeDBExecutor{EnableFailAfter: true, ExecMultipleCallFailAfter: -1}, 3, []error{nil, nil, nil}},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			for i := 0; i < tc.calls; i++ {
				err := tc.executor.ExecMultiple("Q")
				if tc.wantErrs[i] == nil {
					require.NoError(t, err)
				} else {
					assert.ErrorIs(t, err, tc.wantErrs[i])
				}
				assert.Equal(t, i+1, tc.executor.ExecMultipleCallCount)
			}
		})
	}
}

func TestFakeDBExecutor_QueryGeneric(t *testing.T) {
	tests := []struct {
		name     string
		executor *FakeDBExecutor
		calls    int
		wantErrs []error
	}{
		{"default", &FakeDBExecutor{}, 1, []error{nil}},
		{"custom", &FakeDBExecutor{QueryGenericFunc: func(q string, args ...interface{}) ([]map[string]interface{}, error) {
			return nil, errDBCustomFunc
		}}, 1, []error{errDBCustomFunc}},
		{"fail0", &FakeDBExecutor{EnableFailAfter: true, QueryGenericCallFailAfter: 0}, 1, []error{ErrFakeDBExecutorQueryGenericCallLimit}},
		{"fail2", &FakeDBExecutor{EnableFailAfter: true, QueryGenericCallFailAfter: 2}, 3, []error{nil, nil, ErrFakeDBExecutorQueryGenericCallLimit}},
		{"never fail", &FakeDBExecutor{EnableFailAfter: true, QueryGenericCallFailAfter: -1}, 3, []error{nil, nil, nil}},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			for i := 0; i < tc.calls; i++ {
				_, err := tc.executor.QueryGeneric("Q")
				if tc.wantErrs[i] == nil {
					require.NoError(t, err)
				} else {
					assert.ErrorIs(t, err, tc.wantErrs[i])
				}
				assert.Equal(t, i+1, tc.executor.QueryGenericCallCount)
			}
		})
	}
}

func TestFakeDBExecutor_QueryGenericSlice(t *testing.T) {
	tests := []struct {
		name     string
		executor *FakeDBExecutor
		calls    int
		wantErrs []error
	}{
		{"default", &FakeDBExecutor{}, 1, []error{nil}},
		{"custom", &FakeDBExecutor{QueryGenericSliceFunc: func(dest interface{}, q string, args ...interface{}) error { return errDBCustomFunc }}, 1, []error{errDBCustomFunc}},
		{"fail0", &FakeDBExecutor{EnableFailAfter: true, QueryGenericSliceCallFailAfter: 0}, 1, []error{ErrFakeDBExecutorQueryGenericSliceCallLimit}},
		{"fail2", &FakeDBExecutor{EnableFailAfter: true, QueryGenericSliceCallFailAfter: 2}, 3, []error{nil, nil, ErrFakeDBExecutorQueryGenericSliceCallLimit}},
		{"never fail", &FakeDBExecutor{EnableFailAfter: true, QueryGenericSliceCallFailAfter: -1}, 3, []error{nil, nil, nil}},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			var dest []int
			for i := 0; i < tc.calls; i++ {
				err := tc.executor.QueryGenericSlice(&dest, "Q")
				if tc.wantErrs[i] == nil {
					require.NoError(t, err)
				} else {
					assert.ErrorIs(t, err, tc.wantErrs[i])
				}
				assert.Equal(t, i+1, tc.executor.QueryGenericSliceCallCount)
			}
		})
	}
}

func TestFakeDBExecutor_QueryRow(t *testing.T) {
	tests := []struct {
		name     string
		executor *FakeDBExecutor
		calls    int
		wantErrs []error
	}{
		{"default", &FakeDBExecutor{}, 1, []error{nil}},
		{"custom", &FakeDBExecutor{QueryRowFunc: func(q string, args ...interface{}) (map[string]interface{}, error) {
			return nil, errDBCustomFunc
		}}, 1, []error{errDBCustomFunc}},
		{"fail0", &FakeDBExecutor{EnableFailAfter: true, QueryRowCallFailAfter: 0}, 1, []error{ErrFakeDBExecutorQueryRowCallLimit}},
		{"fail2", &FakeDBExecutor{EnableFailAfter: true, QueryRowCallFailAfter: 2}, 3, []error{nil, nil, ErrFakeDBExecutorQueryRowCallLimit}},
		{"never fail", &FakeDBExecutor{EnableFailAfter: true, QueryRowCallFailAfter: -1}, 3, []error{nil, nil, nil}},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			for i := 0; i < tc.calls; i++ {
				_, err := tc.executor.QueryRow("Q")
				if tc.wantErrs[i] == nil {
					require.NoError(t, err)
				} else {
					assert.ErrorIs(t, err, tc.wantErrs[i])
				}
				assert.Equal(t, i+1, tc.executor.QueryRowCallCount)
			}
		})
	}
}

func TestFakeDBExecutor_QueryRowStruct(t *testing.T) {
	tests := []struct {
		name     string
		executor *FakeDBExecutor
		calls    int
		wantErrs []error
	}{
		{"default", &FakeDBExecutor{}, 1, []error{nil}},
		{"custom", &FakeDBExecutor{QueryRowStructFunc: func(dest interface{}, q string, args ...interface{}) error { return errDBCustomFunc }}, 1, []error{errDBCustomFunc}},
		{"fail0", &FakeDBExecutor{EnableFailAfter: true, QueryRowStructCallFailAfter: 0}, 1, []error{ErrFakeDBExecutorQueryRowStructCallLimit}},
		{"fail2", &FakeDBExecutor{EnableFailAfter: true, QueryRowStructCallFailAfter: 2}, 3, []error{nil, nil, ErrFakeDBExecutorQueryRowStructCallLimit}},
		{"never fail", &FakeDBExecutor{EnableFailAfter: true, QueryRowStructCallFailAfter: -1}, 3, []error{nil, nil, nil}},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			var dest struct{ ID int }
			for i := 0; i < tc.calls; i++ {
				err := tc.executor.QueryRowStruct(&dest, "Q")
				if tc.wantErrs[i] == nil {
					require.NoError(t, err)
				} else {
					assert.ErrorIs(t, err, tc.wantErrs[i])
				}
				assert.Equal(t, i+1, tc.executor.QueryRowStructCallCount)
			}
		})
	}
}

func TestFakeDBExecutor_EscapeIdentifier(t *testing.T) {
	tests := []struct {
		name       string
		executor   *FakeDBExecutor
		identifier string
		wantOut    string
	}{
		{"default", &FakeDBExecutor{}, "col1", ""},
		{"custom", &FakeDBExecutor{EscapeIdentifierFunc: func(id string) string { return "[`" + id + "]`" }}, "col1", "[`col1]`"},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			out := tc.executor.EscapeIdentifier(tc.identifier)
			assert.Equal(t, tc.wantOut, out)
			assert.Equal(t, 1, tc.executor.EscapeIdentifierCallCount)
		})
	}
}

func TestFakeDBExecutor_ReplaceNamespace(t *testing.T) {
	tests := []struct {
		name     string
		executor *FakeDBExecutor
		query    string
		wantOut  string
	}{
		{"default", &FakeDBExecutor{}, "SELECT * FROM <NAMESPACE>t", ""},
		{"custom", &FakeDBExecutor{ReplaceNamespaceFunc: func(q string) string { return strings.ReplaceAll(q, "<NAMESPACE>", "prod_") }}, "<NAMESPACE>x", "prod_x"},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			out := tc.executor.ReplaceNamespace(tc.query)
			assert.Equal(t, tc.wantOut, out)
			assert.Equal(t, 1, tc.executor.ReplaceNamespaceCallCount)
		})
	}
}

func TestFakeDBExecutor_Close(t *testing.T) {
	tests := []struct {
		name     string
		executor *FakeDBExecutor
		calls    int
		wantErrs []error
	}{
		{"default", &FakeDBExecutor{}, 1, []error{nil}},
		{"custom", &FakeDBExecutor{CloseFunc: func() error { return errDBCustomFunc }}, 1, []error{errDBCustomFunc}},
		{"fail0", &FakeDBExecutor{EnableFailAfter: true, CloseCallFailAfter: 0}, 1, []error{ErrFakeDBExecutorCloseCallLimit}},
		{"fail2", &FakeDBExecutor{EnableFailAfter: true, CloseCallFailAfter: 2}, 3, []error{nil, nil, ErrFakeDBExecutorCloseCallLimit}},
		{"never fail", &FakeDBExecutor{EnableFailAfter: true, CloseCallFailAfter: -1}, 3, []error{nil, nil, nil}},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			for i := 0; i < tc.calls; i++ {
				err := tc.executor.Close()
				if tc.wantErrs[i] == nil {
					require.NoError(t, err)
				} else {
					assert.ErrorIs(t, err, tc.wantErrs[i])
				}
				assert.Equal(t, i+1, tc.executor.CloseCallCount)
			}
		})
	}
}

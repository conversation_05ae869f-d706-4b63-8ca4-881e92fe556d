package connect

import (
	"context"
	"errors"
	"fmt"
	"os"
	"testing"
	"time"

	"github.com/go-redis/redismock/v9"
	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func saveRedisVars() func() {
	origRedisNewClient := redisNewClient
	origtimeSleepRedis := timeSleepRedis

	return func() {
		redisNewClient = origRedisNewClient
		timeSleepRedis = origtimeSleepRedis
	}
}

// Test_Redis_SuccessfulConnection tests successful Redis connection with default settings
func Test_Redis_SuccessfulConnection(t *testing.T) {
	// Save original functions and restore at the end
	restore := saveRedisVars()
	defer restore()

	// Mock Redis client and override functions
	mockClient, mock := redismock.NewClientMock()
	redisNewClient = func(opt *redis.Options) *redis.Client {
		return mockClient
	}
	timeSleepRedis = func(d time.Duration) {}

	// Set up expectations
	mock.ExpectPing().SetVal("PONG")

	// Set environment variables for testing
	os.Setenv("MEMORYSTORE_HOST", "")
	os.Setenv("MEMORYSTORE_PORT", "")

	// Execute test
	client, err := Redis(context.Background())

	// Verify results
	require.NoError(t, err)
	require.NotNil(t, client)
	require.NoError(t, mock.ExpectationsWereMet())
}

// Test_Redis_ConnectionFailure tests Redis connection failure scenario
func Test_Redis_ConnectionFailure(t *testing.T) {
	// Save original functions and restore at the end
	restore := saveRedisVars()
	defer restore()

	// Mock Redis client
	mockClient, mock := redismock.NewClientMock()

	// Simulate Ping always failing
	mock.ExpectPing().SetErr(errors.New("redis not ready"))

	// Override redisClient to return the mock client
	redisNewClient = func(opt *redis.Options) *redis.Client {
		return mockClient
	}

	// Override timeSleepRedis to avoid delays in test
	timeSleepRedis = func(d time.Duration) {}

	// Set empty env to use default "redis:6379"
	os.Setenv("MEMORYSTORE_HOST", "")
	os.Setenv("MEMORYSTORE_PORT", "6379")

	// Run the Redis connection function
	ctx := context.Background()
	client, err := Redis(ctx)

	// Assertions
	assert.Nil(t, client)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "could not connect to Redis")

	err = mock.ExpectationsWereMet()
	assert.NoError(t, err)
}

// Test_RedisSet tests Redis Set operations
func Test_RedisSet(t *testing.T) {
	// Initialize Redis mock client
	redisClient, redisMock := redismock.NewClientMock()

	// Define test cases
	tests := []struct {
		name    string
		key     string
		value   string
		mockFn  func()
		wantErr bool
	}{
		{
			name:  "successful set operation",
			key:   "test",
			value: "value",
			mockFn: func() {
				redisMock.ExpectSet("test", "value", 0).SetVal("OK")
			},
			wantErr: false,
		},
		{
			name:  "failed set operation",
			key:   "test",
			value: "value",
			mockFn: func() {
				redisMock.ExpectSet("test", "value", 0).SetErr(fmt.Errorf("error"))
			},
			wantErr: true,
		},
	}

	// Execute test cases
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup mock expectations
			tt.mockFn()

			// Execute test
			err := RedisSet(context.Background(), redisClient, tt.key, tt.value)

			// Verify results
			if tt.wantErr {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// Test_RedisGet tests Redis Get operations
func Test_RedisGet(t *testing.T) {
	redisClient, redisMock := redismock.NewClientMock()

	tests := []struct {
		name          string
		key           string
		mockFn        func()
		expectedValue string
		wantErr       bool
	}{
		{
			name: "successful get operation",
			key:  "test-key",
			mockFn: func() {
				redisMock.ExpectGet("test-key").SetVal("test-value")
			},
			expectedValue: "test-value",
			wantErr:       false,
		},
		{
			name: "failed get operation",
			key:  "test-key",
			mockFn: func() {
				redisMock.ExpectGet("test-key").SetErr(fmt.Errorf("key not found"))
			},
			expectedValue: "",
			wantErr:       true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup mock expectations
			tt.mockFn()

			// Execute test
			val, err := RedisGet(context.Background(), redisClient, tt.key)

			// Verify results
			if tt.wantErr {
				assert.Error(t, err)
				assert.Empty(t, val)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedValue, val)
			}
		})
	}
}

// Test_RedisAwait tests Redis key await functionality
func Test_RedisAwait(t *testing.T) {
	redisClient, redisMock := redismock.NewClientMock()

	// Save and override time.After for fast retries
	origTimeAfter := timeAfter
	defer func() { timeAfter = origTimeAfter }()

	tests := []struct {
		name          string
		key           string
		expectedValue string
		setupFn       func() context.Context
		mockFn        func()
		expectedErr   error
	}{
		{
			name:          "immediate success",
			key:           "test-key",
			expectedValue: "test-value",
			setupFn: func() context.Context {
				return context.Background()
			},
			mockFn: func() {
				redisMock.ExpectGet("test-key").SetVal("test-value")
			},
			expectedErr: nil,
		},
		{
			name:          "success after retries",
			key:           "test-key",
			expectedValue: "test-value",
			setupFn: func() context.Context {
				return context.Background()
			},
			mockFn: func() {
				redisMock.ExpectGet("test-key").SetErr(fmt.Errorf("key not found"))
				redisMock.ExpectGet("test-key").SetVal("test-value")
			},
			expectedErr: nil,
		},
		{
			name:          "context cancellation",
			key:           "test-key",
			expectedValue: "test-value",
			setupFn: func() context.Context {
				ctx, cancel := context.WithCancel(context.Background())
				cancel() // Cancel immediately
				return ctx
			},
			mockFn:      func() {},
			expectedErr: context.Canceled,
		},
		{
			name:          "context timeout",
			key:           "test-key",
			expectedValue: "test-value",
			setupFn: func() context.Context {
				ctx, _ := context.WithTimeout(context.Background(), 10*time.Millisecond)
				return ctx
			},
			mockFn: func() {
				redisMock.ExpectGet("test-key").SetVal("wrong-value")
			},
			expectedErr: context.DeadlineExceeded,
		},
		{
			name:          "backoff cap",
			key:           "test-key",
			expectedValue: "test-value",
			setupFn: func() context.Context {
				// Never cancel: we just want to see backoff hit maxBackoff.
				return context.Background()
			},
			mockFn: func() {
				// 8 "wrong‐value" calls to push backoff from 32 s → 64 s (which is >60 s),
				// then one "correct" on the 9th call.
				for range 8 {
					redisMock.ExpectGet("test-key").SetVal("wrong-value")
				}
				redisMock.ExpectGet("test-key").SetVal("test-value")
			},
			expectedErr: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Override timeAfter per test case
			if tt.expectedErr == context.DeadlineExceeded {
				// Return a channel that never fires, so RedisAwait will block until ctx.Done()
				timeAfter = func(d time.Duration) <-chan time.Time {
					return make(chan time.Time)
				}
			} else {
				// Immediate trigger for all other cases
				timeAfter = func(d time.Duration) <-chan time.Time {
					ch := make(chan time.Time, 1)
					ch <- time.Now()
					return ch
				}
			}
			// Setup context
			ctx := tt.setupFn()

			// Setup mock expectations
			tt.mockFn()

			// Execute test
			err := RedisAwait(ctx, redisClient, tt.key, tt.expectedValue)

			// Verify results
			if tt.expectedErr != nil {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedErr, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

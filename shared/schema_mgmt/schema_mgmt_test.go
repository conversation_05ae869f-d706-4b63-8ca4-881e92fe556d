package schema_mgmt

import (
	"database/sql"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"

	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/mocks"
)

// Mock absJoin() that always fails.
func mock_absJoin_fail(filepath ...string) (string, error) {
	return "", fmt.<PERSON><PERSON>("mocked path: %v", filepath)
}

func Test_absJoin_fail(t *testing.T) {
	assert := assert.New(t)

	// Mock the absJoin_filepath_Abs function to simulate a failure
	old_absJoin_filepath_Abs := absJoin_filepath_Abs
	defer func() {
		absJoin_filepath_Abs = old_absJoin_filepath_Abs
	}()

	absJoin_filepath_Abs = func(path string) (string, error) {
		return "", fmt.Errorf("mocked path: %s", path)
	}

	_, err := absJoin("foo", "bar")
	assert.ErrorIs(err, <PERSON>rr<PERSON><PERSON><PERSON>oin, "should return error for non-existent schema")
}

func Test_absJoin_success(t *testing.T) {
	assert := assert.New(t)

	// Table-based test
	tests := []struct {
		path     []string
		expected string
	}{
		{[]string{"foo", "bar"}, "/foo/bar"},
		{[]string{"foo", "bar", "..", "baz"}, "/foo/baz"},
		{[]string{"foo", "bar", "..", "..", "baz"}, "/baz"},
		{[]string{"foo", "bar", "..", "baz", "..", "qux"}, "/foo/qux"},
		{[]string{"..", ".."}, "/"},
		{[]string{""}, "/"},
	}

	for _, test := range tests {
		result, err := absJoin(test.path...)
		assert.NoError(err, "should not return error")
		assert.Equal(test.expected, result, "should return correct path")
	}
}

func Test_getSchemaPaths(t *testing.T) {
	assert := assert.New(t)

	// Existing directory
	paths, err := getSchemaPaths("data-core-bq")
	assert.NoError(err, "should not return error")
	assert.NotEmpty(paths, "should return non-empty paths")
	assert.GreaterOrEqual(len(paths), 1, "should return at least one path")

	// Non-existent directory
	paths, err = getSchemaPaths("foo")
	assert.ErrorAs(err, new(*os.PathError), "should return error for non-existent schema")
	assert.Empty(paths, "should return empty paths")

	// Existing directory, but no semver directory names
	paths, err = getSchemaPaths("")
	assert.NoError(err, "should not return error for empty schema")
	assert.Empty(paths, "should return empty paths")

	orig_getSchemaPaths_absJoin1 := getSchemaPaths_absJoin1
	orig_getSchemaPaths_absJoin2 := getSchemaPaths_absJoin2
	orig_getSchemaPaths_os_ReadDir := getSchemaPaths_os_ReadDir
	restore := func() {
		getSchemaPaths_absJoin1 = orig_getSchemaPaths_absJoin1
		getSchemaPaths_absJoin2 = orig_getSchemaPaths_absJoin2
		getSchemaPaths_os_ReadDir = orig_getSchemaPaths_os_ReadDir
	}
	defer restore()

	// Mock error for getSchemaPaths_absJoin1
	getSchemaPaths_absJoin1 = mock_absJoin_fail
	paths, err = getSchemaPaths("data-core-bq")
	assert.ErrorIs(err, ErrSchemaPathAbsSchema, "should return error for non-existent schema")
	assert.Empty(paths, "should return empty paths")
	restore()

	// Mock error for getSchemaPaths_absJoin2
	getSchemaPaths_absJoin2 = mock_absJoin_fail
	paths, err = getSchemaPaths("data-core-bq")
	assert.ErrorIs(err, ErrSchemaPathAbsVersion, "should return error for non-existent schema")
	assert.Empty(paths, "should return empty paths")
	restore()

	// An earlier test alread produced an error for getSchemaPaths_os_ReadDir.
	// Rather, because this test was written before there are multiple semantic
	// version directories, we need to mock the existence of multiple
	// directories.
	getSchemaPaths_os_ReadDir = func(path string) ([]os.DirEntry, error) {
		return []os.DirEntry{
			// Intentionally out of order
			&mockDirEntry{name: "3.0.0"},
			&mockDirEntry{name: "1.0.0"},
			&mockDirEntry{name: "1.0.2"},
		}, nil
	}
	paths, err = getSchemaPaths("data-core-bq")
	assert.NoError(err, "should not return error")
	assert.NotEmpty(paths, "should return non-empty paths")
	assert.Len(paths, 3, "should return three paths")
	desiredPaths := []string{
		filepath.Join("/", SCHEMA_DIR, "data-core-bq", "1.0.0"),
		filepath.Join("/", SCHEMA_DIR, "data-core-bq", "1.0.2"),
		filepath.Join("/", SCHEMA_DIR, "data-core-bq", "3.0.0"),
	}
	for i, path := range paths {
		assert.Equal(desiredPaths[i], path, "should return correct path")
	}
}

func Test_getSequenceFiles(t *testing.T) {
	assert := assert.New(t)

	old_getSequenceFiles_absJoin1 := getSequenceFiles_absJoin1
	old_getSequenceFiles_absJoin2 := getSequenceFiles_absJoin2
	restore := func() {
		getSequenceFiles_absJoin1 = old_getSequenceFiles_absJoin1
		getSequenceFiles_absJoin2 = old_getSequenceFiles_absJoin2
	}
	defer restore()

	schemaName := "data-core-bq"
	actualSchemaVersion := "0.0"
	environment := "foo"

	// Test happy path.
	// Note: Testing this may seem strange because the test is simply doing the
	// same thing as the function that is being tested, and normally that is not
	// considered to be a good test.  This test serves a different purpose.  In
	// this case, the behavior of the function depends on business rules (i.e.,
	// how and where sequence files are stored) that are not actually enforced
	// externally.  This test just ensures that, if someone changes the function
	// in a way that would alter the business rules, this test will fail.
	paths, err := getSequenceFiles(schemaName, actualSchemaVersion, environment)
	assert.NoError(err, "should not return error")
	assert.NotEmpty(paths, "should return non-empty paths")
	assert.Len(paths, 2, "should return two paths")
	expectedPaths := []string{
		filepath.Join("/", SCHEMA_DIR, schemaName, actualSchemaVersion, "updates", "sequence.txt"),
		filepath.Join("/", SCHEMA_DIR, schemaName, actualSchemaVersion, "updates", fmt.Sprintf("sequence.%s.txt", environment)),
	}
	for i, path := range paths {
		assert.Equal(expectedPaths[i], path, "should return correct path")
	}

	// Test error path: first absJoin fails
	getSequenceFiles_absJoin1 = mock_absJoin_fail
	paths, err = getSequenceFiles(schemaName, actualSchemaVersion, environment)
	assert.ErrorIs(err, ErrGetSequenceFiles, "should return error for first absJoin failure")
	assert.Empty(paths, "should return empty paths")
	restore()

	// Test error path: second absJoin fails
	getSequenceFiles_absJoin2 = mock_absJoin_fail
	paths, err = getSequenceFiles(schemaName, actualSchemaVersion, environment)
	assert.ErrorIs(err, ErrGetSequenceFilesEnv, "should return error for second absJoin failure")
	assert.Empty(paths, "should return empty paths")
	restore()
}

func Test_listVersionDirectories(t *testing.T) {
	assert := assert.New(t)

	// Good path test
	paths, err := listVersionDirectories("data-core-bq", SCHEMA_VERSION_MAX)
	assert.NoError(err, "should not return error")
	assert.NotEmpty(paths, "should return non-empty paths")
	assert.GreaterOrEqual(len(paths), 1, "should return at least one path")
	// Check if the paths are valid directories
	for _, path := range paths {
		_, err = os.Stat(path)
		assert.NoError(err, "should be a valid directory")
	}

	// Good path: specific version
	paths, err = listVersionDirectories("data-core-bq", "0.0.0")
	assert.NoError(err, "should not return error")
	assert.NotEmpty(paths, "should return non-empty paths")
	assert.Len(paths, 1, "should return one path")
	// Check if the path is a valid directory
	for _, path := range paths {
		_, err = os.Stat(path)
		assert.NoError(err, "should be a valid directory")
	}
	// Check if the path is the expected one
	expectedPath := filepath.Join("/", SCHEMA_DIR, "data-core-bq", "0.0")
	assert.Equal(expectedPath, paths[0], "should return correct path")

	// Bad path: invalid version
	paths, err = listVersionDirectories("data-core-bq", "invalid-version")
	assert.ErrorIs(err, ErrListVersionDirsInvalidSchema, "should return error for invalid version")
	assert.Empty(paths, "should return empty paths")

	// Bad path: non-existent schema
	paths, err = listVersionDirectories("non-existent-schema", SCHEMA_VERSION_MAX)
	assert.ErrorAs(err, new(*os.PathError), "should return error for non-existent schema")
	assert.Empty(paths, "should return empty paths")
}

func Test_parseSequenceFiles(t *testing.T) {
	assert := assert.New(t)

	old_parseSequenceFiles_os_ReadFile1 := parseSequenceFiles_os_ReadFile1
	old_parseSequenceFiles_os_ReadFile2 := parseSequenceFiles_os_ReadFile2
	restore := func() {
		parseSequenceFiles_os_ReadFile1 = old_parseSequenceFiles_os_ReadFile1
		parseSequenceFiles_os_ReadFile2 = old_parseSequenceFiles_os_ReadFile2
	}
	defer restore()

	// Happy path: no sequence files (because the directory doesn't exist)
	sequenceFiles, _ := getSequenceFiles("foo", "0.0", "dev")
	parsedFiles, err := parseSequenceFiles(sequenceFiles)
	assert.NoError(err, "should not return error")
	assert.Empty(parsedFiles, "should return empty parsed files")

	// Happy path: valid sequence files
	sequenceFiles, _ = getSequenceFiles("data-core-bq", "0.0", "dev")
	parsedFiles, err = parseSequenceFiles(sequenceFiles)
	assert.NoError(err, "should not return error")
	assert.NotEmpty(parsedFiles, "should return empty parsed files")

	// Sad path: Sequence file with invalid format
	// Mock the strings.Fields function to simulate an error
	parseSequenceFiles_os_ReadFile1 = func(_ string) ([]byte, error) {
		return []byte("invalid format here"), nil
	}
	sequenceFiles, _ = getSequenceFiles("data-core-bq", "0.0", "dev")
	parsedFiles, err = parseSequenceFiles(sequenceFiles)
	assert.ErrorIs(err, ErrParseSequenceFilesInvalidLine, "should return error for invalid format")
	assert.Empty(parsedFiles, "should return empty parsed files")
	restore()

	// Sad path: Sequence file with invalid number (but not invalid format)
	// Mock the strings.Fields function to simulate an error
	parseSequenceFiles_os_ReadFile1 = func(_ string) ([]byte, error) {
		return []byte("5A invalid-number"), nil
	}
	sequenceFiles, _ = getSequenceFiles("data-core-bq", "0.0", "dev")
	parsedFiles, err = parseSequenceFiles(sequenceFiles)
	assert.ErrorIs(err, ErrParseSequenceFilesInvalidNumber, "should return error for invalid number")
	assert.Empty(parsedFiles, "should return empty parsed files")
	restore()

	// Sad path: Failure to read MD5 file
	// Mock the os.ReadFile function to simulate an error
	parseSequenceFiles_os_ReadFile2 = func(_ string) ([]byte, error) {
		return nil, fmt.Errorf("mocked error")
	}
	sequenceFiles, _ = getSequenceFiles("data-core-bq", "0.0", "dev")
	parsedFiles, err = parseSequenceFiles(sequenceFiles)
	assert.ErrorIs(err, ErrParseSequenceFilesInvalidMD5, "should return error for failure to read MD5 file")
	assert.Empty(parsedFiles, "should return empty parsed files")
	restore()
}

func Test_insertMigrations(t *testing.T) {
	assert := assert.New(t)

	fakeMigrations := []Migration{
		{Sequence: 1, FileName: "foo.sql", MD5Hash: "abc123"},
	}

	// Happy path: successful insertion
	dbExecutorSuccess := &mocks.FakeSchemaMigrationExecutor{}
	err := insertMigrations(dbExecutorSuccess, "data-core-bq", fakeMigrations)
	assert.NoError(err, "should not return error for successful insertion")

	// Happy path: no migrations to insert
	err = insertMigrations(dbExecutorSuccess, "data-core-bq", []Migration{})
	assert.NoError(err, "should not return error for no migrations to insert")

	// Sad path: failure to insert
	dbExecutorFail := &mocks.FakeSchemaMigrationExecutor{
		ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
			return nil, fmt.Errorf("mocked error")
		},
	}
	err = insertMigrations(dbExecutorFail, "data-core-bq", fakeMigrations)
	assert.ErrorIs(err, ErrInsertMigrations, "should return error for failed insertion")
}

func Test_executeSQLFile(t *testing.T) {
	assert := assert.New(t)
	fakeDbExecutor := &mocks.FakeSchemaMigrationExecutor{}

	// Happy path: successful
	err := executeSQLFile(fakeDbExecutor, "/schemas/data-core-bq/0.0/updates/test-simple.sql")
	assert.NoError(err, "should not return error for successful execution")

	// Sad path: failure to read file
	err = executeSQLFile(fakeDbExecutor, "/schemas/data-core-bq/0.0/updates/this-file-does-not-exist.sql")
	assert.ErrorIs(err, ErrExecuteSQLFileFailedRead, "should return error for failed file read")
}

func Test_ApplyMigrations(t *testing.T) {
	assert := assert.New(t)

	oldApplyMigrations_absJoin1 := ApplyMigrations_absJoin1
	oldApplyMigrations_absJoin1a := ApplyMigrations_absJoin1a
	oldApplyMigrations_absJoin2 := ApplyMigrations_absJoin2
	oldApplyMigrations_absJoin3 := ApplyMigrations_absJoin3
	oldApplyMigrations_absJoin4 := ApplyMigrations_absJoin4
	oldApplyMigrations_os_Stat1 := ApplyMigrations_os_Stat1
	oldApplyMigrations_os_Stat2 := ApplyMigrations_os_Stat2
	oldgetSequenceFiles_absJoin1 := getSequenceFiles_absJoin1
	oldparseSequenceFiles_os_ReadFile1 := parseSequenceFiles_os_ReadFile1
	oldparseSequenceFiles_os_ReadFile2 := parseSequenceFiles_os_ReadFile2
	oldexecuteSQLFile_os_ReadFile := executeSQLFile_os_ReadFile

	// Mock the fakeMigrationExecutor
	var fakeMigrationExecutor *mocks.FakeSchemaMigrationExecutor
	configFunc := func() connect.DatabaseConfig {
		return connect.DatabaseConfig{
			Environment: "dev",
		}
	}
	restore := func() {
		// Restore the functions to their original implementations
		ApplyMigrations_absJoin1 = oldApplyMigrations_absJoin1
		ApplyMigrations_absJoin1a = oldApplyMigrations_absJoin1a
		ApplyMigrations_absJoin2 = oldApplyMigrations_absJoin2
		ApplyMigrations_absJoin3 = oldApplyMigrations_absJoin3
		ApplyMigrations_absJoin4 = oldApplyMigrations_absJoin4
		ApplyMigrations_os_Stat1 = oldApplyMigrations_os_Stat1
		ApplyMigrations_os_Stat2 = oldApplyMigrations_os_Stat2
		getSequenceFiles_absJoin1 = oldgetSequenceFiles_absJoin1
		parseSequenceFiles_os_ReadFile1 = oldparseSequenceFiles_os_ReadFile1
		parseSequenceFiles_os_ReadFile2 = oldparseSequenceFiles_os_ReadFile2
		executeSQLFile_os_ReadFile = oldexecuteSQLFile_os_ReadFile

		// Reset the fakeMigrationExecutor
		fakeMigrationExecutor = &mocks.FakeSchemaMigrationExecutor{
			EnsureSchemaMigrationsTableCallFailAfter: -1,
			QueryGenericCallFailAfter:                -1,
			ExecCallFailAfter:                        -1,
			ExecMultipleCallFailAfter:                -1,
			QueryRowCallFailAfter:                    -1,
			ConfigFunc:                               configFunc,
		}
	}
	defer restore()
	restore()

	// Failure to write schema migrations table
	fakeMigrationExecutor.EnsureSchemaMigrationsTableFunc = func() error {
		return fmt.Errorf("mocked error")
	}
	err := ApplyMigrations(fakeMigrationExecutor, "data-core-bq", "0.0")
	assert.ErrorIs(err, ErrSchemaTable, "should return error for failure to write schema migrations table")
	restore()

	// No schema versions available
	err = ApplyMigrations(fakeMigrationExecutor, "foo", SCHEMA_VERSION_MAX)
	assert.ErrorIs(err, ErrVersionDirs, "should return error for no schema versions available")
	restore()

	// Failure to read from schema migrations table
	fakeMigrationExecutor.QueryRowFunc = func(query string, args ...interface{}) (map[string]interface{}, error) {
		if query == SQLGetDatabaseSchemaVersionCreatedAt {
			return nil, fmt.Errorf("mocked error")
		}
		return nil, nil
	}
	err = ApplyMigrations(fakeMigrationExecutor, "data-core-bq", SCHEMA_VERSION_MAX)
	assert.ErrorIs(err, ErrSchemaRetrieval, "should return error for failure to read from schema migrations table")
	restore()

	// When reading schema_version, it's not the correct type.
	fakeMigrationExecutor.QueryRowFunc = func(query string, args ...interface{}) (map[string]interface{}, error) {
		if query == SQLGetDatabaseSchemaVersionCreatedAt {
			return map[string]interface{}{"schema_version": 123}, nil
		}
		return nil, nil
	}
	err = ApplyMigrations(fakeMigrationExecutor, "data-core-bq", SCHEMA_VERSION_MAX)
	assert.ErrorIs(err, ErrSchemaRetrievalType, "should return error for incorrect schema_version type")
	restore()

	// ===========
	// Path 1: existingVersion == ""
	// ===========

	// Helper function to mock the existence of a schema_version table containing
	// no rows.
	returnEmptyMigration := func(query string, args ...interface{}) (map[string]interface{}, error) {
		if query == SQLGetDatabaseSchemaVersionCreatedAt {
			return nil, sql.ErrNoRows
		}
		return nil, nil
	}
	restore1 := func() {
		restore()
		fakeMigrationExecutor.QueryRowFunc = returnEmptyMigration
	}
	restore1()

	// Failure to insert into schema migrations table
	fakeMigrationExecutor.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
		if query == SQLInsertSchemaMigrationsCreationRecord {
			return nil, fmt.Errorf("mocked error")
		}
		return nil, nil
	}
	err = ApplyMigrations(fakeMigrationExecutor, "data-core-bq", SCHEMA_VERSION_MAX)
	assert.Error(err, "should return error for failure to insert into schema migrations table")
	restore1()

	// absJoin1 fails
	ApplyMigrations_absJoin1 = mock_absJoin_fail
	err = ApplyMigrations(fakeMigrationExecutor, "data-core-bq", SCHEMA_VERSION_MAX)
	assert.ErrorIs(err, ErrSchemaAbsJoin, "should return error for absJoin1 failure")
	restore1()

	// First executeSQLFile() fails (after absJoin1)
	fakeMigrationExecutor.EnableFailAfter = true
	fakeMigrationExecutor.ExecMultipleCallFailAfter = 0
	err = ApplyMigrations(fakeMigrationExecutor, "data-core-bq", SCHEMA_VERSION_MAX)
	assert.ErrorIs(err, ErrSchemaCreation, "should return error for first executeSQLFile() failure")
	restore1()

	// absJoin1a fails
	ApplyMigrations_absJoin1a = mock_absJoin_fail
	err = ApplyMigrations(fakeMigrationExecutor, "data-core-bq", SCHEMA_VERSION_MAX)
	assert.ErrorIs(err, ErrEnvironmentSchemaAbsJoin, "should return error for absJoin1a failure")
	restore1()

	// First executeSQLFile() fails (after absJoin1a)
	fakeMigrationExecutor.EnableFailAfter = true
	fakeMigrationExecutor.ExecMultipleCallFailAfter = 1
	err = ApplyMigrations(fakeMigrationExecutor, "data-core-bq", SCHEMA_VERSION_MAX)
	assert.ErrorIs(err, ErrEnvironmentSchemaCreation, "should return error for second executeSQLFile() failure")
	restore1()

	// absJoin2 fails
	ApplyMigrations_absJoin2 = mock_absJoin_fail
	err = ApplyMigrations(fakeMigrationExecutor, "data-core-bq", SCHEMA_VERSION_MAX)
	assert.ErrorIs(err, ErrAbsPathDML, "should return error for absJoin2 failure")
	restore1()

	// Second executeSQLFile() fails (after absJoin2)
	fakeMigrationExecutor.EnableFailAfter = true
	fakeMigrationExecutor.ExecMultipleCallFailAfter = 2
	err = ApplyMigrations(fakeMigrationExecutor, "data-core-bq", SCHEMA_VERSION_MAX)
	assert.ErrorIs(err, ErrDMLExecute, "should return error for third executeSQLFile() failure")
	restore1()

	// os.Stat1 fails
	ApplyMigrations_os_Stat1 = func(path string) (os.FileInfo, error) {
		return nil, fmt.Errorf("mocked error")
	}
	err = ApplyMigrations(fakeMigrationExecutor, "data-core-bq", "0.0")
	assert.ErrorIs(err, ErrDMLRead, "should return error for os.Stat1 failure")
	restore1()

	// absJoin3 fails
	ApplyMigrations_absJoin3 = mock_absJoin_fail
	err = ApplyMigrations(fakeMigrationExecutor, "data-core-bq", SCHEMA_VERSION_MAX)
	assert.ErrorIs(err, ErrDMLAbsJoin, "should return error for absJoin3 failure")
	restore1()

	// Fourth executeSQLFile() fails (after absJoin3)
	// Note: normally we override os.Stat() to simulate a failure, but in this
	// case, because we know that there is no DML file for the 'dev' environment,
	// and because environmental DML files are not required, the absence of the
	// file will not cause an error.  Furthermore, if the file does not exist,
	// then the test will not fail, because the test will not attempt to execute
	// the file.  So, in this particular case, we override os.Stat() to report
	// that there was no error (i.e., the file exists).
	ApplyMigrations_os_Stat2 = func(path string) (os.FileInfo, error) {
		return nil, nil
	}
	fakeMigrationExecutor.EnableFailAfter = true
	fakeMigrationExecutor.ExecMultipleCallFailAfter = 3
	err = ApplyMigrations(fakeMigrationExecutor, "data-core-bq", SCHEMA_VERSION_MAX)
	assert.ErrorIs(err, ErrEnvironmentDMLExecute, "should return error for fourth executeSQLFile() failure")
	restore1()

	// getSequenceFiles() fails
	getSequenceFiles_absJoin1 = mock_absJoin_fail
	err = ApplyMigrations(fakeMigrationExecutor, "data-core-bq", SCHEMA_VERSION_MAX)
	assert.ErrorIs(err, ErrGetSequenceFilesFailed, "should return error for getSequenceFiles() failure")
	restore1()

	// parseSequenceFiles() fails
	parseSequenceFiles_os_ReadFile2 = func(_ string) ([]byte, error) {
		return nil, fmt.Errorf("mocked error")
	}
	err = ApplyMigrations(fakeMigrationExecutor, "data-core-bq", SCHEMA_VERSION_MAX)
	assert.ErrorIs(err, ErrParseSequenceFiles, "should return error for parseSequenceFiles() failure")
	restore1()

	// insertMigrations() fails
	fakeMigrationExecutor.EnableFailAfter = true
	fakeMigrationExecutor.ExecCallFailAfter = 1
	err = ApplyMigrations(fakeMigrationExecutor, "data-core-bq", SCHEMA_VERSION_MAX)
	assert.ErrorIs(err, ErrInsertMigrations, "should return error for insertMigrations() failure")
	restore1()

	// ===========
	// Path 2: existingVersion == "0.0"
	// ===========

	// This path mocks the following state:
	// 1. The schema_version table contains one row with the version "0.0".
	// 2. The schema_migrations table contains one row with the version "0.0".
	//    It records a migration with the sequence number "1" and its data.
	// 3. The sequence file contains two rows: "1 foo.sql" and "2 bar.sql".
	// 4. The MD5 files for the SQL files given in the sequence file contains
	//    "abc123" and "def456", respectively.

	// Mock the existence of a schema_version table containing one row.
	returnOneRow := func(query string, args ...interface{}) (map[string]interface{}, error) {
		if query == SQLGetDatabaseSchemaVersionCreatedAt {
			return map[string]interface{}{"schema_version": "0.0"}, nil
		}
		return nil, nil
	}

	// Mock the contents of the sequence file.
	mockSequenceFile := func(filename string) ([]byte, error) {
		// Hack: only return if the filename doesn't contain "dev"
		if strings.Contains(filename, "dev") {
			return nil, nil
		}
		return []byte("1 foo.sql\n2 bar.sql"), nil
	}

	// Mock the contents of the MD5 file.
	mockMD5File := func(filename string) ([]byte, error) {
		switch {
		case strings.HasSuffix(filename, "foo.md5"):
			return []byte("abc123"), nil
		case strings.HasSuffix(filename, "bar.md5"):
			return []byte("def456"), nil
		}
		return nil, fmt.Errorf("mocked error")
	}

	// Mock the schema_migrations table to contain one row.
	mockSchemaMigrations := func(query string, args ...interface{}) ([]map[string]interface{}, error) {
		if query == SQLGetMigrations {
			return []map[string]interface{}{
				{"sequence": float64(1.0), "update_file": "foo.sql", "md5_hash": "abc123", "applied_at": "2023-01-01"},
			}, nil
		}
		return nil, nil
	}

	// This function will consistently reset the state to that which was
	// specified above.
	restore2 := func() {
		restore()
		fakeMigrationExecutor.QueryRowFunc = returnOneRow
		fakeMigrationExecutor.QueryGenericFunc = mockSchemaMigrations

		// Mock the contents of the sequence files
		parseSequenceFiles_os_ReadFile1 = mockSequenceFile
		parseSequenceFiles_os_ReadFile2 = mockMD5File
	}

	// When reading schema_version, it's not a valid semver.
	// Note: We are intentionally not using restore2() before this test,
	// simply because it's not needed.
	fakeMigrationExecutor.QueryRowFunc = func(query string, args ...interface{}) (map[string]interface{}, error) {
		if query == SQLGetDatabaseSchemaVersionCreatedAt {
			return map[string]interface{}{"schema_version": "foo"}, nil
		}
		return nil, nil
	}
	err = ApplyMigrations(fakeMigrationExecutor, "data-core-bq", SCHEMA_VERSION_MAX)
	assert.ErrorIs(err, ErrSchemaVersion, "should return error for incorrect schema_version type")
	restore2()

	// Failure to get migrations
	fakeMigrationExecutor.QueryGenericFunc = func(query string, args ...interface{}) ([]map[string]interface{}, error) {
		if query == SQLGetMigrations {
			return nil, fmt.Errorf("mocked error")
		}
		return nil, nil
	}
	err = ApplyMigrations(fakeMigrationExecutor, "data-core-bq", SCHEMA_VERSION_MAX)
	assert.ErrorIs(err, ErrGetMigrations, "should return error for failure to get migrations")
	restore2()

	// getSequenceFiles() fails
	getSequenceFiles_absJoin1 = mock_absJoin_fail
	err = ApplyMigrations(fakeMigrationExecutor, "data-core-bq", SCHEMA_VERSION_MAX)
	assert.ErrorIs(err, ErrGetSequenceFilesFailed, "should return error for getSequenceFiles() failure")
	restore2()

	// parseSequenceFiles() fails
	parseSequenceFiles_os_ReadFile2 = func(_ string) ([]byte, error) {
		return nil, fmt.Errorf("mocked error")
	}
	err = ApplyMigrations(fakeMigrationExecutor, "data-core-bq", SCHEMA_VERSION_MAX)
	assert.ErrorIs(err, ErrParseSequenceFiles, "should return error for parseSequenceFiles() failure")
	restore2()

	// number of migrations found in database is greater than expected
	fakeMigrationExecutor.QueryGenericFunc = func(query string, args ...interface{}) ([]map[string]interface{}, error) {
		if query == SQLGetMigrations {
			return []map[string]interface{}{
				{"sequence": float64(1), "update_file": "foo.sql", "md5_hash": "abc123", "applied_at": "2023-01-01"},
				{"sequence": float64(2), "update_file": "bar.sql", "md5_hash": "def456", "applied_at": "2023-01-02"},
				{"sequence": float64(3), "update_file": "baz.sql", "md5_hash": "ghi789", "applied_at": "2023-01-03"},
			}, nil
		}
		return nil, nil
	}
	err = ApplyMigrations(fakeMigrationExecutor, "data-core-bq", SCHEMA_VERSION_MAX)
	assert.ErrorIs(err, ErrTooManyMigrations, "should return error for too many migrations found in database")
	restore2()

	// Migration sequence SQL results wrong type
	fakeMigrationExecutor.QueryGenericFunc = func(query string, args ...interface{}) ([]map[string]interface{}, error) {
		if query == SQLGetMigrations {
			return []map[string]interface{}{
				{"sequence": "foo", "update_file": "foo.sql", "md5_hash": "abc123", "applied_at": "2023-01-01"},
			}, nil
		}
		return nil, nil
	}
	err = ApplyMigrations(fakeMigrationExecutor, "data-core-bq", SCHEMA_VERSION_MAX)
	assert.ErrorIs(err, ErrGetMigrations, "should return error for migration sequence SQL results wrong type")

	// Migration sequence verification failed
	fakeMigrationExecutor.QueryGenericFunc = func(query string, args ...interface{}) ([]map[string]interface{}, error) {
		if query == SQLGetMigrations {
			return []map[string]interface{}{
				{"sequence": float64(1), "update_file": "foo.sql", "md5_hash": "abc123_FOO_", "applied_at": "2023-01-01"},
			}, nil
		}
		return nil, nil
	}
	err = ApplyMigrations(fakeMigrationExecutor, "data-core-bq", SCHEMA_VERSION_MAX)
	assert.ErrorIs(err, ErrMigrationSequenceMismatch, "should return error for migration sequence verification mismatch")
	restore2()

	// State == APPLY, absJoin4 fails
	ApplyMigrations_absJoin4 = mock_absJoin_fail
	err = ApplyMigrations(fakeMigrationExecutor, "data-core-bq", SCHEMA_VERSION_MAX)
	assert.ErrorIs(err, ErrAbsPathMigrationFile, "should return error for absJoin4 failure")
	restore2()

	// State == APPLY, Apply the Migration
	// First executeSQLFile() fails (after absJoin1)
	fakeMigrationExecutor.EnableFailAfter = true
	fakeMigrationExecutor.ExecMultipleCallFailAfter = 0
	err = ApplyMigrations(fakeMigrationExecutor, "data-core-bq", SCHEMA_VERSION_MAX)
	assert.ErrorIs(err, ErrApplyMigration, "should return error for executeSQLFile() failure")
	restore2()

	// State == APPLY, Insert the Migration record
	// Mock bar.sql (can be anything)
	readBarSQL := func(path string) ([]byte, error) {
		if strings.Contains(path, "bar.sql") {
			return []byte("SELECT 1"), nil
		}
		return nil, fmt.Errorf("mocked error")
	}
	executeSQLFile_os_ReadFile = readBarSQL
	fakeMigrationExecutor.EnableFailAfter = true
	fakeMigrationExecutor.ExecCallFailAfter = 0
	err = ApplyMigrations(fakeMigrationExecutor, "data-core-bq", SCHEMA_VERSION_MAX)
	assert.ErrorIs(err, ErrInsertMigration, "should return error for insert migration failure")
	restore2()

	// Final happy path: All migrations applied successfully
	executeSQLFile_os_ReadFile = readBarSQL
	err = ApplyMigrations(fakeMigrationExecutor, "data-core-bq", SCHEMA_VERSION_MAX)
	assert.NoError(err, "should not return error for successful migration")
	restore2()
}

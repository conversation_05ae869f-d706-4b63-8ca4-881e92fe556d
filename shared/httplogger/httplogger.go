package httplogger

import (
	"fmt"
	"net/http"
	"time"

	"go.uber.org/zap"

	"synapse-its.com/shared/logger"
)

// loggingResponseWriter wraps http.ResponseWriter to capture the status code and response size.
type loggingResponseWriter struct {
	http.ResponseWriter
	statusCode int
	size       int
}

func (lrw *loggingResponseWriter) WriteHeader(code int) {
	lrw.statusCode = code
	lrw.ResponseWriter.WriteHeader(code)
}

func (lrw *loggingResponseWriter) Write(b []byte) (int, error) {
	// If WriteHeader hasn't been called yet, default to 200.
	if lrw.statusCode == 0 {
		lrw.statusCode = http.StatusOK
	}
	n, err := lrw.ResponseWriter.Write(b)
	lrw.size += n
	return n, err
}

// LoggingMiddleware logs the incoming HTTP requests and fills in details using Cloud Logging's structured httpRequest field.
func LoggingMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		startTime := time.Now()
		lrw := &loggingResponseWriter{ResponseWriter: w}

		next.ServeHTTP(lrw, r)

		latency := time.Since(startTime)

		// Build the structured httpRequest field as expected by Cloud Logging.
		httpRequestField := zap.Any("logging.googleapis.com/httpRequest", map[string]interface{}{
			"requestMethod": r.Method,
			"requestUrl":    r.URL.String(),
			"userAgent":     r.UserAgent(),
			"remoteIp":      r.RemoteAddr,
			"referer":       r.Referer(),
			"status":        lrw.statusCode,
			"responseSize":  lrw.size,
			"latency":       latency.Seconds(), // in seconds
		})

		// Log the HTTP request details.
		logger.Debug(fmt.Sprintf("HTTP %s %s processed", r.Method, r.URL.Path), httpRequestField)
	})
}

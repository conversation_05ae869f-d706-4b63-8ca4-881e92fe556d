run:
  timeout: 3m

linters:
  enable:
    - govet
    - staticcheck
    - errcheck
    - unused
    - deadcode
    - ineffassign
    - gosimple
    - structcheck
    - varcheck
    - typecheck
    - revive
    - gocyclo
    - lll
    - prealloc
    - gosec
    - depguard
  disable:
    - funlen       # skip “function too long” checks

linters-settings:
  gocyclo:
    min-complexity: 15
  errcheck:
    # Ignore errors on these functions:
    ignore:
      - fmt\.Print.* 
      - log\.Print.*
  lll:
    line-length: 80
    tab-width: 2

issues:
  exclude-use-default: false
  exclude-rules:
    # Allow unchecked errors in *_test.go
    - path: _test\.go
      text: "error return value not checked"
  max-issues-per-linter: 50
  max-same-issues: 5

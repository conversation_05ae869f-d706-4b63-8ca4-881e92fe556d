// src/app/foo-list/foo-list.component.ts
import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FooService } from '../foo.service';

@Component({
  selector: 'app-foo-list',
  imports: [CommonModule],
  templateUrl: './foo-list.component.html',
  styleUrls: ['./foo-list.component.css']
})
export class FooListComponent implements OnInit {
  names: string[] = [];
  constructor(private foo: FooService) { }
  ngOnInit() {
    this.foo.getNames().subscribe(data => this.names = data);
  }
}

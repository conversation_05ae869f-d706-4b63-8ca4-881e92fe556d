package pubsubdata

import (
	"reflect"
	"testing"

	test "synapse-its.com/shared/util/test"
)

func TestMyStructJSONMarshalable_ByType(t *testing.T) {
	// Because we validate these types here to see if they json.<PERSON>() without
	// error, then when we call json.<PERSON>() in our code, we do not have to
	// check for an error, making our test coverage simpler to manage.
	test.AssertJSONTypeMarshalable(t, reflect.TypeOf(HeaderDetails{}))
	test.AssertJSONTypeMarshalable(t, reflect.TypeOf(PubsubMessageWrapper{}))
}

# End-to-End Test Suite Documentation

This document describes the structure, conventions, and usage of the end-to-end (E2E) test suite for the Onramp microservice, implemented with Cucumber‑JS and Selenium WebDriver.

---

## Directory Structure

```
.e2e/
├─ features/
│   ├─ <domain>/
│   │   ├─ <domain>.feature
│   │   └─ step_definitions/
│   │       └─ <domain>.steps.js
│   └─ support/
│       ├─ hooks.js           # Global Cucumber hooks (timeouts, world injection)
│       └─ world.js           # Custom World for shared context

page_objects/
├─ <PageName>Page.js          # Encapsulates selectors and actions for each page

package.json
package-lock.json
```

* **features/**: Contains Gherkin feature files and their corresponding step definitions.
* **page\_objects/**: Contains page‑object classes to abstract UI interactions.
* **features/support/**: Contains global setup, teardown, and shared context code.

---

## Conventions

### Feature Files

* Location: `features/<domain>/<domain>.feature`
* Naming: kebab-case matching the area of the application (e.g., `user-settings.feature`).
* Content: High‑level scenarios written in Gherkin:

  ```gherkin
  Feature: User Settings
    Scenario: Update profile information
      Given I am logged in as "alice"
      When I navigate to the settings page
      And I update my profile details
      Then I should see a confirmation message
  ```

### Step Definitions

* Location: `features/<domain>/step_definitions/<domain>.steps.js`
* Responsibility: Map Gherkin steps to code. Import relevant page‑object classes or shared utilities.
* Isolation: Each domain folder contains only the steps relevant to its feature.

### Page Objects

* Location: `page_objects/<PageName>Page.js`
* Naming: `<PageName>Page.js` (PascalCase matching page or component name).
* Structure:

  ```js
  class LoginPage {
    constructor(driver) { this.driver = driver; }

    async open() {
      await this.driver.get(`${process.env.BASE_URL}/login`);
    }

    async login(username, password) {
      await this.driver.findElement(...).sendKeys(username);
      await this.driver.findElement(...).sendKeys(password);
      await this.driver.findElement(...).click();
    }
  }
  module.exports = LoginPage;
  ```

### Support Files

* **hooks.js**: Configures global timeouts and lifecycle hooks.

  ```js
  const { setDefaultTimeout, BeforeAll, AfterAll } = require('@cucumber/cucumber');
  setDefaultTimeout(60 * 1000);
  ```

* **world.js**: Defines a custom World to share driver and page‑object instances:

  ```js
  const { setWorldConstructor } = require('@cucumber/cucumber');
  const LoginPage = require('../../page_objects/LoginPage');

  class CustomWorld {
    constructor() {
      this.driver = /* initialized in hooks */;
      this.loginPage = new LoginPage(this.driver);
      // add additional pages here
    }
  }
  setWorldConstructor(CustomWorld);
  ```

---

## Running Tests

### Prerequisites

* Docker and Docker Compose must be installed.
* Ensure that the Onramp application and Selenium Grid services are defined in `docker-compose.yml` under the `test-network`.

### Execution

1. **Build the E2E test image**:

   ```bash
   docker-compose build cucumber
   ```
2. **Start supporting services**:

   ```bash
   docker-compose up -d selenium onramp
   ```
3. **Run the E2E tests**:

   ```bash
   docker-compose run --rm cucumber
   ```
4. **Shutdown**:

   ```bash
   docker-compose down
   ```

---

## Tagging and Filtering

* Use Gherkin tags (`@smoke`, `@regression`, `@wip`) at the top of feature files to categorize tests.
* Execute subsets via Cucumber’s `--tags` option:

  ```bash
  cucumber-js --tags "@smoke and not @wip"
  ```

---

## Maintenance Guidelines

1. **New Features**: Add a new folder under `features/`, include the `.feature` file and its `step_definitions/`.
2. **Shared Steps**: If multiple domains require the same step, refactor it into `features/support/step_definitions/shared.steps.js`.
3. **Page Objects**: Place common UI interactions in `page_objects/`. Import into step definitions as needed.
4. **Timeouts**: Adjust global timeouts in `features/support/hooks.js`. Avoid magic numbers in individual steps.
5. **CI Integration**: Configure CI to run `docker-compose run --rm onramp-ui-test` and archive Cucumber JSON or HTML reports.


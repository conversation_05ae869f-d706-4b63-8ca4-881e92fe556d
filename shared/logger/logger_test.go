package logger

import (
	"bytes"
	"encoding/json"
	"errors"
	"os"
	"strings"
	"testing"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"go.uber.org/zap/zaptest/observer"
)

// TestPlainHelpers exercises Debug, Info, Warn, <PERSON>rror and Fatal
// Can't be run in parallel due to changing globals
func TestPlainHelpers(t *testing.T) {
	origExit := exitFunc
	defer func() { exitFunc = origExit }()

	type callFn func(msg any, fields ...zap.Field)

	cases := []struct {
		name           string
		fn             callFn
		level          zapcore.Level
		input          any
		wantMsg        string
		wantStack      bool
		wantFields     int
		expectErrField bool
		expectExit     bool
	}{
		// Debug
		{"Debug_string", Debug, zapcore.DebugLevel, "hi", "hi", false, 1, false, false},
		{"Debug_error", Debug, zapcore.DebugLevel, errors.New("boom"), "boom", false, 2, true, false},
		{"Debug_default", Debug, zapcore.DebugLevel, 123, "123", false, 1, false, false},

		// Info
		{"Info_string", Info, zapcore.InfoLevel, "hello", "hello", false, 1, false, false},
		{"Info_error", Info, zapcore.InfoLevel, errors.New("oops"), "oops", false, 2, true, false},
		{"Info_default", Info, zapcore.InfoLevel, true, "true", false, 1, false, false},

		// Warn
		{"Warn_string", Warn, zapcore.WarnLevel, "watch", "watch", false, 1, false, false},
		{"Warn_error", Warn, zapcore.WarnLevel, errors.New("uh oh"), "uh oh", false, 2, true, false},
		{"Warn_default", Warn, zapcore.WarnLevel, 3.14, "3.14", false, 1, false, false},

		// Error (always stack)
		{"Error_string", Error, zapcore.ErrorLevel, "fail", "fail", true, 1, false, false},
		{"Error_error", Error, zapcore.ErrorLevel, errors.New("die"), "die", true, 2, true, false},
		{"Error_default", Error, zapcore.ErrorLevel, 99, "99", true, 1, false, false},

		// Fatal (always stack + exit)
		{"Fatal_string", Fatal, zapcore.FatalLevel, "halt", "halt", true, 1, false, true},
		{"Fatal_error", Fatal, zapcore.FatalLevel, errors.New("die"), "die", true, 2, true, true},
		{"Fatal_default", Fatal, zapcore.FatalLevel, 77, "77", true, 1, false, true},
	}

	for _, tc := range cases {
		t.Run(tc.name, func(t *testing.T) {
			// reset exit code
			var exitCode int
			exitFunc = func(c int) { exitCode = c }

			// fresh logger/observer
			core, obs := observer.New(zapcore.DebugLevel)
			Logger = zap.New(core)
			obs.TakeAll()

			// invoke
			tc.fn(tc.input)

			// one entry
			ents := obs.TakeAll()
			if len(ents) != 1 {
				t.Fatalf("got %d log entries; want 1", len(ents))
			}
			e := ents[0]

			// level
			if e.Level != tc.level {
				t.Errorf("level = %v; want %v", e.Level, tc.level)
			}

			// exit behavior
			if tc.expectExit {
				if exitCode != 1 {
					t.Errorf("exitCode = %d; want 1", exitCode)
				}
			} else if exitCode != 0 {
				t.Errorf("exitCode = %d; want 0", exitCode)
			}

			// message
			if tc.wantStack {
				prefix := tc.wantMsg + "\nStack trace:\n"
				if !strings.HasPrefix(e.Message, prefix) {
					t.Errorf("message = %q; want prefix %q", e.Message, prefix)
				}
			} else {
				if e.Message != tc.wantMsg {
					t.Errorf("message = %q; want %q", e.Message, tc.wantMsg)
				}
			}

			// fields
			ctx := e.ContextMap()
			if len(ctx) != tc.wantFields {
				t.Errorf("got %d fields; want %d", len(ctx), tc.wantFields)
			}
			if _, ok := ctx["logging.googleapis.com/sourceLocation"]; !ok {
				t.Error("missing sourceLocation field")
			}
			if hasErr := ctx["error"]; (hasErr != nil) != tc.expectErrField {
				t.Errorf("error field present = %v; want %v", hasErr != nil, tc.expectErrField)
			}
		})
	}
}

// TestFormattedHelpers exercises Debugf, Infof, Warnf, Errorf and Fatalf
// Can't be run in parallel due to changing globals
func TestFormattedHelpers(t *testing.T) {
	origExit := exitFunc
	defer func() { exitFunc = origExit }()

	tests := []struct {
		name      string
		fn        func(format string, args ...any)
		level     zapcore.Level
		format    string
		args      []any
		wantMsg   string
		wantStack bool
		wantExit  bool
	}{
		{"Debugf", Debugf, zapcore.DebugLevel, "d=%d", []any{1}, "d=1", false, false},
		{"Infof", Infof, zapcore.InfoLevel, "i=%s", []any{"ok"}, "i=ok", false, false},
		{"Warnf", Warnf, zapcore.WarnLevel, "w=%.1f", []any{3.14}, "w=3.1", false, false},
		{"Errorf", Errorf, zapcore.ErrorLevel, "e=%v", []any{"err"}, "e=err", true, false},
		{"Fatalf", Fatalf, zapcore.FatalLevel, "f=%d", []any{42}, "f=42", true, true},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			// reset exit code
			var exitCode int
			exitFunc = func(c int) { exitCode = c }

			// fresh logger/observer
			core, obs := observer.New(zapcore.DebugLevel)
			Logger = zap.New(core)
			obs.TakeAll()

			// invoke
			tc.fn(tc.format, tc.args...)

			// one entry
			ents := obs.TakeAll()
			if len(ents) != 1 {
				t.Fatalf("%s: got %d entries; want 1", tc.name, len(ents))
			}
			e := ents[0]

			// level
			if e.Level != tc.level {
				t.Errorf("%s: level = %v; want %v", tc.name, e.Level, tc.level)
			}

			// exit behavior
			if tc.wantExit {
				if exitCode != 1 {
					t.Errorf("%s: exitCode = %d; want 1", tc.name, exitCode)
				}
			} else if exitCode != 0 {
				t.Errorf("%s: exitCode = %d; want 0", tc.name, exitCode)
			}

			// message
			if tc.wantStack {
				prefix := tc.wantMsg + "\nStack trace:\n"
				if !strings.HasPrefix(e.Message, prefix) {
					t.Errorf("%s: message = %q; want prefix %q", tc.name, e.Message, prefix)
				}
			} else {
				if e.Message != tc.wantMsg {
					t.Errorf("%s: message = %q; want %q", tc.name, e.Message, tc.wantMsg)
				}
			}

			// sourceLocation
			if _, ok := e.ContextMap()["logging.googleapis.com/sourceLocation"]; !ok {
				t.Errorf("%s: missing sourceLocation field", tc.name)
			}
		})
	}
}

// Can't be run in parallel due to changing globals
func TestGetLogLevel(t *testing.T) {
	tests := []struct {
		name   string
		envVal string
		want   zapcore.Level
	}{
		{"empty_value_fallback_to_info", "", zapcore.InfoLevel},
		{"debug_lowercase", "debug", zapcore.DebugLevel},
		{"DEBUG_uppercase", "DEBUG", zapcore.DebugLevel},
		{"warn", "warn", zapcore.WarnLevel},
		{"ERROR", "ERROR", zapcore.ErrorLevel},
		{"invalid_value_fallback_to_info", "notalevel", zapcore.InfoLevel},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			// Use the testing package’s Setenv so it’s automatically cleaned up.
			t.Setenv("LOG_LEVEL", tc.envVal)

			got := getLogLevel()
			if got != tc.want {
				t.Errorf("getLogLevel() with LOG_LEVEL=%q = %v; want %v", tc.envVal, got, tc.want)
			}
		})
	}
}

// Can't be run in parallel due to changing globals
func TestBuildLogger(t *testing.T) {
	origExit := exitFunc
	defer func() { exitFunc = origExit }()

	cases := []struct {
		name          string
		cfg           func() zap.Config
		wantNil       bool
		wantExit      bool
		checkFallback func(t *testing.T, out string)
	}{
		{
			name: "success",
			cfg: func() zap.Config {
				return zap.NewProductionConfig()
			},
			wantNil:  false,
			wantExit: false,
			checkFallback: func(t *testing.T, out string) {
				if out != "" {
					t.Errorf("expected no stderr, got %q", out)
				}
			},
		},
		{
			name: "bad_encoding",
			cfg: func() zap.Config {
				cfg := zap.NewProductionConfig()
				cfg.Encoding = "no-such-encoder"
				return cfg
			},
			wantNil:  true,
			wantExit: true,
			checkFallback: func(t *testing.T, out string) {
				if out == "" {
					t.Fatal("expected fallback JSON on stderr, got empty")
				}
				var rec map[string]any
				if err := json.Unmarshal([]byte(out), &rec); err != nil {
					t.Fatalf("failed to parse JSON: %v", err)
				}
				if sev, _ := rec["severity"].(string); sev != zapcore.FatalLevel.String() {
					t.Errorf("severity = %q; want %q", sev, zapcore.FatalLevel.String())
				}
				msg, _ := rec["message"].(string)
				if !strings.Contains(msg, "failed to initialize logger") {
					t.Errorf("message = %q; want it to mention failure", msg)
				}
			},
		},
	}

	for _, tc := range cases {
		tc := tc // capture
		t.Run(tc.name, func(t *testing.T) {
			// stub exitFunc
			exitCalled := false
			exitFunc = func(code int) { exitCalled = true }

			// capture stderr
			oldStderr := os.Stderr
			r, w, err := os.Pipe()
			if err != nil {
				t.Fatalf("pipe error: %v", err)
			}
			os.Stderr = w
			defer func() {
				w.Close()
				os.Stderr = oldStderr
			}()

			// invoke
			logger := buildLogger(tc.cfg())

			// read stderr
			w.Close()
			var buf bytes.Buffer
			if _, err := buf.ReadFrom(r); err != nil {
				t.Fatalf("read stderr: %v", err)
			}
			out := buf.String()

			// assertions
			if (logger == nil) != tc.wantNil {
				t.Errorf("logger == nil = %v; want %v", logger == nil, tc.wantNil)
			}
			if exitCalled != tc.wantExit {
				t.Errorf("exitCalled = %v; want %v", exitCalled, tc.wantExit)
			}
			tc.checkFallback(t, out)
		})
	}
}

package healthz

import (
	"context"
	"io"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

// Test normalizePort with various inputs.
func TestNormalizePort(t *testing.T) {
	t.<PERSON>llel()

	cases := []struct {
		input  string
		output string
	}{
		{"", ":8081"},
		{"9090", ":9090"},
		{":9090", ":9090"},
		{"0", ":0"},
	}
	for _, tc := range cases {
		tc := tc
		t.Run(tc.input, func(t *testing.T) {
			t.<PERSON>llel()
			assert.Equal(t, tc.output, normalizePort(tc.input))
		})
	}
}

// Directly test handler methods without starting an HTTP server.
func TestServerCombined(t *testing.T) {
	t.Parallel()

	// Subtest: Direct handler behavior without real server.
	t.Run("Handlers", func(t *testing.T) {
		t.<PERSON>llel()
		srv := NewServer(":0").(*Server)
		do := func(f func(http.ResponseWriter, *http.Request)) (int, string) {
			rec := httptest.NewRecorder()
			req := httptest.NewRequest("GET", "/", nil)
			f(rec, req)
			res := rec.Result()
			body, _ := io.ReadAll(res.Body)
			return res.StatusCode, string(body)
		}

		// /livez -> 200
		status, body := do(srv.livez)
		assert.Equal(t, http.StatusOK, status)
		assert.Empty(t, body)

		// /startupz before BootComplete -> 503
		status, body = do(srv.startupz)
		assert.Equal(t, http.StatusServiceUnavailable, status)
		assert.Contains(t, body, "starting up")

		// After BootComplete -> 200
		srv.SetBootComplete()
		status, body = do(srv.startupz)
		assert.Equal(t, http.StatusOK, status)
		assert.Empty(t, body)

		// /readyz before Ready -> 503 "not ready"
		status, body = do(srv.readyz)
		assert.Equal(t, http.StatusServiceUnavailable, status)
		assert.Contains(t, body, "not ready")

		// After Ready -> 200
		srv.SetReady()
		status, body = do(srv.readyz)
		assert.Equal(t, http.StatusOK, status)
		assert.Empty(t, body)

		// Custom check error -> 503 "not ready: <err>"
		srv.SetCustomReadinessCheck(func() error { return assert.AnError })
		status, body = do(srv.readyz)
		assert.Equal(t, http.StatusServiceUnavailable, status)
		assert.Contains(t, body, "not ready:")

		// Custom check nil -> 200
		srv.SetCustomReadinessCheck(func() error { return nil })
		status, body = do(srv.readyz)
		assert.Equal(t, http.StatusOK, status)
		assert.Empty(t, body)
	})

	// Subtest: HTTP stack and shutdown behaviors.
	t.Run("HTTPAndShutdown", func(t *testing.T) {
		t.Parallel()
		srv := NewServer(":0").(*Server)

		// Start listening on ephemeral port.
		err := srv.ListenAndServe()
		assert.NoError(t, err)

		addr := srv.ActualAddr()
		assert.NotEmpty(t, addr)

		client := &http.Client{Timeout: 1 * time.Second}
		doGet := func(path string) (int, string) {
			resp, err := client.Get("http://" + addr + path)
			assert.NoError(t, err)
			defer resp.Body.Close()
			body, _ := io.ReadAll(resp.Body)
			return resp.StatusCode, string(body)
		}

		// /livez -> 200
		status, body := doGet("/livez")
		assert.Equal(t, http.StatusOK, status)
		assert.Empty(t, body)

		// /startupz still 503
		status, body = doGet("/startupz")
		assert.Equal(t, http.StatusServiceUnavailable, status)
		assert.Contains(t, body, "starting up")

		// BootComplete -> /startupz -> 200
		srv.SetBootComplete()
		status, _ = doGet("/startupz")
		assert.Equal(t, http.StatusOK, status)

		// Ready -> /readyz -> 200
		srv.SetReady()
		status, _ = doGet("/readyz")
		assert.Equal(t, http.StatusOK, status)

		// NotReady -> /readyz -> 503
		srv.SetNotReady()
		status, _ = doGet("/readyz")
		assert.Equal(t, http.StatusServiceUnavailable, status)

		// Now force s.srv.Shutdown(ctx) to return an error.
		// Do so by installing a custom readiness check that never returns, then sending a GET /readyz,
		// so the handler blocks. Then shutdown with a short deadline so Shutdown waits on handler and errors.
		srv.SetBootComplete()
		srv.SetCustomReadinessCheck(func() error {
			// Block until context is done
			<-make(chan struct{})
			return nil
		})
		srv.SetReady()

		// Start a goroutine to hit /readyz (it will hang in the custom check).
		blockingDone := make(chan struct{})
		go func() {
			defer close(blockingDone)
			_, _ = client.Get("http://" + addr + "/readyz")
		}()

		// Give the goroutine a moment to start and enter handler.
		time.Sleep(50 * time.Millisecond)

		// Shutdown with a short timeout: Shutdown should return context deadline exceeded.
		shortCtx, cancelShort := context.WithTimeout(context.Background(), 50*time.Millisecond)
		defer cancelShort()
		err = srv.Shutdown(shortCtx)
		assert.Error(t, err, "Shutdown should return error when handler is blocking and context times out")

		// Wait for the blocking GET to finish (handler was cut off).
		<-blockingDone

		// Restart and normal Shutdown
		srv2 := NewServer(":0").(*Server)
		err = srv2.ListenAndServe()
		assert.NoError(t, err)
		ctxOk, cancelOk := context.WithTimeout(context.Background(), time.Second)
		defer cancelOk()
		err = srv2.Shutdown(ctxOk)
		assert.NoError(t, err)

		// After shutdown, requests to first addr should fail
		_, err = client.Get("http://" + addr + "/livez")
		assert.Error(t, err)
	})

	// Subtest: invalid ListenAndServe and nil‐srv Shutdown.
	t.Run("ErrorPaths", func(t *testing.T) {
		t.Parallel()
		// Invalid port -> ListenAndServe error
		invalid := NewServer(":notaport").(*Server)
		err := invalid.ListenAndServe()
		assert.Error(t, err)
		assert.Empty(t, invalid.ActualAddr())

		// Shutdown when srv.srv == nil -> no error
		nilSrv := &Server{}
		ctx, cancel := context.WithTimeout(context.Background(), time.Second)
		defer cancel()
		err = nilSrv.Shutdown(ctx)
		assert.NoError(t, err)
	})
}

package notifications

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"synapse-its.com/shared/api/authorizer"
	"synapse-its.com/shared/connect"
)

// MockRepository is a mock implementation of Repository
type MockRepository struct {
	mock.Mock
}

func (m *MockRepository) UpdateNotificationSmsEnabled(userID int64, notificationSmsEnabled bool) error {
	args := m.Called(userID, notificationSmsEnabled)
	return args.Error(0)
}

func Test_HandlerWithDeps(t *testing.T) {
	tests := []struct {
		name                 string
		requestBody          interface{}
		userInfo             *authorizer.UserInfo
		userInfoOk           bool
		connections          *connect.Connections
		uuidGeneratedErr     error
		connectionsErr       error
		updateErr            error
		expectedStatus       int
		expectedResponseData *Response
	}{
		{
			name: "successful update",
			requestBody: Request{
				NotificationSmsEnabled: true,
			},
			userInfo: &authorizer.UserInfo{
				ID: 123,
			},
			userInfoOk:     true,
			connections:    &connect.Connections{},
			expectedStatus: http.StatusOK,
			expectedResponseData: &Response{
				UserID:                 123,
				TraceID:                "00000000-0000-0000-0000-000000000000",
				NotificationSmsEnabled: true,
			},
		},
		{
			name:           "missing user info",
			requestBody:    Request{},
			userInfoOk:     false,
			expectedStatus: http.StatusInternalServerError,
		},
		{
			name:        "connection error",
			requestBody: Request{},
			userInfo: &authorizer.UserInfo{
				ID: 123,
			},
			userInfoOk:     true,
			connectionsErr: errors.New("connection error"),
			expectedStatus: http.StatusInternalServerError,
		},
		{
			name:        "invalid request body",
			requestBody: "invalid json",
			userInfo: &authorizer.UserInfo{
				ID: 123,
			},
			userInfoOk:     true,
			connections:    &connect.Connections{},
			expectedStatus: http.StatusBadRequest,
		},
		{
			name: "repository error",
			requestBody: Request{
				NotificationSmsEnabled: true,
			},
			userInfo: &authorizer.UserInfo{
				ID: 123,
			},
			userInfoOk:     true,
			connections:    &connect.Connections{},
			updateErr:      errors.New("update error"),
			expectedStatus: http.StatusInternalServerError,
		},
		{
			name: "uuid generation error",
			requestBody: Request{
				NotificationSmsEnabled: true,
			},
			userInfo: &authorizer.UserInfo{
				ID: 123,
			},
			userInfoOk:       true,
			connections:      &connect.Connections{},
			uuidGeneratedErr: errors.New("uuid generation error"),
			expectedStatus:   http.StatusOK,
			expectedResponseData: &Response{
				UserID:                 123,
				TraceID:                "unexpected",
				NotificationSmsEnabled: true,
			},
		},
	}

	for _, tt := range tests {
		tt := tt // Create new variable for parallel test
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create mock repository
			mockRepo := new(MockRepository)
			if tt.userInfoOk && tt.connectionsErr == nil && tt.requestBody != "invalid json" {
				req := tt.requestBody.(Request)
				mockRepo.On("UpdateNotificationSmsEnabled", tt.userInfo.ID, req.NotificationSmsEnabled).
					Return(tt.updateErr)
			}

			// Create handler with mocked dependencies
			handler := HandlerWithDeps(HandlerDeps{
				UserInfoFromContext: func(ctx context.Context) (*authorizer.UserInfo, bool) {
					return tt.userInfo, tt.userInfoOk
				},
				UUIDGenerator: func() (uuid.UUID, error) {
					if tt.uuidGeneratedErr != nil {
						return uuid.UUID{}, tt.uuidGeneratedErr
					}
					return uuid.MustParse(tt.expectedResponseData.TraceID), nil
				},
				GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return tt.connections, tt.connectionsErr
				},
				GetRepository: func(conns *connect.Connections) Repository {
					return mockRepo
				},
			})

			// Create request
			var body []byte
			var err error
			if tt.requestBody == "invalid json" {
				body = []byte("invalid json")
			} else {
				body, err = json.Marshal(tt.requestBody)
				assert.NoError(t, err)
			}

			req := httptest.NewRequest(http.MethodPost, "/", bytes.NewBuffer(body))
			w := httptest.NewRecorder()

			// Execute request
			handler(w, req)

			// Assert response
			assert.Equal(t, tt.expectedStatus, w.Code)

			if tt.expectedResponseData != nil {
				var response map[string]interface{}
				err = json.Unmarshal(w.Body.Bytes(), &response)
				assert.NoError(t, err)

				// Assert the wrapper response structure
				assert.Equal(t, "success", response["status"])
				assert.Equal(t, "Request Succeeded", response["message"])
				assert.Equal(t, float64(http.StatusOK), response["code"])

				// Convert and assert the actual data
				dataBytes, err := json.Marshal(response["data"])
				assert.NoError(t, err)
				var data Response
				err = json.Unmarshal(dataBytes, &data)
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedResponseData, &data)
			}

			mockRepo.AssertExpectations(t)
		})
	}
}

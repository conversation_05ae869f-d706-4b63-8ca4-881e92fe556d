package setup

import (
	"strings"
	"time"

	"cloud.google.com/go/bigquery"

	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/logger"
	"synapse-its.com/shared/schema_mgmt"
)

// BigQuery creates the dataset (if it doesn't already exist) and ensures it is
// managed under schema_mgmt control.  It then applies schema migrations to
// upgrade the dataset to the highest available version up to the specified
// version.  If the provided version is an empty string, the dataset is
// upgraded to the latest available schema.
func BigQuery(bq *connect.BigQueryExecutor, version string) error {
	datasetID := bq.Config.DBName
	dataset := bq.Client.Dataset(datasetID)

	const (
		maxAttempts   = 10
		baseDelaySecs = 2
	)

	var err error
	for attempt := 1; attempt <= maxAttempts; attempt++ {
		err = dataset.Create(bq.Ctx, &bigquery.DatasetMetadata{
			Location: "us-central1",
		})
		if err == nil {
			logger.Debugf("Created dataset %q", datasetID)
			break
		}

		// If it's already there, we’re done
		if strings.Contains(err.<PERSON>rror(), "Already Exists") ||
			strings.Contains(err.<PERSON>r(), "already created") {
			logger.Debugf("Dataset %q already exists", datasetID)
			err = nil
			break
		}

		// Otherwise, transient failure: retry?
		if attempt < maxAttempts {
			wait := time.Duration(baseDelaySecs*attempt) * time.Second
			logger.Warnf(
				"Attempt %d/%d: failed to create dataset %q: %v; retrying in %s",
				attempt, maxAttempts, datasetID, err, wait,
			)
			time.Sleep(wait)
			continue
		}

		// Last attempt failed — let’s blow up
		logger.Fatalf(
			"Failed to create dataset %q after %d attempts: %v",
			datasetID, maxAttempts, err,
		)
	}

	// Hand off to your migration runner
	return schema_mgmt.ApplyMigrations(
		&schema_mgmt.BigQueryMigrationExecutor{Client: bq},
		"data-core-bq",
		version,
	)
}

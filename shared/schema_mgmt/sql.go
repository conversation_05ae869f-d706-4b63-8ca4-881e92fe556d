package schema_mgmt

var (
	// Get the version of the schema used to create the database
	SQLGetDatabaseSchemaVersionCreatedAt = `
		SELECT schema_version
		FROM {{schema_migrations}}
		ORDER BY applied_at ASC
		LIMIT 1`

	// Inserts a record into the schema_migrations table representing the
	// creation of the schema itself.
	SQLInsertSchemaMigrationsCreationRecord = `
		INSERT INTO {{schema_migrations}}
			(schema_version, sequence, update_file, md5_hash, applied_at)
		VALUES
			($1, 0, $2, '', $3)`

	// Get the list of migrations that have been applied to the database
	SQLGetMigrations = `
		SELECT sequence, update_file, md5_hash, applied_at
		FROM {{schema_migrations}}
		WHERE schema_version = $1
			AND md5_hash != ''
		ORDER BY sequence ASC`

	// Insert a record into the schema_migrations table representing the
	// application of a migration.
	SQLInsertSchemaMigrations = `
		INSERT INTO {{schema_migrations}}
			(schema_version, sequence, update_file, md5_hash, applied_at)
		VALUES
			($1, $2, $3, $4, $5)`
)

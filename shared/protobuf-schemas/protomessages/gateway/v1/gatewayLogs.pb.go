// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.30.2
// source: gateway/v1/gatewayLogs.proto

package gatewayv1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// GatewayLogs represents the overall body payload.
type GatewayLogs struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Field 1: A timestamp indicating when the message was created.
	MessageTime *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=message_time,json=messageTime,proto3" json:"message_time,omitempty"`
	// Field 2: A byte-array containing the zipped bytes of either the perfStats or a gatewayLog
	// The message is always uses zip compression.
	Message       []byte `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GatewayLogs) Reset() {
	*x = GatewayLogs{}
	mi := &file_gateway_v1_gatewayLogs_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GatewayLogs) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GatewayLogs) ProtoMessage() {}

func (x *GatewayLogs) ProtoReflect() protoreflect.Message {
	mi := &file_gateway_v1_gatewayLogs_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GatewayLogs.ProtoReflect.Descriptor instead.
func (*GatewayLogs) Descriptor() ([]byte, []int) {
	return file_gateway_v1_gatewayLogs_proto_rawDescGZIP(), []int{0}
}

func (x *GatewayLogs) GetMessageTime() *timestamppb.Timestamp {
	if x != nil {
		return x.MessageTime
	}
	return nil
}

func (x *GatewayLogs) GetMessage() []byte {
	if x != nil {
		return x.Message
	}
	return nil
}

var File_gateway_v1_gatewayLogs_proto protoreflect.FileDescriptor

const file_gateway_v1_gatewayLogs_proto_rawDesc = "" +
	"\n" +
	"\x1cgateway/v1/gatewayLogs.proto\x12\n" +
	"gateway.v1\x1a\x1fgoogle/protobuf/timestamp.proto\"f\n" +
	"\vGatewayLogs\x12=\n" +
	"\fmessage_time\x18\x01 \x01(\v2\x1a.google.protobuf.TimestampR\vmessageTime\x12\x18\n" +
	"\amessage\x18\x02 \x01(\fR\amessageBOZMbitbucket.org/synapse-its/protobuf-schemas/protomessages/gateway/v1;gatewayv1b\x06proto3"

var (
	file_gateway_v1_gatewayLogs_proto_rawDescOnce sync.Once
	file_gateway_v1_gatewayLogs_proto_rawDescData []byte
)

func file_gateway_v1_gatewayLogs_proto_rawDescGZIP() []byte {
	file_gateway_v1_gatewayLogs_proto_rawDescOnce.Do(func() {
		file_gateway_v1_gatewayLogs_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_gateway_v1_gatewayLogs_proto_rawDesc), len(file_gateway_v1_gatewayLogs_proto_rawDesc)))
	})
	return file_gateway_v1_gatewayLogs_proto_rawDescData
}

var file_gateway_v1_gatewayLogs_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_gateway_v1_gatewayLogs_proto_goTypes = []any{
	(*GatewayLogs)(nil),           // 0: gateway.v1.GatewayLogs
	(*timestamppb.Timestamp)(nil), // 1: google.protobuf.Timestamp
}
var file_gateway_v1_gatewayLogs_proto_depIdxs = []int32{
	1, // 0: gateway.v1.GatewayLogs.message_time:type_name -> google.protobuf.Timestamp
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_gateway_v1_gatewayLogs_proto_init() }
func file_gateway_v1_gatewayLogs_proto_init() {
	if File_gateway_v1_gatewayLogs_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_gateway_v1_gatewayLogs_proto_rawDesc), len(file_gateway_v1_gatewayLogs_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_gateway_v1_gatewayLogs_proto_goTypes,
		DependencyIndexes: file_gateway_v1_gatewayLogs_proto_depIdxs,
		MessageInfos:      file_gateway_v1_gatewayLogs_proto_msgTypes,
	}.Build()
	File_gateway_v1_gatewayLogs_proto = out.File
	file_gateway_v1_gatewayLogs_proto_goTypes = nil
	file_gateway_v1_gatewayLogs_proto_depIdxs = nil
}

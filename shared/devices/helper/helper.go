package helper

import (
	"errors"
	"reflect"
)

// BoolTranslator knows how to turn a bool into one of two strings.
type BoolTranslator struct {
	TrueVal, FalseVal string
}

// String returns the corresponding string for b.
func (t BoolTranslator) String(b bool) string {
	if b {
		return t.TrueVal
	}
	return t.FalseVal
}

// This is used to translate a struct (cfg) of bool to string values based on the a translator struct (translators)
// Example of cfg type foo struct {MinimumYellowClearanceEnable      bool, MinimumYellowRedClearanceEnable   bool}
// Example of the translator struct is EtlTranslators in /shared/devices/edi/helper/schemas
func TranslateConfig(cfg interface{}, translators map[string]BoolTranslator) (map[string]string, error) {
	v := reflect.ValueOf(cfg)
	// If it's a pointer, dereference it.
	if v.Kind() == reflect.Ptr {
		v = v.Elem()
	}
	if v.Kind() != reflect.Struct {
		return nil, errors.New("TranslateConfig: expected struct or pointer to struct")
	}

	t := v.Type()
	out := make(map[string]string, len(translators))

	for i := 0; i < t.NumField(); i++ {
		field := t.Field(i)
		// only look at exported fields
		if !field.IsExported() {
			continue
		}
		// we only care about bool fields
		if field.Type.Kind() != reflect.Bool {
			continue
		}

		name := field.Name
		tr, ok := translators[name]
		if !ok {
			// no translator for this field; skip
			continue
		}

		val := v.Field(i).Bool()
		if val {
			out[name] = tr.TrueVal
		} else {
			out[name] = tr.FalseVal
		}
	}

	return out, nil
}

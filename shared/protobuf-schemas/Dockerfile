# Use an official Go image
FROM golang:1.24

# 1) Install protoc
RUN apt-get update && \
    apt-get install -y wget unzip && \
      wget https://github.com/protocolbuffers/protobuf/releases/download/v30.2/protoc-30.2-linux-x86_64.zip \
         -O /tmp/protoc.zip && \
    unzip /tmp/protoc.zip -d /usr/local && \
    rm /tmp/protoc.zip

# 2) Install Go codegen plugins
RUN go install google.golang.org/protobuf/cmd/protoc-gen-go@v1.36.6 && \
    go install google.golang.org/grpc/cmd/protoc-gen-go-grpc@v1.5.1

# 3) Ensure protoc and the Go plugins are on PATH
ENV PATH="/usr/local/bin:${GOPATH}/bin:${PATH}"

# We’ll invoke this container by mounting our repo, so WORKDIR needn’t copy sources
WORKDIR /workspace

# Default: print usage if you forget to pass arguments
ENTRYPOINT ["protoc", "--help"]

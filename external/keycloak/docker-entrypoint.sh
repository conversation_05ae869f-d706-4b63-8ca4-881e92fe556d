#!/usr/bin/env bash
set -euo pipefail

KC_HOME=/opt/keycloak
APP_DIR=/app
REALM_NAME=${REALM_NAME:-onramp-dev}
REALM_FILE=$APP_DIR/realm-export.json
PREV_DIR=$KC_HOME/realm-storage
PREV_REALM=$PREV_DIR/realm-export.json
TMP_EXPORT=$APP_DIR/realm-export.tmp.json

# 1) Import on cold start
if [[ -f "$REALM_FILE" ]]; then
  if [[ -f "$PREV_REALM" ]]; then
    # get byte-sizes
    curr_size=$(stat -c%s "$REALM_FILE")
    prev_size=$(stat -c%s "$PREV_REALM")
    # get modification times (epoch seconds)
    curr_mtime=$(stat -c%Y "$REALM_FILE")
    prev_mtime=$(stat -c%Y "$PREV_REALM")

    if [[ "$curr_size" -ne "$prev_size" || "$curr_mtime" -gt "$prev_mtime" ]]; then
      echo "Realm JSON changed (size or date); importing..."
      "$KC_HOME/bin/kc.sh" import --file "$REALM_FILE" --override || true
      cp "$REALM_FILE" "$PREV_REALM"
    else
      echo "Realm unchanged; skipping import."
    fi
  else
    echo "No previous realm file found; importing fresh..."
    "$KC_HOME/bin/kc.sh" import --file "$REALM_FILE" --override || true
    cp "$REALM_FILE" "$PREV_REALM"
  fi
else
  echo "No realm-export.json at $REALM_FILE; skipping import."
fi

# 2) Define cleanup (will run on SIGTERM)
cleanup() {
  # echo "SIGTERM received; stopping Keycloak..."
  kill -- -"$KC_PID"   || true
  wait "$KC_PID"       || true

  # echo "Raw export of realm '$REALM_NAME' to $TMP_EXPORT"
  "$KC_HOME/bin/kc.sh" export \
    --realm     "$REALM_NAME" \
    --file      "$TMP_EXPORT" \
    --users     same_file \
    --optimized

  # echo "Canonicalizing JSON"
  jq -S 'walk(if type=="array" then sort else . end)' "$TMP_EXPORT" \
     > "${TMP_EXPORT}.canon"
  mv "${TMP_EXPORT}.canon" "$TMP_EXPORT"

  # echo "Comparing to existing $REALM_FILE"
  if cmp -s "$TMP_EXPORT" "$REALM_FILE"; then
    # echo "no real changes"
    :
  else
    mv "$TMP_EXPORT" "$REALM_FILE"
    # echo "realm-export.json updated"
  fi
}
trap cleanup SIGTERM SIGINT

# Starting Keycloak in prod mode, 
# --optimized can be used here since initial import causes optimization
echo "Starting Keycloak..."
setsid "$KC_HOME/bin/kc.sh" start --optimized &
KC_PID=$!

# now *this* shell stays alive, waiting for Keycloak or a SIGTERM
wait "$KC_PID"

package instruction

import (
	"context"
	"encoding/json"
	"net/http"
	"time"

	"synapse-its.com/shared/api/authorizer"
	"synapse-its.com/shared/api/response"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/logger"
)

// HandlerDeps bundles dependencies for injection and testing.
type HandlerDeps struct {
	UserInfoFromContext func(ctx context.Context) (*authorizer.UserInfo, bool)
	GetConnections      func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error)
	ParseRequest        func(r *http.Request) (*userInstructionRequest, error)
	InsertInstruction   func(db connect.DatabaseExecutor, userID int64, instruction string, deviceID int64) error
}

// The map of supported instructions
var supportedInstructions = map[string]bool{
	"get_device_logs": true,
}

// HandlerWithDeps returns an http.HandlerFunc with injected deps.
func HandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Get user info from jwt authorizer using dependency
		userInfo, ok := deps.UserInfoFromContext(ctx)
		if !ok {
			logger.Error(ErrUserInfoRetrieve.Error())
			response.CreateInternalErrorResponse(w)
			return
		}

		// Parse and validate userInstructionRequest
		requestBody, err := deps.ParseRequest(r)
		if err != nil {
			response.CreateInternalErrorResponse(w)
			return
		}

		// Get the postgres connection using dependency
		connections, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("Error getting connections: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}
		pg := connections.Postgres

		// InsertInstruction stores a new device instruction in the database
		if err := deps.InsertInstruction(pg, userInfo.ID, requestBody.Instruction, requestBody.DeviceID); err != nil {
			logger.Errorf("Error inserting instruction: %v", err)
			response.CreateInternalErrorResponse(w)
			return
		}

		response.CreateSuccessResponse("", w)
	}
}

// Handler is the production-ready HTTP handler using default dependencies.
var Handler = HandlerWithDeps(HandlerDeps{
	UserInfoFromContext: authorizer.UserInfoFromContext,
	GetConnections:      connect.GetConnections,
	ParseRequest:        parseRequest,
	InsertInstruction:   insertInstruction,
})

// Insert into SoftwareGatewayInstruction table
var insertInstruction = func(pg connect.DatabaseExecutor, userID int64, instruction string, deviceID int64) error {
	query := `
	INSERT INTO {{SoftwareGatewayInstruction}} (
		UserId,
		DeviceId,
		Instruction,
		DateQueuedUTC,
		Status
	)
	SELECT $1, $2, $3, $4, $5
	FROM {{UserDevice}} as ud
	WHERE ud.UserId = $1 AND ud.DeviceId = $2
	LIMIT 1`
	result, err := pg.Exec(query, userID, deviceID, instruction, time.Now().UTC(), "queued")
	if err != nil {
		return err
	}
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return err
	}
	logger.Debugf("(%v) instruction delivered - userId: (%v), deviceId: (%v) instruction set", rowsAffected, userID, deviceID)
	return nil
}

// Parse request
var parseRequest = func(r *http.Request) (*userInstructionRequest, error) {
	var body userInstructionRequest
	decorder := json.NewDecoder(r.Body)
	err := decorder.Decode(&body)
	if err != nil {
		logger.Error(ErrDecodeRequest.Error())
		return nil, ErrDecodeRequest
	}

	// Validate the instruction passed in is valid
	if !supportedInstructions[body.Instruction] {
		logger.Error(ErrInvalidInstructionRequest.Error())
		return nil, ErrInvalidInstructionRequest
	}

	return &body, nil
}

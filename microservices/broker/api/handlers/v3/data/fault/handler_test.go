package fault

import (
	"context"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	authorizer "synapse-its.com/shared/api/authorizer"
	"synapse-its.com/shared/connect"
	edihelper "synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/mocks/bqexecutor"
	"synapse-its.com/shared/mocks/dbexecutor"
	"synapse-its.com/shared/schemas"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// backup & restore our package-level overrides
var (
	origUserInfoFromContext = userInfoFromContext
	origParseRequest        = parseRequest
	origGetPGDeviceDetail   = getPGDeviceDetail
	origGetBQLogs           = getBQLogs
	origJSONMarshalIndent   = jsonMarshalIndentFn
	origJSONClean           = jsonCleanFn
)

func teardown() {
	userInfoFromContext = origUserInfoFromContext
	parseRequest = origParseRequest
	getPGDeviceDetail = origGetPGDeviceDetail
	getBQLogs = origGetBQLogs
	jsonMarshalIndentFn = origJSONMarshalIndent
	jsonCleanFn = origJSONClean
}

// a tiny helper to grab HTTP status codes
func doRequest(t *testing.T, req *http.Request) *httptest.ResponseRecorder {
	w := httptest.NewRecorder()
	Handler(w, req)
	return w
}

func TestHandler_UserInfoMissing(t *testing.T) {
	defer teardown()
	// simulate no JWT info
	userInfoFromContext = func(ctx context.Context) (*authorizer.UserInfo, bool) {
		return nil, false
	}

	req := httptest.NewRequest("GET", "/?deviceid=42", nil)
	w := doRequest(t, req)
	assert.Equal(t, http.StatusInternalServerError, w.Code)
}

func TestHandler_ParseRequestError(t *testing.T) {
	defer teardown()
	// stub valid JWT
	userInfoFromContext = func(ctx context.Context) (*authorizer.UserInfo, bool) {
		return &authorizer.UserInfo{ID: 1}, true
	}
	// stub parseRequest to fail
	parseRequest = func(r *http.Request) (int, error) {
		return 0, errors.New("bad query")
	}

	req := httptest.NewRequest("GET", "/?deviceid=notanint", nil)
	w := doRequest(t, req)
	assert.Equal(t, http.StatusUnauthorized, w.Code)
}

func TestHandler_GetConnectionsError(t *testing.T) {
	defer teardown()
	userInfoFromContext = func(ctx context.Context) (*authorizer.UserInfo, bool) {
		return &authorizer.UserInfo{ID: 2}, true
	}
	parseRequest = func(r *http.Request) (int, error) {
		return 123, nil
	}

	origGetConns := connect.GetConnections
	connect.GetConnections = func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
		return nil, errors.New("fail connect")
	}
	defer func() { connect.GetConnections = origGetConns }()

	req := httptest.NewRequest("GET", "/?deviceid=123", nil)
	w := doRequest(t, req)
	assert.Equal(t, http.StatusInternalServerError, w.Code)
}

func TestHandler_PGDeviceDetailError(t *testing.T) {
	defer teardown()
	userInfoFromContext = func(ctx context.Context) (*authorizer.UserInfo, bool) {
		return &authorizer.UserInfo{ID: 3}, true
	}
	parseRequest = func(r *http.Request) (int, error) {
		return 777, nil
	}

	// make GetConnections succeed
	origGetConns := connect.GetConnections
	connect.GetConnections = func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
		return &connect.Connections{}, nil
	}
	defer func() { connect.GetConnections = origGetConns }()

	// stub PG lookup to fail
	getPGDeviceDetail = func(pg connect.DatabaseExecutor, userID int64, deviceId int) (*PGDeviceDetail, error) {
		return nil, errors.New("db error")
	}

	req := httptest.NewRequest("GET", "/?deviceid=777", nil)
	w := doRequest(t, req)
	assert.Equal(t, http.StatusInternalServerError, w.Code)
}

func TestHandler_BQLogsError(t *testing.T) {
	defer teardown()
	userInfoFromContext = func(ctx context.Context) (*authorizer.UserInfo, bool) {
		return &authorizer.UserInfo{ID: 4}, true
	}
	parseRequest = func(r *http.Request) (int, error) {
		return 888, nil
	}

	// make GetConnections & PG succeed
	origGetConns := connect.GetConnections
	connect.GetConnections = func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
		return &connect.Connections{}, nil
	}
	defer func() { connect.GetConnections = origGetConns }()

	getPGDeviceDetail = func(pg connect.DatabaseExecutor, userID int64, deviceId int) (*PGDeviceDetail, error) {
		return &PGDeviceDetail{DeviceID: "dev", SoftWareGateWayID: "gw", OrganizationID: "org"}, nil
	}
	// stub BQ to error
	getBQLogs = func(bq connect.BigQueryExecutorInterface, deviceID, softwaregatewayID, orgID string, filterDays ...int) (schemas.AllLogs, error) {
		return schemas.AllLogs{}, errors.New("bq error")
	}

	req := httptest.NewRequest("GET", "/?deviceid=888", nil)
	w := doRequest(t, req)
	assert.Equal(t, http.StatusInternalServerError, w.Code)
}

func TestHandler_Success_DefaultSchema(t *testing.T) {
	defer teardown()
	userInfoFromContext = func(ctx context.Context) (*authorizer.UserInfo, bool) {
		return &authorizer.UserInfo{ID: 5}, true
	}
	parseRequest = func(r *http.Request) (int, error) {
		return 999, nil
	}

	// stub connect & PG
	origGetConns := connect.GetConnections
	connect.GetConnections = func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
		return &connect.Connections{}, nil
	}
	defer func() { connect.GetConnections = origGetConns }()

	getPGDeviceDetail = func(pg connect.DatabaseExecutor, userID int64, deviceId int) (*PGDeviceDetail, error) {
		return &PGDeviceDetail{
			DeviceID:                 "dev",
			SoftWareGateWayID:        "gw",
			OrganizationID:           "org",
			MonitorID:                11,
			MonitorName:              "X",
			RMSEngineFirmwareType:    1,
			RMSEngineFirmwareVersion: 2,
		}, nil
	}
	// stub BQ to return zero‐valued AllLogs with a Model that maps via default case
	getBQLogs = func(
		bq connect.BigQueryExecutorInterface,
		deviceID, softwaregatewayID, orgID string,
		filterDays ...int,
	) (schemas.AllLogs, error) {
		return schemas.AllLogs{
			FaultLogs: schemas.FaultLogsWithModel{
				Model:              int64(99999), // hits default → 16leip
				DeviceModel:        "",
				FirmwareReVision:   0,
				FirmwareVersion:    0,
				MonitorCommVersion: 0,
				PubsubTimestamp:    time.Time{}, // zero‐value
				RawLogMessages:     schemas.RawLogMessages{},
				// all other fields on FaultLogsWithModel default to zero
			},
			// LogMonitorReset, LogPreviousFail, LogConfiguration,
			// LogACLineEvent and LogFaultSignalSequence are omitted
			// and will default to their zero‐values—satisfying the converter.
		}, nil
	}

	req := httptest.NewRequest("GET", "/?deviceid=999", nil)
	w := doRequest(t, req)
	assert.Equal(t, http.StatusOK, w.Code)
}

// Test the ECL2010_FPlus path
func TestHandler_Success_ECL2010FPlusSchema(t *testing.T) {
	defer teardown()

	// 1) Stub JWT and request parsing
	userInfoFromContext = func(ctx context.Context) (*authorizer.UserInfo, bool) {
		return &authorizer.UserInfo{ID: 100}, true
	}
	parseRequest = func(r *http.Request) (int, error) {
		return 555, nil
	}

	// 2) Stub connections + Postgres lookup
	origGetConns := connect.GetConnections
	connect.GetConnections = func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
		return &connect.Connections{}, nil
	}
	defer func() { connect.GetConnections = origGetConns }()
	getPGDeviceDetail = func(pg connect.DatabaseExecutor, userID int64, deviceId int) (*PGDeviceDetail, error) {
		return &PGDeviceDetail{
			DeviceID:                 "devID",
			SoftWareGateWayID:        "gwID",
			OrganizationID:           "orgID",
			MonitorID:                7,
			MonitorName:              "MyMon",
			RMSEngineFirmwareType:    9,
			RMSEngineFirmwareVersion: 10,
		}, nil
	}

	// 3) Stub BQ to return ECL2010 model
	getBQLogs = func(bq connect.BigQueryExecutorInterface, deviceID, gwID, orgID string, filterDays ...int) (schemas.AllLogs, error) {
		return schemas.AllLogs{
			FaultLogs: schemas.FaultLogsWithModel{
				Model:              int64(edihelper.Ecl2010), // logs_edi_ecl2010_fplus.proto branch
				DeviceModel:        "MyModel",
				FirmwareReVision:   2,
				FirmwareVersion:    3,
				MonitorCommVersion: 4,
				PubsubTimestamp:    time.Now(),
				RawLogMessages:     schemas.RawLogMessages{},
			},
		}, nil
	}

	// 4) Exercise handler
	req := httptest.NewRequest("GET", "/?deviceid=555", nil)
	w := doRequest(t, req)

	// 5) Verify 200 OK and that the response is valid JSON containing our device model
	require.Equal(t, http.StatusOK, w.Code)
	var resp json.RawMessage
	require.NoError(t, json.Unmarshal(w.Body.Bytes(), &resp))
	assert.Contains(t, string(resp), `"model":"MyModel"`)
	assert.Contains(t, string(resp), `"schema":"logs_edi_ecl2010_fplus.proto"`)
}

// Test the CMU2212_Base path
func TestHandler_Success_CMU2212BaseSchema(t *testing.T) {
	defer teardown()

	userInfoFromContext = func(ctx context.Context) (*authorizer.UserInfo, bool) {
		return &authorizer.UserInfo{ID: 101}, true
	}
	parseRequest = func(r *http.Request) (int, error) {
		return 777, nil
	}

	origGetConns := connect.GetConnections
	connect.GetConnections = func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
		return &connect.Connections{}, nil
	}
	defer func() { connect.GetConnections = origGetConns }()
	getPGDeviceDetail = func(pg connect.DatabaseExecutor, userID int64, deviceId int) (*PGDeviceDetail, error) {
		return &PGDeviceDetail{
			DeviceID:                 "devID2",
			SoftWareGateWayID:        "gwID2",
			OrganizationID:           "orgID2",
			MonitorID:                8,
			MonitorName:              "YourMon",
			RMSEngineFirmwareType:    11,
			RMSEngineFirmwareVersion: 12,
		}, nil
	}

	getBQLogs = func(bq connect.BigQueryExecutorInterface, deviceID, gwID, orgID string, filterDays ...int) (schemas.AllLogs, error) {
		return schemas.AllLogs{
			FaultLogs: schemas.FaultLogsWithModel{
				Model:              int64(edihelper.CMUip2212_hv), // logs_edi_cmu2212_base.proto branch
				DeviceModel:        "YourModel",
				FirmwareReVision:   5,
				FirmwareVersion:    6,
				MonitorCommVersion: 7,
				PubsubTimestamp:    time.Now(),
				RawLogMessages:     schemas.RawLogMessages{},
			},
		}, nil
	}

	req := httptest.NewRequest("GET", "/?deviceid=777", nil)
	w := doRequest(t, req)

	require.Equal(t, http.StatusOK, w.Code)
	var resp json.RawMessage
	require.NoError(t, json.Unmarshal(w.Body.Bytes(), &resp))
	assert.Contains(t, string(resp), `"model":"YourModel"`)
	assert.Contains(t, string(resp), `"schema":"logs_edi_cmu2212_base.proto"`)
}

func TestHandler_JSONMarshalError(t *testing.T) {
	defer teardown()
	// normal user + parse + connect + PG + BQ success stubs...
	userInfoFromContext = func(ctx context.Context) (*authorizer.UserInfo, bool) {
		return &authorizer.UserInfo{ID: 200}, true
	}
	parseRequest = func(r *http.Request) (int, error) { return 1, nil }
	origGetConns := connect.GetConnections
	connect.GetConnections = func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
		return &connect.Connections{}, nil
	}
	defer func() { connect.GetConnections = origGetConns }()
	getPGDeviceDetail = func(pg connect.DatabaseExecutor, userID int64, deviceId int) (*PGDeviceDetail, error) {
		return &PGDeviceDetail{
			DeviceID:                 "d", // string
			SoftWareGateWayID:        "g", // string
			MonitorID:                0,   // int64 (untyped 0 works)
			MonitorName:              "",  // string
			RMSEngineFirmwareType:    0,   // int64
			RMSEngineFirmwareVersion: 0,   // int64
			OrganizationID:           "o", // string
		}, nil
	}
	getBQLogs = func(bq connect.BigQueryExecutorInterface, did, gid, oid string, f ...int) (schemas.AllLogs, error) {
		return schemas.AllLogs{FaultLogs: schemas.FaultLogsWithModel{Model: int64(edihelper.Mmu16le)}}, nil
	}

	// force JSON‐marshal error
	jsonMarshalIndentFn = func(v interface{}, prefix, indent string) ([]byte, error) {
		return nil, errors.New("marshal fail")
	}

	w := doRequest(t, httptest.NewRequest("GET", "/?deviceid=1", nil))
	assert.Equal(t, http.StatusInternalServerError, w.Code)
}

func TestHandler_JSONCleanError(t *testing.T) {
	defer teardown()
	// same stubs for everything
	userInfoFromContext = func(ctx context.Context) (*authorizer.UserInfo, bool) {
		return &authorizer.UserInfo{ID: 201}, true
	}
	parseRequest = func(r *http.Request) (int, error) { return 1, nil }
	origGetConns := connect.GetConnections
	connect.GetConnections = func(ctx context.Context, _ ...bool) (*connect.Connections, error) {
		return &connect.Connections{}, nil
	}
	defer func() { connect.GetConnections = origGetConns }()
	getPGDeviceDetail = func(pg connect.DatabaseExecutor, userID int64, deviceId int) (*PGDeviceDetail, error) {
		return &PGDeviceDetail{
			DeviceID:                 "d", // string
			SoftWareGateWayID:        "g", // string
			MonitorID:                0,   // int64 (untyped 0 works)
			MonitorName:              "",  // string
			RMSEngineFirmwareType:    0,   // int64
			RMSEngineFirmwareVersion: 0,   // int64
			OrganizationID:           "o", // string
		}, nil
	}
	getBQLogs = func(bq connect.BigQueryExecutorInterface, did, gid, oid string, f ...int) (schemas.AllLogs, error) {
		return schemas.AllLogs{FaultLogs: schemas.FaultLogsWithModel{Model: int64(edihelper.Mmu16le)}}, nil
	}

	// JSON‐marshal works normally
	jsonMarshalIndentFn = origJSONMarshalIndent
	// force JSON‐clean error
	jsonCleanFn = func(raw []byte) ([]byte, error) {
		return nil, errors.New("clean fail")
	}

	w := doRequest(t, httptest.NewRequest("GET", "/?deviceid=1", nil))
	assert.Equal(t, http.StatusInternalServerError, w.Code)
}

func TestGetPGDeviceDetail_Success_WithFakeDBExecutor(t *testing.T) {
	// Prepare a FakeDBExecutor that will populate the dest with our expected data
	fdb := &dbexecutor.FakeDBExecutor{}
	expected := PGDeviceDetail{
		DeviceID:                 "dev-123",
		SoftWareGateWayID:        "gw-abc",
		MonitorID:                42,
		MonitorName:              "MonName",
		RMSEngineFirmwareType:    7,
		RMSEngineFirmwareVersion: 8,
		OrganizationID:           "org-xyz",
	}
	fdb.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
		// dest is *PGDeviceDetail
		pd := dest.(*PGDeviceDetail)
		*pd = expected
		return nil
	}

	got, err := getPGDeviceDetail(fdb /*userID*/, 99 /*deviceId*/, 123)
	require.NoError(t, err)
	require.NotNil(t, got)

	// Should exactly match the struct we injected
	assert.Equal(t, expected, *got)

	// And ensure QueryRowStruct was actually called once
	assert.Equal(t, 1, fdb.QueryRowStructCallCount)
}

func TestGetPGDeviceDetail_Error_WithFakeDBExecutor(t *testing.T) {
	// Prepare a FakeDBExecutor that always returns an error
	fdb := &dbexecutor.FakeDBExecutor{}
	wantErr := errors.New("db query failure")
	fdb.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
		return wantErr
	}

	got, err := getPGDeviceDetail(fdb /*userID*/, 1 /*deviceId*/, 2)
	// On error, should return nil detail and propagate the error
	assert.Nil(t, got)
	require.Error(t, err)
	assert.True(t, errors.Is(err, wantErr))

	// And ensure QueryRowStruct was actually called once
	assert.Equal(t, 1, fdb.QueryRowStructCallCount)
}

func TestGetBQLogs_Behavior(t *testing.T) {
	const (
		deviceID          = "dev-123"
		softwareGatewayID = "swg-456"
		orgID             = "org-789"
	)

	cases := []struct {
		name       string
		filterDays []int
		setup      func(fbq *bqexecutor.FakeBigQueryExecutor)
		wantLogs   schemas.AllLogs
		wantCalls  int
	}{
		{
			name:       "default 90 days, returns populated AllLogs",
			filterDays: nil,
			wantLogs:   schemas.AllLogs{FaultLogs: schemas.FaultLogsWithModel{Model: 99}},
			setup: func(fbq *bqexecutor.FakeBigQueryExecutor) {
				fbq.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					// args[0] must be default 90
					assert.Equal(t, 90, args[0])
					// args[1]==orgID, args[2]==softwareGatewayID, args[3]==deviceID
					assert.Equal(t, orgID, args[1])
					assert.Equal(t, softwareGatewayID, args[2])
					assert.Equal(t, deviceID, args[3])
					// populate dest
					*dest.(*schemas.AllLogs) = schemas.AllLogs{
						FaultLogs: schemas.FaultLogsWithModel{Model: 99},
					}
					return nil
				}
			},
			wantCalls: 1,
		},
		{
			name:       "custom days override",
			filterDays: []int{7},
			setup: func(fbq *bqexecutor.FakeBigQueryExecutor) {
				fbq.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					// args[0] must be our override 7
					assert.Equal(t, 7, args[0])
					return nil
				}
			},
			wantLogs:  schemas.AllLogs{}, // zero since we didn't populate
			wantCalls: 1,
		},
		{
			name:       "QueryRowStruct error is swallowed",
			filterDays: nil,
			setup: func(fbq *bqexecutor.FakeBigQueryExecutor) {
				fbq.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					return errors.New("simulated BQ error")
				}
			},
			wantLogs:  schemas.AllLogs{}, // zero on error
			wantCalls: 1,
		},
	}

	for _, tc := range cases {
		t.Run(tc.name, func(t *testing.T) {
			// build fake BigQuery executor (it embeds dbexecutor.FakeDBExecutor)
			fbq := &bqexecutor.FakeBigQueryExecutor{
				FakeDBExecutor: dbexecutor.FakeDBExecutor{},
			}
			tc.setup(fbq)

			got, err := getBQLogs(fbq, deviceID, softwareGatewayID, orgID, tc.filterDays...)
			assert.NoError(t, err, "getBQLogs should never return a Go error")
			assert.Equal(t, tc.wantLogs, got, "unexpected AllLogs result")
			assert.Equal(t, tc.wantCalls, fbq.QueryRowStructCallCount, "QueryRowStruct should be called exactly once")
		})
	}
}

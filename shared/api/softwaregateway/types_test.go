package softwaregateway

import (
	"reflect"
	"testing"

	test "synapse-its.com/shared/util/test"
)

func TestTypesJSONMarshalable_ByType(t *testing.T) {
	// Because we validate these types here to see if they json.<PERSON>() without
	// error, then when we call json.<PERSON>() in our code, we do not have to
	// check for an error, making our test coverage simpler to manage.
	test.AssertJSONTypeMarshalable(t, reflect.TypeOf(DeviceSettings{}))
	test.AssertJSONTypeMarshalable(t, reflect.TypeOf(GatewaySettings{}))
	test.AssertJSONTypeMarshalable(t, reflect.TypeOf(GoogleSettings{}))
	test.AssertJSONTypeMarshalable(t, reflect.TypeOf(CloudSettings{}))
	test.AssertJSONTypeMarshalable(t, reflect.TypeOf(GlobalSettings{}))
	test.AssertJSONTypeMarshalable(t, reflect.TypeOf(OnDemandPayload{}))
	test.AssertJSONTypeMarshalable(t, reflect.TypeOf(SoftwareGatewayUpdateResponse{}))
}

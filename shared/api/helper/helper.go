package helper

import (
	"bytes"
	"compress/flate"
	"io"

	"github.com/google/uuid"
)

// Allow tests to override how we construct the compressor.
var (
	flateNewWriter = flate.NewWriter
	flateNewReader = flate.NewReader
)

func CompressBytes(dataToBeCompressed []byte) (compressedResult []byte, err error) {
	var buf bytes.Buffer
	w, err := flateNewWriter(&buf, flate.DefaultCompression)
	if err != nil {
		return nil, err
	}
	_, err = w.Write(dataToBeCompressed)
	if err != nil {
		return nil, err
	}
	err = w.Close()
	if err != nil {
		return nil, err
	}
	return buf.Bytes(), nil
}

func DecompressBytes(compressedData []byte) (decompressedResult []byte, err error) {
	var out bytes.Buffer
	buf := bytes.NewBuffer(compressedData)
	r := flateNewReader(buf)
	_, err = io.Copy(&out, r)
	if err != nil {
		return nil, err
	}
	err = r.Close()
	if err != nil {
		return nil, err
	}
	return out.Bytes(), nil
}

func CreateGuid() (guid string) {
	return uuid.New().String()
}

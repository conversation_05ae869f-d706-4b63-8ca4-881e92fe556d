# Shared Protobuf Schemas

This repository contains the canonical `.proto` files and generated Go code for use across applications and systems.

It ensures a consistent schema definition and a reproducible Go code generation process via Docker.

---

## 🧠 Project Structure

```
.
├── infra/
│   └── docker-compose.tools.yml # Used to manually run go commands
├── models/                      # Original .proto files organized by package/domain and version
│   └── gateway/
│       ├── v1/
│       │   └── *.proto          # v1 versioned schemas
│       └── v2/                  # future versioned schemas
├── protomessages/               # Generated Go files output here
│   └── gateway/
│       ├── v1/
│       │   └── *.pb.go          # generated code for v1
│       └── v2/                  # generated code for future versions
├── scripts/
│   ├── generate.sh              # Bash script to invoke protoc with Go plugins
│   └── gen-proto.ps1            # PowerShell wrapper for Dockerized generation
├── Dockerfile                   # Containerized protoc + Go plugin environment
└── go.mod                       # Module used to resolve Go imports
```

---

## ⚙️ Requirements

- Docker
- (For Windows) PowerShell 5.1+ to use `gen-proto.ps1`
- Go 1.20+ (for consuming generated code)

---

## 🚀 How to Use

### 🔨 Generate Go Code

Use either of the following:

#### On Unix/macOS:
```bash
./scripts/generate.sh
```

#### On Windows (PowerShell):
```powershell
# Just generate
./scripts/gen-proto.ps1

# Rebuild the Docker image before generating
./scripts/gen-proto.ps1 -BuildImage
```

This will:
- Compile all `.proto` files under `models/`
- Place generated `.pb.go` files in `protomessages/`
- Run `go mod tidy` and `go mod download` to sync dependencies

### 🔁 Rebuild Image

The Dockerfile pins a consistent protoc + plugin setup. To update or rebuild:
```bash
docker build -t proto-builder .
```

---


## 📜 Conventions

- **Directory layout**

  - Place versioned protos in `models/<domain>/vX/`.
  - Generated code ends up under `protomessages/<domain>/vX/`.

- **Proto **``\
  Use the domain plus an underscore‐sanitized version number:

  ```proto
  package gateway.v1;
  ```

- **Go import path**\
  In your `.proto`, set:

  ```proto
  option go_package = "bitbucket.org/synapse-its/protobuf-schemas/protomessages/gateway/v1;gatewayv1";
  ```

  1. First part: module import path plus `protomessages/<domain>/vX`.  
  2. After semicolon: valid Go package name (e.g. `gatewayv1`).

- **Imports**\
  When importing a versioned proto in another file:

  ```proto
  import "gateway/v0.1/deviceLogs.proto";
  ```

---

## 🛠️ Updating Protos

1. Modify or add `.proto` files under the appropriate `models/<domain>/vX/` directory.
2. Ensure each file includes the correct `package` and `option go_package` reflecting its version.
3. Re-run the codegen script (`./scripts/generate.sh` or `gen-proto.ps1`).
4. Commit both the updated `.proto` files and the generated `.pb.go` files.

---

## 🤝 Contributing

- Keep proto definitions backwards-compatible when possible.
- Follow naming conventions and versioning across packages.
- Use consistent message structures and comments.

---

## 📬 Questions?
Reach out via your team’s internal support channel or open an issue in this repository.

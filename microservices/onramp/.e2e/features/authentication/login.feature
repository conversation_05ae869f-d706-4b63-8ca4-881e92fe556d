Feature: User authentication

  Background:
    Given I open the home page
    When I click the "Log in" button
    And I complete the Keycloak login form with username "foo" and password "bar"
    Then I should be redirected back to the application

  Scenario: Verify welcome message
    Then I should see the message "You're logged in!"

  Scenario: Successful logout
    When I click the "Log out" button
    Then I should be redirected back to the application
    And I should see the "Log in" button

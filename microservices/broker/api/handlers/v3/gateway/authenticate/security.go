package authenticate

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/base64"
	"encoding/hex"
	"errors"
	"fmt"
)

// Package-level variables allow injection for testing error paths.
var (
	randRead     = rand.Read
	base64Decode = base64.StdEncoding.DecodeString
	aesNewCipher = aes.NewCipher
	cipherNewGCM = cipher.NewGCM
)

// generateRandomTokenHex returns a hex-encoded random token of the specified length.
// It errors if length is negative or if the random source fails.
func generateRandomTokenHex(length uint) (string, error) {
	// 2 hex characters per byte
	sz := length / 2
	b := make([]byte, sz)
	if _, err := randRead(b); err != nil {
		return "", err
	}
	return hex.EncodeToString(b), nil
}

// decryptBase64EncodedString decodes a Base64 input, then decrypts it with AES-GCM using the provided key.
// Key must be 16, 24, or 32 bytes long. Returns an error on any failure along the path.
func decryptBase64EncodedString(encoded, key string) (string, error) {
	// Decode Base64
	data, err := base64Decode(encoded)
	if err != nil {
		return "", fmt.Errorf("base64 decode: %w", err)
	}

	// Initialize AES cipher
	block, err := aesNewCipher([]byte(key))
	if err != nil {
		return "", fmt.Errorf("creating cipher: %w", err)
	}

	// Wrap in GCM
	gcm, err := cipherNewGCM(block)
	if err != nil {
		return "", fmt.Errorf("creating GCM: %w", err)
	}

	nonceSize := gcm.NonceSize()
	if len(data) < nonceSize {
		return "", errors.New("ciphertext too short")
	}

	nonce, ciphertext := data[:nonceSize], data[nonceSize:]

	// Decrypt and authenticate
	plain, err := gcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return "", fmt.Errorf("decrypting data: %w", err)
	}
	return string(plain), nil
}

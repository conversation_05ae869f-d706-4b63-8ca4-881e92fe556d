package subscriptions

import (
	"context"
	"sync/atomic"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

// getHealthState is a helper to inspect the internal healthState map.
func getHealthState(m *Manager, name string) int32 {
	val, ok := m.healthState.Load(name)
	if !ok {
		return -1
	}
	return atomic.LoadInt32(val.(*int32))
}

func TestMinAndHealthMarking(t *testing.T) {
	tests := []struct {
		name          string
		a, b          int
		expectMin     int
		doHealthMark  bool
		markUnhealthy bool
		expectUnheal  bool
	}{
		{
			name:         "minSimple",
			a:            3,
			b:            5,
			expectMin:    3,
			doHealthMark: false,
		},
		{
			name:         "minEqual",
			a:            7,
			b:            7,
			expectMin:    7,
			doHealthMark: false,
		},
		{
			name:          "markUnhealthyThenHealthy",
			doHealthMark:  true,
			markUnhealthy: true,
			expectUnheal:  true,
		},
		{
			name:          "markHealthyAfterUnhealthy",
			doHealthMark:  true,
			markUnhealthy: true,
			expectUnheal:  false, // after marking healthy
		},
	}

	for _, tc := range tests {
		tc := tc
		t.Run(tc.name, func(t *testing.T) {
			t.Parallel()

			// Test min(a,b)
			got := min(tc.a, tc.b)
			if got != tc.expectMin {
				t.Errorf("min(%d, %d) = %d; expected %d", tc.a, tc.b, got, tc.expectMin)
			}

			// Test health‐marking when requested
			m := NewManager(nil, BackoffConfig{})
			if tc.doHealthMark {
				m.MarkUnhealthy("subX")
				assert.True(t, m.IsAnyUnhealthy(), "expected IsAnyUnhealthy() to be true after markUnhealthy")
				assert.Equal(t, int32(1), getHealthState(m, "subX"), "health state after markUnhealthy")

				m.MarkHealthy("subX")
				assert.False(t, m.IsAnyUnhealthy(), "expected IsAnyUnhealthy() to be false after markHealthy")
				assert.Equal(t, int32(0), getHealthState(m, "subX"), "health state after markHealthy")
			} else {
				// Ensure no health entries exist initially
				assert.False(t, m.IsAnyUnhealthy(), "expected no subscriptions unhealthy initially")
			}
		})
	}
}

func TestRunWorkerScenarios(t *testing.T) {
	// Cannot add t.Parallel() due to precise timeouts
	// 1. cleanShutdownAfterHandler: handler cancels context, covers ctx.Err() branch.
	// 2. delayExceedsMax: handler returns immediately, triggers retry with delay > Max.
	// 3. neverReturn: handler blocks forever, Wait should time out.
	tests := []struct {
		name               string
		makeSubs           func(ctx context.Context, cancel context.CancelFunc) []Subscription
		backoff            BackoffConfig
		cancelImmediately  bool
		waitTimeout        time.Duration
		expectStragglerLen int
	}{
		{
			name: "cleanShutdownAfterHandler",
			makeSubs: func(ctx context.Context, cancel context.CancelFunc) []Subscription {
				return []Subscription{
					{
						Name: "cleanShutdown",
						Handler: func(ctx context.Context, s string) {
							cancel() // cancel the context then return
						},
					},
				}
			},
			backoff: BackoffConfig{
				Initial:    10 * time.Millisecond,
				Max:        50 * time.Millisecond,
				MaxRetries: 1,
			},
			// Do not cancel immediately; handler itself will cancel
			cancelImmediately:  false,
			waitTimeout:        100 * time.Millisecond,
			expectStragglerLen: 0,
		},
		{
			name: "delayExceedsMax",
			makeSubs: func(ctx context.Context, cancel context.CancelFunc) []Subscription {
				return []Subscription{
					{
						Name: "overflowDelay",
						Handler: func(ctx context.Context, s string) {
							// return immediately, triggers retry
						},
					},
				}
			},
			backoff: BackoffConfig{
				Initial:    10 * time.Millisecond,
				Max:        5 * time.Millisecond,
				MaxRetries: 1,
			},
			cancelImmediately:  false,
			waitTimeout:        100 * time.Millisecond,
			expectStragglerLen: 0,
		},
		{
			name: "neverReturn",
			makeSubs: func(ctx context.Context, cancel context.CancelFunc) []Subscription {
				return []Subscription{
					{
						Name: "hang",
						Handler: func(ctx context.Context, s string) {
							select {} // never returns
						},
					},
				}
			},
			backoff: BackoffConfig{
				Initial:    time.Millisecond,
				Max:        time.Millisecond,
				MaxRetries: 1,
			},
			cancelImmediately:  false,
			waitTimeout:        10 * time.Millisecond,
			expectStragglerLen: 1,
		},
	}

	for _, tc := range tests {
		tc := tc
		t.Run(tc.name, func(t *testing.T) {
			ctx, cancel := context.WithCancel(context.Background())
			defer cancel()

			if tc.cancelImmediately {
				cancel()
			}

			subs := tc.makeSubs(ctx, cancel)
			m := NewManager(subs, tc.backoff)
			m.Start(ctx)

			stragglers := m.Wait(tc.waitTimeout)
			assert.Len(t, stragglers, tc.expectStragglerLen, "[%s] straggler length", tc.name)
			if tc.expectStragglerLen > 0 {
				expected := []string{subs[0].Name}
				assert.Equal(t, expected, stragglers, "[%s] expected stragglers", tc.name)
			}
		})
	}
}

func TestInitialContextCancelled(t *testing.T) {
	// Cannot add t.Parallel() due to timing of Start() -> exit
	// Make one subscription whose handler should never be invoked.
	handlerInvoked := false
	subs := []Subscription{
		{
			Name: "shouldNotRun",
			Handler: func(ctx context.Context, s string) {
				handlerInvoked = true
			},
		},
	}

	// Create a context that is already cancelled.
	ctx, cancel := context.WithCancel(context.Background())
	cancel() // cancel before calling Start

	// Use a small backoff; it shouldn't matter since runWorker should exit immediately.
	bc := BackoffConfig{
		Initial:    1 * time.Millisecond,
		Max:        1 * time.Millisecond,
		MaxRetries: 1,
	}

	m := NewManager(subs, bc)
	m.Start(ctx)

	// Wait with some timeout
	stragglers := m.Wait(50 * time.Millisecond)
	assert.Len(t, stragglers, 0, "no stragglers expected")
	assert.False(t, handlerInvoked, "handler should not be invoked")
}

func TestWaitMixedWorkers(t *testing.T) {
	// Cannot add t.Parallel() due to precise sleep timing.

	// TestWaitMixedWorkers creates two subscriptions:
	//  1. One handler returns immediately, causing that worker to exit (w.Done closes).
	//  2. One handler hangs forever.
	// Then Wait should timeout and, when iterating the workers, see that one w.Done is closed
	// (hitting the "case <-w.Done:" branch) and the other is still running (hitting default).
	subFast := Subscription{
		Name: "fastWorker",
		Handler: func(ctx context.Context, s string) {
			// do nothing and return immediately
		},
	}

	subHang := Subscription{
		Name: "hangWorker",
		Handler: func(ctx context.Context, s string) {
			// hang forever
			select {}
		},
	}

	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Use a very small backoff so that fastWorker returns quickly.
	bc := BackoffConfig{
		Initial:    1 * time.Millisecond,
		Max:        1 * time.Millisecond,
		MaxRetries: 1,
	}

	m := NewManager([]Subscription{subFast, subHang}, bc)
	m.Start(ctx)

	// Give a bit of time for fastWorker to complete its single retry loop and exit.
	time.Sleep(5 * time.Millisecond)

	// Now call Wait with a small timeout; it should see fastWorker's w.Done closed and hangWorker still running.
	stragglers := m.Wait(10 * time.Millisecond)

	// We expect exactly ["hangWorker"] as the only straggler.
	assert.Len(t, stragglers, 1, "exactly one straggler expected")
	assert.Equal(t, "hangWorker", stragglers[0], "hangWorker should be the only straggler")
}

func TestRunWorkerSelectCtxDone(t *testing.T) {
	// Cannot add t.Parallel() due to timing of test
	sub := Subscription{
		Name: "delayWorker",
		Handler: func(ctx context.Context, s string) {
			// returns immediately -> will hit the retry‐delay select
		},
	}

	ctx, cancel := context.WithCancel(context.Background())
	// Use a backoff that gives a non‐zero delay.
	bc := BackoffConfig{
		Initial:    50 * time.Millisecond,
		Max:        50 * time.Millisecond,
		MaxRetries: 1,
	}

	m := NewManager([]Subscription{sub}, bc)
	m.Start(ctx)

	// Allow runWorker to invoke the handler and reach the retry select.
	time.Sleep(10 * time.Millisecond)
	// Now cancel the context while runWorker is blocked in `select { case <-ctx.Done(): ... }`.
	cancel()

	// Wait long enough to let runWorker return after picking up ctx.Done in the select.
	stragglers := m.Wait(100 * time.Millisecond)
	assert.Len(t, stragglers, 0, "no stragglers expected once ctx.Done is selected")
}

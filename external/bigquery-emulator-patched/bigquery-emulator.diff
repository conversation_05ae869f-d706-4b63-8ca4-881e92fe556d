diff --git a/internal/contentdata/repository.go b/internal/contentdata/repository.go
index 7be8c3b..409326c 100644
--- a/internal/contentdata/repository.go
+++ b/internal/contentdata/repository.go
@@ -221,11 +221,7 @@ func (r *Repository) Query(ctx context.Context, tx *connection.Tx, projectID, da
 		resultValues := make([]interface{}, 0, len(values))
 		for idx, value := range values {
 			v := reflect.ValueOf(value).Elem().Interface()
-			if v == nil && fields[idx].Mode == string(types.RepeatedMode) {
-				// GoogleSQL for BigQuery translates a NULL array into an empty array in the query result
-				v = []interface{}{}
-			}
-			cell, err := r.convertValueToCell(v)
+			cell, err := r.convertValueToCell(v, fields[idx])
 			if err != nil {
 				return nil, fmt.Errorf("failed to convert value to cell: %w", err)
 			}
@@ -283,53 +279,110 @@ func (r *Repository) queryParameterValueToGoValue(value *bigqueryv2.QueryParamet
 
 // zetasqlite returns []map[string]interface{} value as struct value, also returns []interface{} value as array value.
 // we need to convert them to specifically TableRow and TableCell type.
-func (r *Repository) convertValueToCell(value interface{}) (*internaltypes.TableCell, error) {
+func (r *Repository) convertValueToCell(value interface{}, schema *bigqueryv2.TableFieldSchema) (*internaltypes.TableCell, error) {
 	if value == nil {
-		return &internaltypes.TableCell{V: nil}, nil
-	}
-	rv := reflect.ValueOf(value)
-	kind := rv.Type().Kind()
-	if kind != reflect.Slice && kind != reflect.Array {
-		v := fmt.Sprint(value)
-		return &internaltypes.TableCell{V: v, Bytes: int64(len(v))}, nil
-	}
-	elemType := rv.Type().Elem()
-	if elemType.Kind() == reflect.Map {
-		// value is struct type
-		var (
-			cells      []*internaltypes.TableCell
-			totalBytes int64
-		)
-		for i := 0; i < rv.Len(); i++ {
-			fieldV := rv.Index(i)
-			keys := fieldV.MapKeys()
-			if len(keys) != 1 {
-				return nil, fmt.Errorf("unexpected key number of field map value. expected 1 but got %d", len(keys))
+		if schema.Mode == "REPEATED" {
+			// GoogleSQL for BigQuery translates a NULL array into an empty array in the query result
+			return &internaltypes.TableCell{
+				V:     []*internaltypes.TableCell{},
+				Bytes: 0,
+			}, nil
+		}
+		return &internaltypes.TableCell{
+			V:     nil,
+			Bytes: 0,
+		}, nil
+	}
+	if schema.Mode == "REPEATED" {
+		arr, ok := value.([]interface{})
+		if !ok {
+			return nil, fmt.Errorf(
+				`field %q: expected []interface{} for REPEATED, got %T`,
+				schema.Name, value,
+			)
+		}
+		cells := make([]*internaltypes.TableCell, 0, len(arr))
+		var totalBytes int64
+		if schema.Type == "RECORD" {
+			for _, rawElem := range arr {
+				m, ok := rawElem.(map[string]interface{})
+				if !ok {
+					return nil, fmt.Errorf(
+						`field %q: expected map[string]interface{} inside REPEATED RECORD, got %T`,
+						schema.Name, rawElem,
+					)
+				}
+				var childCells []*internaltypes.TableCell
+				var childBytes int64
+				for _, childSchema := range schema.Fields {
+					rawChild := m[childSchema.Name]
+					childCell, err := r.convertValueToCell(rawChild, childSchema)
+					if err != nil {
+						return nil, err
+					}
+					childCell.Name = childSchema.Name
+					childBytes += childCell.Bytes
+					childCells = append(childCells, childCell)
+				}
+				cells = append(cells, &internaltypes.TableCell{
+					V:     internaltypes.TableRow{F: childCells},
+					Bytes: childBytes,
+				})
+				totalBytes += childBytes
 			}
-			cell, err := r.convertValueToCell(fieldV.MapIndex(keys[0]).Interface())
+			return &internaltypes.TableCell{
+				V:     cells,
+				Bytes: totalBytes,
+			}, nil
+		}
+		elemSchema := &bigqueryv2.TableFieldSchema{
+			Name: schema.Name + "_elem",
+			Type: schema.Type,
+			Mode: "",
+		}
+		for _, rawElem := range arr {
+			childCell, err := r.convertValueToCell(rawElem, elemSchema)
 			if err != nil {
 				return nil, err
 			}
-			cell.Name = keys[0].Interface().(string)
-			totalBytes += cell.Bytes
-			cells = append(cells, cell)
+			totalBytes += childCell.Bytes
+			cells = append(cells, childCell)
 		}
-		return &internaltypes.TableCell{V: internaltypes.TableRow{F: cells}, Bytes: totalBytes}, nil
+		return &internaltypes.TableCell{
+			V:     cells,
+			Bytes: totalBytes,
+		}, nil
 	}
-	// array type
-	var (
-		cells            = []*internaltypes.TableCell{}
-		totalBytes int64 = 0
-	)
-	for i := 0; i < rv.Len(); i++ {
-		cell, err := r.convertValueToCell(rv.Index(i).Interface())
-		if err != nil {
-			return nil, err
+	if schema.Type == "RECORD" {
+		m, ok := value.(map[string]interface{})
+		if !ok {
+			return nil, fmt.Errorf(
+				`field %q: expected map[string]interface{} for RECORD, got %T`,
+				schema.Name, value,
+			)
 		}
-		totalBytes += cell.Bytes
-		cells = append(cells, cell)
-	}
-	return &internaltypes.TableCell{V: cells, Bytes: totalBytes}, nil
+		cells := make([]*internaltypes.TableCell, 0, len(schema.Fields))
+		var totalBytes int64
+		for _, childSchema := range schema.Fields {
+			rawChild := m[childSchema.Name]
+			childCell, err := r.convertValueToCell(rawChild, childSchema)
+			if err != nil {
+				return nil, err
+			}
+			childCell.Name = childSchema.Name
+			totalBytes += childCell.Bytes
+			cells = append(cells, childCell)
+		}
+		return &internaltypes.TableCell{
+			V:     internaltypes.TableRow{F: cells},
+			Bytes: totalBytes,
+		}, nil
+	}
+	s := fmt.Sprint(value)
+	return &internaltypes.TableCell{
+		V:     s,
+		Bytes: int64(len(s)),
+	}, nil
 }
 
 func (r *Repository) CreateOrReplaceTable(ctx context.Context, tx *connection.Tx, projectID, datasetID string, table *types.Table) error {
@@ -395,26 +448,9 @@ func (r *Repository) AddTableData(ctx context.Context, tx *connection.Tx, projec
 	}
 
 	for _, data := range table.Data {
-		values := make([]interface{}, 0, len(table.Columns))
-
-		for _, column := range columns {
-			if value, found := data[column.Name]; found {
-				isTimestampColumn := column.Type == types.TIMESTAMP
-				inputString, isInputString := value.(string)
-
-				if isInputString && isTimestampColumn {
-					parsedTimestamp, err := zetasqlite.TimeFromTimestampValue(inputString)
-					// If we could parse the timestamp, use it when inserting, otherwise fallback to the supplied value
-					if err == nil {
-						values = append(values, parsedTimestamp)
-						continue
-					}
-				}
-
-				values = append(values, value)
-			} else {
-				values = append(values, nil)
-			}
+		values, err := r.addTableDataParseTimestamp(data, columns)
+		if err != nil {
+			return err
 		}
 
 		if _, err := stmt.ExecContext(ctx, values...); err != nil {
@@ -425,6 +461,97 @@ func (r *Repository) AddTableData(ctx context.Context, tx *connection.Tx, projec
 	return nil
 }
 
+func (r *Repository) addTableDataParseTimestamp(
+	rowData map[string]interface{},
+	columns []*types.Column,
+) ([]interface{}, error) {
+	parsed := make([]interface{}, 0, len(columns))
+	var parseValue func(raw interface{}, col *types.Column) (interface{}, error)
+	parseValue = func(raw interface{}, col *types.Column) (interface{}, error) {
+		if raw == nil {
+			if col.Mode == types.RepeatedMode {
+				return []interface{}{}, nil
+			}
+			return nil, nil
+		}
+		if col.Mode == types.RepeatedMode {
+			arr, ok := raw.([]interface{})
+			if !ok {
+				return nil, fmt.Errorf(
+					"column %q: expected []interface{} for REPEATED, got %T",
+					col.Name, raw,
+				)
+			}
+			out := make([]interface{}, 0, len(arr))
+			elemCol := &types.Column{
+				Name:   col.Name + "_elem",
+				Type:   col.Type,
+				Mode:   "",
+				Fields: col.Fields,
+			}
+			for _, elt := range arr {
+				n, err := parseValue(elt, elemCol)
+				if err != nil {
+					return nil, err
+				}
+				out = append(out, n)
+			}
+			return out, nil
+		}
+		if col.Type == types.RECORD || col.Type == types.STRUCT {
+			switch v := raw.(type) {
+			case []interface{}:
+				if len(v) > 0 {
+					raw = v[0]
+				} else {
+					return map[string]interface{}{}, nil
+				}
+			case []map[string]interface{}:
+				if len(v) > 0 {
+					raw = v[0]
+				} else {
+					return map[string]interface{}{}, nil
+				}
+			}
+			m, ok := raw.(map[string]interface{})
+			if !ok {
+				return nil, fmt.Errorf(
+					"column %q: expected map[string]interface{} for RECORD, got %T",
+					col.Name, raw,
+				)
+			}
+			outMap := make(map[string]interface{}, len(col.Fields))
+			for _, child := range col.Fields {
+				rc := m[child.Name]
+				nc, err := parseValue(rc, child)
+				if err != nil {
+					return nil, err
+				}
+				outMap[child.Name] = nc
+			}
+			return outMap, nil
+		}
+		if col.Type == types.TIMESTAMP {
+			if s, ok := raw.(string); ok {
+				if t, err := zetasqlite.TimeFromTimestampValue(s); err == nil {
+					return t, nil
+				}
+			}
+			return raw, nil
+		}
+		return raw, nil
+	}
+	for _, col := range columns {
+		rawVal := rowData[col.Name]
+		n, err := parseValue(rawVal, col)
+		if err != nil {
+			return nil, err
+		}
+		parsed = append(parsed, n)
+	}
+	return parsed, nil
+}
+
 func (r *Repository) DeleteTables(ctx context.Context, tx *connection.Tx, projectID, datasetID string, tableIDs []string) error {
 	tx.SetProjectAndDataset(projectID, datasetID)
 	if err := tx.ContentRepoMode(); err != nil {
diff --git a/internal/types/types.go b/internal/types/types.go
index ba8bef6..c0c6ee0 100644
--- a/internal/types/types.go
+++ b/internal/types/types.go
@@ -2,6 +2,7 @@ package types
 
 import (
 	"fmt"
+	"strconv"
 	"time"
 
 	"github.com/apache/arrow/go/v10/arrow/array"
@@ -184,27 +185,128 @@ func (c *TableCell) AppendValueToARROWBuilder(builder array.Builder) error {
 	}
 }
 
+// Format rewrites TIMESTAMP values in the rows to int64 microseconds since epoch.
 func Format(schema *bigqueryv2.TableSchema, rows []*TableRow, useInt64Timestamp bool) []*TableRow {
 	if !useInt64Timestamp {
 		return rows
 	}
-	formattedRows := make([]*TableRow, 0, len(rows))
-	for _, row := range rows {
-		cells := make([]*TableCell, 0, len(row.F))
+	formattedRows := make([]*TableRow, len(rows))
+	for rowIdx, row := range rows {
+		cells := make([]*TableCell, len(row.F))
 		for colIdx, cell := range row.F {
-			if schema.Fields[colIdx].Type == "TIMESTAMP" && cell.V != nil {
-				t, _ := zetasqlite.TimeFromTimestampValue(cell.V.(string))
-				microsec := t.UnixNano() / int64(time.Microsecond)
-				cells = append(cells, &TableCell{
-					V: fmt.Sprint(microsec),
-				})
-			} else {
-				cells = append(cells, cell)
-			}
+			formattedCell, _ := formatCell(schema.Fields[colIdx], cell)
+			cells[colIdx] = formattedCell
 		}
-		formattedRows = append(formattedRows, &TableRow{
+		formattedRows[rowIdx] = &TableRow{
 			F: cells,
-		})
+		}
 	}
 	return formattedRows
 }
+
+// formatCell recursively rewrites TIMESTAMP fields to int64 microseconds and
+// recalculates the total bytes for each cell.
+func formatCell(schema *bigqueryv2.TableFieldSchema, cell *TableCell) (*TableCell, int64) {
+	if cell.V == nil {
+		return cell, 0
+	}
+	if schema.Mode == "REPEATED" {
+		entries, ok := cell.V.([]*TableCell)
+		if !ok {
+			return cell, 0
+		}
+		totalBytes := int64(0)
+		if schema.Type == "RECORD" {
+			records := make([]*TableCell, len(entries))
+			for i, record := range entries {
+				partialTotalBytes := int64(0)
+				recordTable, ok := record.V.(TableRow)
+				if !ok {
+					records[i] = record
+					totalBytes += record.Bytes
+					continue
+				}
+				newCells := make([]*TableCell, len(recordTable.F))
+				for j, cell := range recordTable.F {
+					formattedCell, bytes := formatCell(schema.Fields[j], cell)
+					newCells[j] = formattedCell
+					partialTotalBytes += bytes
+				}
+				records[i] = &TableCell{
+					V: &TableRow{
+						F: newCells,
+					},
+					Bytes: partialTotalBytes,
+				}
+				totalBytes += partialTotalBytes
+			}
+			return &TableCell{
+				V:     records,
+				Name:  cell.Name,
+				Bytes: totalBytes,
+			}, totalBytes
+		}
+		if schema.Type == "TIMESTAMP" {
+			newArr := make([]*TableCell, len(entries))
+			totalBytes := int64(0)
+			for i, cell := range entries {
+				if cell.V == nil {
+					newArr[i] = cell
+					totalBytes += cell.Bytes
+					continue
+				}
+				s, ok := cell.V.(string)
+				if !ok {
+					newArr[i] = cell
+					totalBytes += cell.Bytes
+					continue
+				}
+				t, _ := zetasqlite.TimeFromTimestampValue(s)
+				microsec := t.UnixNano() / int64(time.Microsecond)
+				newArr[i] = &TableCell{V: strconv.FormatInt(microsec, 10), Bytes: 16}
+				totalBytes += 16
+			}
+			return &TableCell{
+				V:     newArr,
+				Name:  cell.Name,
+				Bytes: totalBytes,
+			}, totalBytes
+		}
+		return cell, cell.Bytes
+	}
+	if schema.Type == "RECORD" {
+		recordTable, ok := cell.V.(TableRow)
+		if !ok {
+			return cell, cell.Bytes
+		}
+		childCells := recordTable.F
+		totalBytes := int64(0)
+		newChildCells := make([]*TableCell, len(childCells))
+		for i, childCell := range childCells {
+			formattedCell, bytes := formatCell(schema.Fields[i], childCell)
+			newChildCells[i] = formattedCell
+			totalBytes += bytes
+		}
+		return &TableCell{
+			V: TableRow{
+				F: newChildCells,
+			},
+			Bytes: totalBytes,
+			Name:  cell.Name,
+		}, totalBytes
+	}
+	if schema.Type == "TIMESTAMP" {
+		s, ok := cell.V.(string)
+		if !ok {
+			return cell, cell.Bytes
+		}
+		t, _ := zetasqlite.TimeFromTimestampValue(s)
+		microsec := t.UnixNano() / int64(time.Microsecond)
+		return &TableCell{
+			V:     strconv.FormatInt(microsec, 10),
+			Name:  cell.Name,
+			Bytes: 16,
+		}, 16
+	}
+	return cell, cell.Bytes
+}
diff --git a/server/handler.go b/server/handler.go
index 7492beb..664d911 100644
--- a/server/handler.go
+++ b/server/handler.go
@@ -507,11 +507,9 @@ func (h *uploadContentHandler) Handle(ctx context.Context, r *uploadContentReque
 			data = append(data, rowData.(map[string]interface{}))
 		}
 	case "NEWLINE_DELIMITED_JSON":
-		for _, f := range tableContent.Schema.Fields {
-			columns = append(columns, &types.Column{
-				Name: f.Name,
-				Type: types.Type(f.Type),
-			})
+		schemaFields := tableContent.Schema.Fields
+		for _, f := range schemaFields {
+			columns = append(columns, buildColumn(f))
 		}
 		columnMap := map[string]*types.Column{}
 		for _, col := range columns {
@@ -553,6 +551,23 @@ func (h *uploadContentHandler) Handle(ctx context.Context, r *uploadContentReque
 	return nil
 }
 
+func buildColumn(field *bigqueryv2.TableFieldSchema) *types.Column {
+	col := &types.Column{
+		Name:   field.Name,
+		Type:   types.Type(field.Type),
+		Mode:   types.Mode(field.Mode),
+		Fields: nil,
+	}
+	if field.Type == "RECORD" || field.Type == "STRUCT" {
+		childCols := make([]*types.Column, 0, len(field.Fields))
+		for _, childSchema := range field.Fields {
+			childCols = append(childCols, buildColumn(childSchema))
+		}
+		col.Fields = childCols
+	}
+	return col
+}
+
 const (
 	formatOptionsUseInt64TimestampParam = "formatOptions.useInt64Timestamp"
 	deleteContentsParam                 = "deleteContents"

package integration

import (
	"fmt"
	"os"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"google.golang.org/api/iterator"

	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/schema_mgmt"
	Utils "synapse-its.com/testing/utils"
)

// Test the GetMostRecentSchema function.
func TestGetMostRecentSchema(t *testing.T) {
	assert := assert.New(t)

	// Valid directory, valid semver directory names.
	_, err := schema_mgmt.GetMostRecentSchema("data-core-bq")
	assert.NoError(err, "should get most recent schema without error")

	// Invalid directory.
	_, err = schema_mgmt.GetMostRecentSchema("foo")
	assert.Error(err, "should return error for non-existent schema")

	// Valid directory, no semver directory names.
	_, err = schema_mgmt.GetMostRecentSchema("")
	assert.Error(err, "should return error for empty schema directory")
}

// getAllTables() is a helper function to get all tables in the dataset whose
// name starts with the config's namespace.  It supports multiple databases.
func getAllTables(db connect.DatabaseExecutor) []string {
	tables := []string{}
	switch dbType := db.(type) {
	case *connect.BigQueryExecutor:
		it := dbType.Client.Dataset(dbType.Config.DBName).Tables(dbType.Ctx)
		for {
			tbl, err := it.Next()
			if err == iterator.Done {
				break
			}
			if err != nil {
				return nil
			}
			if strings.HasPrefix(tbl.TableID, dbType.Config.Namespace) {
				tables = append(tables, tbl.TableID)
			}
		}

	case *connect.PostgresExecutor:
		tablesList, err := dbType.QueryGeneric("SELECT table_name FROM information_schema.tables WHERE table_schema = 'public'")
		if err != nil {
			return nil
		}
		for _, tbl := range tablesList {
			if strings.HasPrefix(tbl["table_name"].(string), dbType.Config.Namespace) {
				tables = append(tables, tbl["table_name"].(string))
			}
		}

	default:
		return nil
	}
	return tables
}

// Test the GetSchemaVersion function (the happy path).
func TestApplyMigrations_Happy(t *testing.T) {
	const (
		version         = ""
		expectedVersion = schema_mgmt.SCHEMA_VERSION_MAX
	)

	baseConfig := connect.DatabaseConfig{
		Environment: "dev",
		Namespace:   "T_SM_HAPPY",
	}

	tests := []struct {
		name       string
		schemaName string
		makeDB     func(t *testing.T, cfg *connect.DatabaseConfig) (connect.DatabaseExecutor, error)
	}{
		{
			name:       "BigQuery",
			schemaName: "data-core-bq",
			makeDB: func(t *testing.T, cfg *connect.DatabaseConfig) (connect.DatabaseExecutor, error) {
				return connect.BigQuery(t.Context(), cfg, nil)
			},
		},
		{
			name:       "Postgres",
			schemaName: "data-core-pg",
			makeDB: func(t *testing.T, cfg *connect.DatabaseConfig) (connect.DatabaseExecutor, error) {
				cfg.DBName = os.Getenv("POSTGRES_DB")
				return connect.Postgres(t.Context(), cfg)
			},
		},
	}

	for i, tc := range tests {
		tc := tc // capture range variable
		t.Run(tc.name, func(t *testing.T) {
			assert := assert.New(t)
			require := require.New(t)

			// give each backend its own namespace
			cfg := baseConfig
			cfg.Namespace = fmt.Sprintf("%s_%d", cfg.Namespace, i)

			// 1) connect
			db, err := tc.makeDB(t, &cfg)
			require.NoError(err, "connect to %s", tc.name)

			// 2) before migrations: expect no tables
			pre := getAllTables(db)
			assert.Empty(pre, "no tables before migrations for %s", tc.name)

			// 3) apply migrations
			start := time.Now()
			switch tc.name {
			case "BigQuery":
				bq := db.(*connect.BigQueryExecutor)
				require.NoError(Utils.SetupBigQuery(bq, tc.schemaName, version))
			case "Postgres":
				pg := db.(*connect.PostgresExecutor)
				require.NoError(Utils.SetupPostgres(pg, tc.schemaName, version))
			}

			// 4) inspect first migration row
			rows, err := db.QueryGeneric(`
				SELECT sequence, update_file, md5_hash, applied_at
				FROM {{schema_migrations}}
				ORDER BY sequence ASC
				LIMIT 1
			`)
			require.NoError(err)
			require.Len(rows, 1)

			first := rows[0]
			assert.Equal(0.0, first["sequence"], "sequence==0")
			assert.Equal("", first["md5_hash"], "md5_hash empty")

			uf, ok := first["update_file"].(string)
			require.True(ok, "update_file is string")
			assert.True(strings.HasPrefix(uf, "SCHEMA_CREATION "), "prefix")

			parts := strings.Fields(uf)
			assert.Len(parts, 4, "update_file parts")

			kv := map[string]string{}
			for _, seg := range parts[1:] {
				pair := strings.SplitN(seg, ":", 2)
				require.Len(pair, 2, "segment %q", seg)
				kv[pair[0]] = pair[1]
			}

			assert.Equal(tc.schemaName, kv["SCHEMA"], "SCHEMA")
			assert.Equal(expectedVersion, kv["VERSION_DESIRED"], "VERSION_DESIRED")
			assert.Equal(cfg.Environment, kv["ENVIRONMENT"], "ENVIRONMENT")

			at, ok := first["applied_at"].(time.Time)
			require.True(ok, "applied_at is time.Time")
			assert.True(at.After(start) && at.Before(time.Now()), "applied_at window")

			// 5) idempotency
			beforeTables := getAllTables(db)
			require.NotEmpty(beforeTables, "tables exist after first run")

			beforeMigs, err := db.QueryGeneric(`
				SELECT sequence, update_file, md5_hash, applied_at
				FROM {{schema_migrations}}
				ORDER BY sequence ASC
			`)
			require.NoError(err)

			// run migrations again
			switch tc.name {
			case "BigQuery":
				bq := db.(*connect.BigQueryExecutor)
				require.NoError(Utils.SetupBigQuery(bq, tc.schemaName, version))
			case "Postgres":
				pg := db.(*connect.PostgresExecutor)
				require.NoError(Utils.SetupPostgres(pg, tc.schemaName, version))
			}

			afterTables := getAllTables(db)
			assert.ElementsMatch(beforeTables, afterTables, "tables unchanged")

			afterMigs, err := db.QueryGeneric(`
				SELECT sequence, update_file, md5_hash, applied_at
				FROM {{schema_migrations}}
				ORDER BY sequence ASC
			`)
			require.NoError(err)
			assert.Equal(len(beforeMigs), len(afterMigs), "migration count unchanged")
			assert.Equal(beforeMigs, afterMigs, "migration rows unchanged")
		})
	}
}

package authenticate

import (
	"bytes"
	"context"
	"database/sql"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	response "synapse-its.com/shared/api/response"
	connect "synapse-its.com/shared/connect"
	mocks "synapse-its.com/shared/mocks"
)

// Store original functions to restore after tests.
var (
	origNewUserHandlerFunc  = newUserHandlerFunc
	origNewTokenHandlerFunc = newTokenHandlerFunc
)

type ctxKey string

const (
	ctxKeyTesting     ctxKey = "testing"
	ctxKeyConnections ctxKey = "connections"
)

// setupTestContext creates a testing context with mock database connections.
func setupTestContext(req *http.Request) *http.Request {
	// Create a testing context.
	ctx := context.WithValue(req.Context(), ctxKeyTesting, true)

	// Create a mock database executor.
	fakeDB := &mocks.FakeDBExecutor{
		QueryRowStructFunc: func(dest interface{}, query string, args ...interface{}) error {
			// Cast dest to dbUser and populate it.
			user, ok := dest.(*dbUser)
			if ok {
				user.Id = 123
				user.RoleId = 1
				user.WebEnabled = 1
				user.WebTokenDurationSeconds = 3600
				user.TokenDurationHours = 24
				user.UserName = "testuser"
				user.OrganizationIdentifier = "testorg"
				user.APIKey = "testapikey"
				user.ExpirationUTC = time.Now().Add(time.Hour).Format(time.DateTime)
			}
			return nil
		},
		ExecFunc: func(query string, args ...interface{}) (sql.Result, error) {
			return &fakeResult{}, nil
		},
	}

	// Create a mock connections object.
	conns := &connect.Connections{
		Postgres: fakeDB,
	}

	// Add connections to context.
	ctx = context.WithValue(ctx, ctxKeyConnections, conns)

	return req.WithContext(ctx)
}

// Mock UserAccessor for testing.
type mockUserAccessor struct{}

// UserAccess implements the UserAccessor interface for testing.
func (m *mockUserAccessor) UserAccess(userName, password string, w http.ResponseWriter, r *http.Request) {
	// Always return success for testing.
	response.CreateAuthSuccessResponse(map[string]string{"token": "test-token"}, w)
}

// WebAccess implements the UserAccessor interface for testing.
func (m *mockUserAccessor) WebAccess(userName, password string, w http.ResponseWriter, r *http.Request) {
	// Always return success for testing.
	response.CreateAuthSuccessResponse(map[string]string{"token": "test-token"}, w)
}

// Mock TokenAccessor for testing.
type mockTokenAccessor struct{}

// TokenAccess implements the TokenAccessor interface for testing.
func (m *mockTokenAccessor) TokenAccess(token string, w http.ResponseWriter, r *http.Request) {
	// Always return success for testing.
	response.CreateAuthSuccessResponse(map[string]string{"token": "test-token"}, w)
}

// setupMockHandlers sets up mock handlers for testing.
func setupMockHandlers() {
	// Replace the handler functions with mock versions.
	newUserHandlerFunc = func() UserAccessor {
		return &mockUserAccessor{}
	}

	newTokenHandlerFunc = func() TokenAccessor {
		return &mockTokenAccessor{}
	}
}

// teardownMockHandlers restores the original handler functions.
func teardownMockHandlers() {
	newUserHandlerFunc = origNewUserHandlerFunc
	newTokenHandlerFunc = origNewTokenHandlerFunc
}

// TestHandler tests all scenarios for the Handler function using table-driven tests.
func TestHandler(t *testing.T) {
	// Define test cases.
	tests := []struct {
		name           string
		requestURL     string
		requestBody    string
		expectedStatus int
		description    string
	}{
		{
			name:           "NormalAuthRequest",
			requestURL:     "/authenticate",
			requestBody:    `{"username": "testuser", "password": "testpass"}`,
			expectedStatus: http.StatusOK,
			description:    "Normal authentication request with valid credentials",
		},
		{
			name:           "ExtractUserNameAndPasswordError",
			requestURL:     "/authenticate",
			requestBody:    `{"invalid": "json"}`,
			expectedStatus: http.StatusUnauthorized,
			description:    "Authentication request with invalid JSON body",
		},
		{
			name:           "WebAccessRequest",
			requestURL:     "/authenticate?webaccess=true",
			requestBody:    `{"username": "testuser", "password": "testpass"}`,
			expectedStatus: http.StatusOK,
			description:    "Web access request with valid credentials",
		},
		{
			name:           "WebAccessRequestInvalidParam",
			requestURL:     "/authenticate?webaccess=invalid",
			requestBody:    `{"username": "testuser", "password": "testpass"}`,
			expectedStatus: http.StatusUnauthorized,
			description:    "Web access request with invalid webaccess parameter",
		},
		{
			name:           "WebAccessRequestExtractError",
			requestURL:     "/authenticate?webaccess=true",
			requestBody:    `{"invalid": "json"}`,
			expectedStatus: http.StatusUnauthorized,
			description:    "Web access request with invalid JSON body",
		},
		{
			name:           "TokenAccessRequest",
			requestURL:     "/authenticate?token=" + strings.Repeat("a", 2*NonceByteLength),
			requestBody:    "",
			expectedStatus: http.StatusOK,
			description:    "Token access request with valid token",
		},
		{
			name:           "TokenAccessRequestInvalidLength",
			requestURL:     "/authenticate?token=short",
			requestBody:    "",
			expectedStatus: http.StatusUnauthorized,
			description:    "Token access request with token of invalid length",
		},
		{
			name:           "InvalidQueryParam",
			requestURL:     "/authenticate?invalid=param",
			requestBody:    "",
			expectedStatus: http.StatusUnauthorized,
			description:    "Request with invalid query parameter",
		},
		{
			name:           "BothWebAccessAndTokenParams",
			requestURL:     "/authenticate?webaccess=true&token=" + strings.Repeat("a", 2*NonceByteLength),
			requestBody:    "",
			expectedStatus: http.StatusUnauthorized,
			description:    "Request with both webaccess and token parameters",
		},
		{
			name:           "MultipleQueryParams",
			requestURL:     "/authenticate?param1=value1&param2=value2",
			requestBody:    "",
			expectedStatus: http.StatusUnauthorized,
			description:    "Request with multiple query parameters",
		},
	}

	// Execute test cases.
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup mock handlers.
			setupMockHandlers()
			defer teardownMockHandlers()

			// Create request.
			var req *http.Request
			if tt.requestBody != "" {
				// Create request with body.
				body := bytes.NewBufferString(tt.requestBody)
				req, _ = http.NewRequest("POST", tt.requestURL, body)
			} else {
				// Create request without body.
				req, _ = http.NewRequest("POST", tt.requestURL, nil)
			}

			// Setup testing context.
			req = setupTestContext(req)

			// Create response recorder.
			rr := httptest.NewRecorder()

			// Call the Handler function.
			Handler(rr, req)

			// Check the status code.
			assert.Equal(t, tt.expectedStatus, rr.Code, "Status code should be %d for %s", tt.expectedStatus, tt.description)
		})
	}
}

// TestHandlerFunctionVariables tests that the handler function variables
// are properly initialized and return the expected types.
func TestHandlerFunctionVariables(t *testing.T) {
	// Define test cases.
	tests := []struct {
		name        string
		setupFn     func()
		testFn      func(t *testing.T)
		description string
	}{
		{
			name: "DefaultHandlers",
			setupFn: func() {
				// No setup needed for default handlers.
			},
			testFn: func(t *testing.T) {
				// Test that the default functions return non-nil values.
				userHandler := newUserHandlerFunc()
				assert.NotNil(t, userHandler, "newUserHandlerFunc should return non-nil value")

				tokenHandler := newTokenHandlerFunc()
				assert.NotNil(t, tokenHandler, "newTokenHandlerFunc should return non-nil value")
			},
			description: "Default handler functions should return non-nil values",
		},
		{
			name: "MockHandlers",
			setupFn: func() {
				// Setup mock handlers.
				setupMockHandlers()
			},
			testFn: func(t *testing.T) {
				// Get the mock handlers.
				mockUserHandler := newUserHandlerFunc()
				mockTokenHandler := newTokenHandlerFunc()

				// Create a mock response writer and request.
				rr := httptest.NewRecorder()
				req, _ := http.NewRequest("POST", "/authenticate", nil)

				// Call UserAccess on the mock user handler.
				mockUserHandler.UserAccess("testuser", "testpass", rr, req)
				assert.Equal(t, http.StatusOK, rr.Code, "Mock UserAccess should return 200 OK")

				// Reset the response recorder.
				rr = httptest.NewRecorder()
				mockUserHandler.WebAccess("testuser", "testpass", rr, req)
				assert.Equal(t, http.StatusOK, rr.Code, "Mock WebAccess should return 200 OK")

				// Reset the response recorder.
				rr = httptest.NewRecorder()
				mockTokenHandler.TokenAccess("testtoken", rr, req)
				assert.Equal(t, http.StatusOK, rr.Code, "Mock TokenAccess should return 200 OK")
			},
			description: "Mock handler functions should behave as expected",
		},
	}

	// Save original functions to restore after test.
	origUserHandlerFunc := newUserHandlerFunc
	origTokenHandlerFunc := newTokenHandlerFunc
	defer func() {
		newUserHandlerFunc = origUserHandlerFunc
		newTokenHandlerFunc = origTokenHandlerFunc
	}()

	// Execute test cases.
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup test.
			tt.setupFn()

			// Execute test.
			tt.testFn(t)
		})
	}
}

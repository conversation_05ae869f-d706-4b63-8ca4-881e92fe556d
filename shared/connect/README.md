# connect

---

## Overview

The `connect` package centralizes context-based storage and retrieval of service connections, along with uniform executor interfaces for Redis, Pub/Sub, BigQuery, and PostgreSQL. It bootstraps and manages client initialization with retry logic and provides utility functions for release identification, SQL handling, and map lookups.

---

## Package Layout

```
shared/
├── connect/
│   ├── bigquery.go
│   ├── connect.go
│   ├── getReleaseIdentifier.go
│   ├── postgres.go
│   ├── pubsub.go
│   └── redis.go
```

---

## Features

- **Context Propagation**: `WithConnections` / `GetConnections`  
- **Lazy Initialized Clients**: Redis -> Pub/Sub -> BigQuery -> PostgreSQL with retries  
- **Uniform Executor Interfaces**: `DatabaseExecutor`, `PsClient`, `PsTopic`, `PsSubscription`, `PsPublishResult`  
- **Utilities**:  
  - `GetReleaseIdentifier()` for consistent version tagging  
  - `SplitSQLStatements()` for multi-statement SQL  
  - `GetColumn[T]()` for type-safe map extraction  

---

## Usage

```go
ctx := context.Background()
conns := connect.NewConnections(ctx)
defer conns.Close()

// Store in context
ctx = connect.WithConnections(ctx, conns)

// Retrieve connections
conns, err := connect.GetConnections(ctx)
if err != nil {
    // handle error
}

// Use PostgreSQL executor
rows, err := conns.Postgres.QueryGeneric("SELECT * FROM {{users}} WHERE id=$1", 42)
// Use Pub/Sub client
topic := conns.Pubsub.Topic("my-topic")
res := topic.Publish(ctx, &pubsub.Message{Data: []byte("hello")})
id, err := res.Get(ctx)
```

---

## Environment Variables

| Name                      | Description                                      |
|---------------------------|--------------------------------------------------|
| `MEMORYSTORE_HOST`        | Redis host (default `redis`)                     |
| `MEMORYSTORE_PORT`        | Redis port (default `6379`)                      |
| `GCP_PROJECT_ID`          | Pub/Sub GCP project (default `test-project`)     |
| `PUBSUB_EMULATOR_HOST`    | Pub/Sub emulator endpoint (optional)             |
| `BIGQUERY_EMULATOR_HOST`  | BigQuery emulator endpoint (optional)            |
| `BIGQUERY_DATASET`        | BigQuery dataset name                            |
| `BIGQUERY_NAMESPACE`      | BigQuery table namespace prefix                  |
| `POSTGRES_HOST`           | PostgreSQL host                                  |
| `POSTGRES_PORT`           | PostgreSQL port                                  |
| `POSTGRES_DB`             | PostgreSQL database name                         |
| `POSTGRES_NAMESPACE`      | PostgreSQL table namespace prefix                |
| `POSTGRES_USER`           | PostgreSQL username                              |
| `POSTGRES_PASSWORD`       | PostgreSQL password                              |
| `ENVIRONMENT`             | Application environment (e.g. `dev`, `prod`)     |
| `CURRENT_TIMESTAMP`       | Fallback release identifier if no file present   |

---

## API Reference

### Types

```go
type Connections struct {
    Redis    *redis.Client
    Postgres DatabaseExecutor
    Bigquery DatabaseExecutor
    Pubsub   PsClient
}
```

```go
type DatabaseExecutor interface {
    Exec(query string, args ...interface{}) (sql.Result, error)
    ExecMultiple(queries string) error
    QueryGeneric(query string, args ...interface{}) ([]map[string]interface{}, error)
    QueryGenericSlice(dest interface{}, query string, args ...interface{}) error
    QueryRow(query string, args ...interface{}) (map[string]interface{}, error)
    QueryRowStruct(dest interface{}, query string, args ...interface{}) error
    EscapeIdentifier(identifier string) string
    ReplaceNamespace(query string) string
    Close() error
}
```

```go
type PsClient interface { /* Pub/Sub client abstraction */ }
type PsTopic interface { /* topic abstraction */ }
type PsSubscription interface { /* subscription abstraction */ }
type PsPublishResult interface { /* publish result abstraction */ }
```

### Functions

```go
func NewConnections(ctx context.Context) *Connections
func WithConnections(ctx context.Context, conns *Connections) context.Context
func GetConnections(ctx context.Context, check ...bool) (*Connections, error)
func GetReleaseIdentifier() (string, error)
func SplitSQLStatements(sqlStr string) []string
func GetColumn[T any](m map[string]interface{}, key string) (T, error)
```

---

## Internals

- **Initialization Order**: `GetReleaseIdentifier` -> Redis -> Pub/Sub -> BigQuery -> PostgreSQL  
- **Retry Logic**: exponential backoff for client connections  
- **Namespace & Placeholder Handling**: `ReplaceNamespace`, SQL placeholder translation for BigQuery  
- **Reflection-based Scanning**: safe struct mapping with support for unexported fields  

---

## Example

```bash
go run cmd/service/main.go
```

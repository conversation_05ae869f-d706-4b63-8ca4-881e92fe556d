package organization

import (
	"bytes"
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gorilla/mux"
	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/mocks/dbexecutor"
)

// Test helper to create a mock request with JSON body
func createMockRequest(method, url string, body interface{}) *http.Request {
	var reqBody *bytes.Buffer
	if body != nil {
		jsonBody, _ := json.Marshal(body)
		reqBody = bytes.NewBuffer(jsonBody)
	} else {
		reqBody = bytes.NewBuffer([]byte{})
	}

	req := httptest.NewRequest(method, url, reqBody)
	req.Header.Set("Content-Type", "application/json")
	return req
}

// Test validateIdentifier function
func Test_validateIdentifier(t *testing.T) {
	t.<PERSON>()

	tests := []struct {
		name        string
		identifier  string
		expectedErr error
		wantErr     bool
	}{
		{
			name:        "valid identifier",
			identifier:  "valid-org-123",
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:        "empty identifier",
			identifier:  "",
			expectedErr: ErrInvalidIdentifier,
			wantErr:     true,
		},
		{
			name:        "whitespace only identifier",
			identifier:  "   ",
			expectedErr: ErrInvalidIdentifier,
			wantErr:     true,
		},
		{
			name:        "identifier with spaces",
			identifier:  "  valid-org-123  ",
			expectedErr: nil,
			wantErr:     false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Execute the function under test
			err := validateIdentifier(tt.identifier)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedErr, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// Test parseCreatAndUpdateRequest function
func Test_parseCreatAndUpdateRequest(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		requestBody interface{}
		expectedErr error
		wantErr     bool
		description string
	}{
		{
			name: "valid request",
			requestBody: CreateAnhUpdateOrganizationRequest{
				Description: "Valid organization description",
			},
			expectedErr: nil,
			wantErr:     false,
			description: "Valid organization description",
		},
		{
			name: "empty description",
			requestBody: CreateAnhUpdateOrganizationRequest{
				Description: "",
			},
			expectedErr: ErrInvalidDescription,
			wantErr:     true,
			description: "",
		},
		{
			name: "whitespace only description",
			requestBody: CreateAnhUpdateOrganizationRequest{
				Description: "   ",
			},
			expectedErr: ErrInvalidDescription,
			wantErr:     true,
			description: "   ",
		},
		{
			name: "description with leading/trailing spaces",
			requestBody: CreateAnhUpdateOrganizationRequest{
				Description: "  Valid description  ",
			},
			expectedErr: nil,
			wantErr:     false,
			description: "  Valid description  ",
		},
		{
			name:        "invalid JSON",
			requestBody: "invalid json",
			expectedErr: ErrInvalidRequestBody,
			wantErr:     true,
			description: "",
		},
		{
			name: "unexpected fields",
			requestBody: map[string]interface{}{
				"description": "Valid description",
				"unexpected":  "field",
			},
			expectedErr: ErrUnexpectedFields,
			wantErr:     true,
			description: "Valid description",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create mock request with test body
			req := createMockRequest("POST", "/organizations", tt.requestBody)

			// Execute the function under test
			result, err := parseCreatAndUpdateRequest(req)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.Equal(t, tt.expectedErr, err)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, tt.description, result.Description)
			}
		})
	}
}

// Test Organization.ToResponse method
func Test_Organization_ToResponse(t *testing.T) {
	t.Parallel()

	// Create test organization
	now := time.Now().UTC()
	org := Organization{
		Id:                     1,
		OrganizationIdentifier: "test-org-123",
		APIKey:                 "api-key-456",
		Description:            "Test Organization",
		CreatedAt:              now,
		UpdatedAt:              now.Add(time.Hour),
		DeletedAt:              nil,
		IsDeleted:              false,
	}

	// Execute the method under test
	response := org.ToResponse()

	// Assert all fields are correctly mapped
	assert.Equal(t, org.OrganizationIdentifier, response.OrganizationIdentifier)
	assert.Equal(t, org.APIKey, response.APIKey)
	assert.Equal(t, org.Description, response.Description)
	assert.Equal(t, org.CreatedAt, response.CreatedAt)
	assert.Equal(t, org.UpdatedAt, response.UpdatedAt)
}

// Test createOrganization function
func Test_createOrganization(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		request     *CreateAnhUpdateOrganizationRequest
		setupMock   func(*dbexecutor.FakeDBExecutor)
		expectedErr error
		wantErr     bool
	}{
		{
			name: "successful creation",
			request: &CreateAnhUpdateOrganizationRequest{
				Description: "Test Organization",
			},
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock successful QueryRowStruct call
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					// Simulate successful organization creation
					org := dest.(*Organization)
					org.Id = 1
					org.OrganizationIdentifier = "test-org-123"
					org.Description = "Test Organization"
					org.APIKey = "api-key-456"
					org.CreatedAt = time.Now().UTC()
					org.UpdatedAt = time.Now().UTC()
					org.IsDeleted = false
					return nil
				}
			},
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name: "database error",
			request: &CreateAnhUpdateOrganizationRequest{
				Description: "Test Organization",
			},
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock database error
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					return errors.New("database connection failed")
				}
			},
			expectedErr: ErrDatabaseOperation,
			wantErr:     true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create mock database executor
			mockDB := &dbexecutor.FakeDBExecutor{}
			tt.setupMock(mockDB)

			// Execute the function under test
			result, err := createOrganization(mockDB, tt.request)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.ErrorIs(t, err, tt.expectedErr)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, tt.request.Description, result.Description)
				assert.NotEmpty(t, result.OrganizationIdentifier)
				assert.NotEmpty(t, result.APIKey)
			}
		})
	}
}

// Test getAllOrganizations function
func Test_getAllOrganizations(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		setupMock   func(*dbexecutor.FakeDBExecutor)
		expectedErr error
		wantErr     bool
		expectedLen int
	}{
		{
			name: "successful retrieval with organizations",
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock successful QueryGenericSlice call
				mockDB.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
					// Simulate successful organizations retrieval
					orgs := dest.(*[]Organization)
					*orgs = []Organization{
						{
							Id:                     1,
							OrganizationIdentifier: "org-1",
							Description:            "Organization 1",
							APIKey:                 "key-1",
							CreatedAt:              time.Now().UTC(),
							UpdatedAt:              time.Now().UTC(),
							IsDeleted:              false,
						},
						{
							Id:                     2,
							OrganizationIdentifier: "org-2",
							Description:            "Organization 2",
							APIKey:                 "key-2",
							CreatedAt:              time.Now().UTC(),
							UpdatedAt:              time.Now().UTC(),
							IsDeleted:              false,
						},
					}
					return nil
				}
			},
			expectedErr: nil,
			wantErr:     false,
			expectedLen: 2,
		},
		{
			name: "successful retrieval with no organizations",
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock successful QueryGenericSlice call with empty result
				mockDB.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
					// Simulate empty organizations list
					orgs := dest.(*[]Organization)
					*orgs = []Organization{}
					return nil
				}
			},
			expectedErr: nil,
			wantErr:     false,
			expectedLen: 0,
		},
		{
			name: "database error",
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock database error
				mockDB.QueryGenericSliceFunc = func(dest interface{}, query string, args ...interface{}) error {
					return errors.New("database connection failed")
				}
			},
			expectedErr: ErrDatabaseOperation,
			wantErr:     true,
			expectedLen: 0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create mock database executor
			mockDB := &dbexecutor.FakeDBExecutor{}
			tt.setupMock(mockDB)

			// Execute the function under test
			result, err := getAllOrganizations(mockDB)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.ErrorIs(t, err, tt.expectedErr)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Len(t, *result, tt.expectedLen)
			}
		})
	}
}

// Test getOrganizationByIdentifier function
func Test_getOrganizationByIdentifier(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		identifier  string
		setupMock   func(*dbexecutor.FakeDBExecutor)
		expectedErr error
		wantErr     bool
	}{
		{
			name:       "successful retrieval",
			identifier: "test-org-123",
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock successful QueryRowStruct call
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					// Simulate successful organization retrieval
					org := dest.(*Organization)
					org.Id = 1
					org.OrganizationIdentifier = "test-org-123"
					org.Description = "Test Organization"
					org.APIKey = "api-key-456"
					org.CreatedAt = time.Now().UTC()
					org.UpdatedAt = time.Now().UTC()
					org.IsDeleted = false
					return nil
				}
			},
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:       "organization not found",
			identifier: "non-existent-org",
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock sql.ErrNoRows
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					return sql.ErrNoRows
				}
			},
			expectedErr: ErrOrganizationNotFound,
			wantErr:     true,
		},
		{
			name:       "database error",
			identifier: "test-org-123",
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock database error
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					return errors.New("database connection failed")
				}
			},
			expectedErr: ErrDatabaseOperation,
			wantErr:     true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create mock database executor
			mockDB := &dbexecutor.FakeDBExecutor{}
			tt.setupMock(mockDB)

			// Execute the function under test
			result, err := getOrganizationByIdentifier(mockDB, tt.identifier)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.ErrorIs(t, err, tt.expectedErr)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, tt.identifier, result.OrganizationIdentifier)
			}
		})
	}
}

// Test updateOrganization function
func Test_updateOrganization(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		identifier  string
		request     *CreateAnhUpdateOrganizationRequest
		setupMock   func(*dbexecutor.FakeDBExecutor)
		expectedErr error
		wantErr     bool
	}{
		{
			name:       "successful update",
			identifier: "test-org-123",
			request: &CreateAnhUpdateOrganizationRequest{
				Description: "Updated Organization",
			},
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock successful QueryRowStruct call
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					// Simulate successful organization update
					org := dest.(*Organization)
					org.Id = 1
					org.OrganizationIdentifier = "test-org-123"
					org.Description = "Updated Organization"
					org.APIKey = "api-key-456"
					org.CreatedAt = time.Now().UTC().Add(-time.Hour)
					org.UpdatedAt = time.Now().UTC()
					org.IsDeleted = false
					return nil
				}
			},
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:       "organization not found",
			identifier: "non-existent-org",
			request: &CreateAnhUpdateOrganizationRequest{
				Description: "Updated Organization",
			},
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock sql.ErrNoRows
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					return sql.ErrNoRows
				}
			},
			expectedErr: ErrOrganizationNotFound,
			wantErr:     true,
		},
		{
			name:       "database error",
			identifier: "test-org-123",
			request: &CreateAnhUpdateOrganizationRequest{
				Description: "Updated Organization",
			},
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock database error
				mockDB.QueryRowStructFunc = func(dest interface{}, query string, args ...interface{}) error {
					return errors.New("database connection failed")
				}
			},
			expectedErr: ErrDatabaseOperation,
			wantErr:     true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create mock database executor
			mockDB := &dbexecutor.FakeDBExecutor{}
			tt.setupMock(mockDB)

			// Execute the function under test
			result, err := updateOrganization(mockDB, tt.identifier, tt.request)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.ErrorIs(t, err, tt.expectedErr)
				assert.Nil(t, result)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, result)
				assert.Equal(t, tt.identifier, result.OrganizationIdentifier)
				assert.Equal(t, tt.request.Description, result.Description)
			}
		})
	}
}

// Mock sql.Result for testing
type mockSQLResult struct {
	rowsAffected int64
	lastInsertId int64
	err          error
}

func (m *mockSQLResult) LastInsertId() (int64, error) {
	return m.lastInsertId, m.err
}

func (m *mockSQLResult) RowsAffected() (int64, error) {
	return m.rowsAffected, m.err
}

// Test deleteOrganization function
func Test_deleteOrganization(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name        string
		identifier  string
		setupMock   func(*dbexecutor.FakeDBExecutor)
		expectedErr error
		wantErr     bool
	}{
		{
			name:       "successful deletion",
			identifier: "test-org-123",
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock successful Exec call with 1 row affected
				mockDB.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return &mockSQLResult{rowsAffected: 1}, nil
				}
			},
			expectedErr: nil,
			wantErr:     false,
		},
		{
			name:       "organization not found (0 rows affected)",
			identifier: "non-existent-org",
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock successful Exec call with 0 rows affected
				mockDB.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return &mockSQLResult{rowsAffected: 0}, nil
				}
			},
			expectedErr: ErrOrganizationNotFound,
			wantErr:     true,
		},
		{
			name:       "database error on exec",
			identifier: "test-org-123",
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock database error on Exec
				mockDB.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return nil, errors.New("database connection failed")
				}
			},
			expectedErr: ErrDatabaseOperation,
			wantErr:     true,
		},
		{
			name:       "error getting rows affected",
			identifier: "test-org-123",
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock error when getting rows affected
				mockDB.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return &mockSQLResult{rowsAffected: 1, err: errors.New("rows affected error")}, nil
				}
			},
			expectedErr: ErrDatabaseOperation,
			wantErr:     true,
		},
		{
			name:       "nil result from exec",
			identifier: "test-org-123",
			setupMock: func(mockDB *dbexecutor.FakeDBExecutor) {
				// Mock nil result (edge case)
				mockDB.ExecFunc = func(query string, args ...interface{}) (sql.Result, error) {
					return nil, nil
				}
			},
			expectedErr: nil,
			wantErr:     false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create mock database executor
			mockDB := &dbexecutor.FakeDBExecutor{}
			tt.setupMock(mockDB)

			// Execute the function under test
			err := deleteOrganization(mockDB, tt.identifier)

			// Assert results
			if tt.wantErr {
				assert.Error(t, err)
				assert.ErrorIs(t, err, tt.expectedErr)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

// Test CreateHandlerWithDeps function
func Test_CreateHandlerWithDeps(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		requestBody    interface{}
		setupDeps      func() HandlerDeps
		expectedStatus int
		wantErr        bool
	}{
		{
			name: "successful_creation",
			requestBody: CreateAnhUpdateOrganizationRequest{
				Description: "Test Organization",
			},
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						// Return mock connections
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
						}, nil
					},
					CreateOrganization: func(pg connect.DatabaseExecutor, req *CreateAnhUpdateOrganizationRequest) (*Organization, error) {
						// Return mock organization
						return &Organization{
							Id:                     1,
							OrganizationIdentifier: "test-org-123",
							Description:            req.Description,
							APIKey:                 "api-key-456",
							CreatedAt:              time.Now().UTC(),
							UpdatedAt:              time.Now().UTC(),
							IsDeleted:              false,
						}, nil
					},
				}
			},
			expectedStatus: http.StatusOK,
			wantErr:        false,
		},
		{
			name: "connection_error",
			requestBody: CreateAnhUpdateOrganizationRequest{
				Description: "Test Organization",
			},
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return nil, errors.New("connection failed")
					},
				}
			},
			expectedStatus: http.StatusInternalServerError,
			wantErr:        true,
		},
		{
			name:        "invalid_request_body",
			requestBody: "invalid json",
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
						}, nil
					},
				}
			},
			expectedStatus: http.StatusBadRequest,
			wantErr:        true,
		},
		{
			name: "create_organization_error",
			requestBody: CreateAnhUpdateOrganizationRequest{
				Description: "Test Organization",
			},
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
						}, nil
					},
					CreateOrganization: func(pg connect.DatabaseExecutor, req *CreateAnhUpdateOrganizationRequest) (*Organization, error) {
						return nil, errors.New("database error")
					},
				}
			},
			expectedStatus: http.StatusInternalServerError,
			wantErr:        true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create handler with test dependencies
			handler := CreateHandlerWithDeps(tt.setupDeps())

			// Create mock request
			req := createMockRequest("POST", "/organizations", tt.requestBody)
			req = req.WithContext(context.Background())

			// Create response recorder
			rr := httptest.NewRecorder()

			// Execute the handler
			handler.ServeHTTP(rr, req)

			// Assert status code
			assert.Equal(t, tt.expectedStatus, rr.Code)
		})
	}
}

// Test GetAllHandlerWithDeps function
func Test_GetAllHandlerWithDeps(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		setupDeps      func() HandlerDeps
		expectedStatus int
		wantErr        bool
	}{
		{
			name: "successful_retrieval",
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
						}, nil
					},
					GetAllOrganizations: func(pg connect.DatabaseExecutor) (*[]Organization, error) {
						// Return mock organizations
						orgs := []Organization{
							{
								Id:                     1,
								OrganizationIdentifier: "org-1",
								Description:            "Organization 1",
								APIKey:                 "key-1",
								CreatedAt:              time.Now().UTC(),
								UpdatedAt:              time.Now().UTC(),
								IsDeleted:              false,
							},
							{
								Id:                     2,
								OrganizationIdentifier: "org-2",
								Description:            "Organization 2",
								APIKey:                 "key-2",
								CreatedAt:              time.Now().UTC(),
								UpdatedAt:              time.Now().UTC(),
								IsDeleted:              false,
							},
						}
						return &orgs, nil
					},
				}
			},
			expectedStatus: http.StatusOK,
			wantErr:        false,
		},
		{
			name: "connection_error",
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return nil, errors.New("connection failed")
					},
				}
			},
			expectedStatus: http.StatusInternalServerError,
			wantErr:        true,
		},
		{
			name: "get_organizations_error",
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
						}, nil
					},
					GetAllOrganizations: func(pg connect.DatabaseExecutor) (*[]Organization, error) {
						return nil, errors.New("database error")
					},
				}
			},
			expectedStatus: http.StatusInternalServerError,
			wantErr:        true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create handler with test dependencies
			handler := GetAllHandlerWithDeps(tt.setupDeps())

			// Create mock request
			req := httptest.NewRequest("GET", "/organizations", nil)
			req = req.WithContext(context.Background())

			// Create response recorder
			rr := httptest.NewRecorder()

			// Execute the handler
			handler.ServeHTTP(rr, req)

			// Assert status code
			assert.Equal(t, tt.expectedStatus, rr.Code)
		})
	}
}

// Test GetByIdentifierHandlerWithDeps function
func Test_GetByIdentifierHandlerWithDeps(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		identifier     string
		setupDeps      func() HandlerDeps
		expectedStatus int
		wantErr        bool
	}{
		{
			name:       "successful_retrieval",
			identifier: "test-org-123",
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
						}, nil
					},
					GetOrganization: func(pg connect.DatabaseExecutor, identifier string) (*Organization, error) {
						// Return mock organization
						return &Organization{
							Id:                     1,
							OrganizationIdentifier: identifier,
							Description:            "Test Organization",
							APIKey:                 "api-key-456",
							CreatedAt:              time.Now().UTC(),
							UpdatedAt:              time.Now().UTC(),
							IsDeleted:              false,
						}, nil
					},
				}
			},
			expectedStatus: http.StatusOK,
			wantErr:        false,
		},
		{
			name:       "connection_error",
			identifier: "test-org-123",
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return nil, errors.New("connection failed")
					},
				}
			},
			expectedStatus: http.StatusInternalServerError,
			wantErr:        true,
		},
		{
			name:       "invalid_identifier",
			identifier: "",
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
						}, nil
					},
				}
			},
			expectedStatus: http.StatusBadRequest,
			wantErr:        true,
		},
		{
			name:       "get_organization_error",
			identifier: "test-org-123",
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
						}, nil
					},
					GetOrganization: func(pg connect.DatabaseExecutor, identifier string) (*Organization, error) {
						return nil, errors.New("database error")
					},
				}
			},
			expectedStatus: http.StatusInternalServerError,
			wantErr:        true,
		},
		{
			name:       "get_organization_not_found",
			identifier: "test-org-123",
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
						}, nil
					},
					GetOrganization: func(pg connect.DatabaseExecutor, identifier string) (*Organization, error) {
						return nil, ErrOrganizationNotFound
					},
				}
			},
			expectedStatus: http.StatusNotFound,
			wantErr:        true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create handler with test dependencies
			handler := GetByIdentifierHandlerWithDeps(tt.setupDeps())

			// Create mock request with URL vars
			req := httptest.NewRequest("GET", "/organizations/"+tt.identifier, nil)
			req = req.WithContext(context.Background())
			req = mux.SetURLVars(req, map[string]string{"identifier": tt.identifier})

			// Create response recorder
			rr := httptest.NewRecorder()

			// Execute the handler
			handler.ServeHTTP(rr, req)

			// Assert status code
			assert.Equal(t, tt.expectedStatus, rr.Code)
		})
	}
}

// Test UpdateHandlerWithDeps function
func Test_UpdateHandlerWithDeps(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		identifier     string
		requestBody    interface{}
		setupDeps      func() HandlerDeps
		expectedStatus int
		wantErr        bool
	}{
		{
			name:       "successful_update",
			identifier: "test-org-123",
			requestBody: CreateAnhUpdateOrganizationRequest{
				Description: "Updated Organization",
			},
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
						}, nil
					},
					UpdateOrganization: func(pg connect.DatabaseExecutor, identifier string, req *CreateAnhUpdateOrganizationRequest) (*Organization, error) {
						// Return mock updated organization
						return &Organization{
							Id:                     1,
							OrganizationIdentifier: identifier,
							Description:            req.Description,
							APIKey:                 "api-key-456",
							CreatedAt:              time.Now().UTC().Add(-time.Hour),
							UpdatedAt:              time.Now().UTC(),
							IsDeleted:              false,
						}, nil
					},
				}
			},
			expectedStatus: http.StatusOK,
			wantErr:        false,
		},
		{
			name:       "connection_error",
			identifier: "test-org-123",
			requestBody: CreateAnhUpdateOrganizationRequest{
				Description: "Updated Organization",
			},
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return nil, errors.New("connection failed")
					},
				}
			},
			expectedStatus: http.StatusInternalServerError,
			wantErr:        true,
		},
		{
			name:       "invalid_identifier",
			identifier: "",
			requestBody: CreateAnhUpdateOrganizationRequest{
				Description: "Updated Organization",
			},
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
						}, nil
					},
				}
			},
			expectedStatus: http.StatusBadRequest,
			wantErr:        true,
		},
		{
			name:        "invalid_request_body",
			identifier:  "test-org-123",
			requestBody: "invalid json",
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
						}, nil
					},
				}
			},
			expectedStatus: http.StatusBadRequest,
			wantErr:        true,
		},
		{
			name:       "update_organization_error",
			identifier: "test-org-123",
			requestBody: CreateAnhUpdateOrganizationRequest{
				Description: "Updated Organization",
			},
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
						}, nil
					},
					UpdateOrganization: func(pg connect.DatabaseExecutor, identifier string, req *CreateAnhUpdateOrganizationRequest) (*Organization, error) {
						return nil, errors.New("database error")
					},
				}
			},
			expectedStatus: http.StatusInternalServerError,
			wantErr:        true,
		},
		{
			name:       "update_organization_error_no_row_updated",
			identifier: "test-org-123",
			requestBody: CreateAnhUpdateOrganizationRequest{
				Description: "Updated Organization",
			},
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
						}, nil
					},
					UpdateOrganization: func(pg connect.DatabaseExecutor, identifier string, req *CreateAnhUpdateOrganizationRequest) (*Organization, error) {
						return nil, ErrOrganizationNotFound
					},
				}
			},
			expectedStatus: http.StatusNotFound,
			wantErr:        true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create handler with test dependencies
			handler := UpdateHandlerWithDeps(tt.setupDeps())

			// Create mock request with URL vars
			req := createMockRequest("PUT", "/organizations/"+tt.identifier, tt.requestBody)
			req = req.WithContext(context.Background())
			req = mux.SetURLVars(req, map[string]string{"identifier": tt.identifier})

			// Create response recorder
			rr := httptest.NewRecorder()

			// Execute the handler
			handler.ServeHTTP(rr, req)

			// Assert status code
			assert.Equal(t, tt.expectedStatus, rr.Code)
		})
	}
}

// Test DeleteHandlerWithDeps function
func Test_DeleteHandlerWithDeps(t *testing.T) {
	t.Parallel()

	tests := []struct {
		name           string
		identifier     string
		setupDeps      func() HandlerDeps
		expectedStatus int
		wantErr        bool
	}{
		{
			name:       "successful_deletion",
			identifier: "test-org-123",
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
						}, nil
					},
					DeleteOrganization: func(pg connect.DatabaseExecutor, identifier string) error {
						// Return success
						return nil
					},
				}
			},
			expectedStatus: http.StatusOK,
			wantErr:        false,
		},
		{
			name:       "connection_error",
			identifier: "test-org-123",
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return nil, errors.New("connection failed")
					},
				}
			},
			expectedStatus: http.StatusInternalServerError,
			wantErr:        true,
		},
		{
			name:       "invalid_identifier",
			identifier: "",
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
						}, nil
					},
				}
			},
			expectedStatus: http.StatusBadRequest,
			wantErr:        true,
		},
		{
			name:       "delete_organization_error",
			identifier: "test-org-123",
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
						}, nil
					},
					DeleteOrganization: func(pg connect.DatabaseExecutor, identifier string) error {
						return errors.New("database error")
					},
				}
			},
			expectedStatus: http.StatusInternalServerError,
			wantErr:        true,
		},
		{
			name:       "delete_organization_error_no_row",
			identifier: "test-org-123",
			setupDeps: func() HandlerDeps {
				return HandlerDeps{
					GetConnections: func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
						return &connect.Connections{
							Postgres: &dbexecutor.FakeDBExecutor{},
						}, nil
					},
					DeleteOrganization: func(pg connect.DatabaseExecutor, identifier string) error {
						return ErrOrganizationNotFound
					},
				}
			},
			expectedStatus: http.StatusNotFound,
			wantErr:        true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			// Create handler with test dependencies
			handler := DeleteHandlerWithDeps(tt.setupDeps())

			// Create mock request with URL vars
			req := httptest.NewRequest("DELETE", "/organizations/"+tt.identifier, nil)
			req = req.WithContext(context.Background())
			req = mux.SetURLVars(req, map[string]string{"identifier": tt.identifier})

			// Create response recorder
			rr := httptest.NewRecorder()

			// Execute the handler
			handler.ServeHTTP(rr, req)

			// Assert status code
			assert.Equal(t, tt.expectedStatus, rr.Code)
		})
	}
}

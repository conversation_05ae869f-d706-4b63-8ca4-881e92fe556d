package main

import (
	"context"
	"net/http"
	"os"

	"synapse-its.com/coordinator/internal/setup"
	"synapse-its.com/shared/connect"
	"synapse-its.com/shared/healthz"
	"synapse-its.com/shared/logger"
)

func main() {
	ctx := context.Background()

	// Start healthz service
	healthzsrv := healthz.NewServer(os.Getenv("HEALTH_PORT"))
	if err := healthzsrv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
		logger.Fatal(err)
	}
	defer healthzsrv.Shutdown(ctx)

	logger.Info("Starting coordinator...")
	healthzsrv.SetBootComplete()

	// Get the release identifier string.
	logger.Debug("Getting release identifier...")
	releaseIdentifier, err := connect.GetReleaseIdentifier()
	if err != nil {
		logger.Fatal(err)
	}
	logger.Infof("Release Identifier: %s", releaseIdentifier)

	// Get the Redis client.
	logger.Debug("Connecting to Redis...")
	redisClient, err := connect.Redis(ctx)
	if err != nil {
		logger.Fatal(err)
	}
	defer redisClient.Close()

	// Get and setup the PubSub client.
	logger.Debug("Connecting to Pubsub..")
	pubsubClient, err := connect.PubSub(ctx)
	if err != nil {
		logger.Fatal(err)
	}
	defer pubsubClient.Close()

	// Get and setup Bigquery.
	logger.Debug("Connecting to BigQuery...")
	bq, err := connect.BigQuery(ctx, nil, nil)
	if err != nil {
		logger.Fatal(err)
	}
	defer bq.Client.Close()

	// Get and setup Postgres.
	logger.Debug("Connecting to Postgres...")
	pg, err := connect.Postgres(ctx, nil)
	if err != nil {
		logger.Fatal(err)
	}
	defer pg.DB.Close()

	// All connections made
	healthzsrv.SetReady()

	// Setup all services
	setup.PubSub(ctx, pubsubClient)
	connect.RedisSet(ctx, redisClient, "ReleaseIdentifier:PubSub", releaseIdentifier)

	if err = setup.BigQuery(bq, ""); err != nil {
		logger.Fatal(err)
	}
	connect.RedisSet(ctx, redisClient, "ReleaseIdentifier:BigQuery", releaseIdentifier)

	if err = setup.Postgres(pg, ""); err != nil {
		logger.Fatal(err)
	}
	connect.RedisSet(ctx, redisClient, "ReleaseIdentifier:Postgres", releaseIdentifier)

	// Finished all setups
	logger.Info("Coordinator setup complete. Shutting down.")
}

const { setWorldConstructor } = require('@cucumber/cucumber');
const { until, By } = require('selenium-webdriver');

class CustomWorld {
  constructor() { }

  /**
   * Waits for the browser to redirect back to BASE_URL
   * and for Angular to render its <nav> inside <app-root>.
   */
  async waitForAppReady(timeout = 15000) {
    // 1) URL
    await this.driver.wait(
      async () => (await this.driver.getCurrentUrl()).startsWith(process.env.BASE_URL),
      timeout,
      `Expected URL to start with ${process.env.BASE_URL}`
    );

    // 2) DOM
    await this.driver.wait(
      until.elementLocated(By.css('app-root nav')),
      timeout,
      'Timed out waiting for <nav> inside <app-root>'
    );
  }

  /**
   * Wait until Angular reports itself as stable.
   */
  async waitForAngularStable(timeout = 15000) {
    await this.driver.wait(
      async () => {
        return this.driver.executeScript(() => {
          // In-browser function: return false until Angular testability is ready
          if (
            window.getAllAngularTestabilities &&
            Array.isArray(window.getAllAngularTestabilities())
          ) {
            return window
              .getAllAngularTestabilities()
              .every(testability => testability.isStable());
          }
          return false;
        });
      },
      timeout,
      'Timed out waiting for Angular to become stable'
    );
  }
}

setWorldConstructor(CustomWorld);

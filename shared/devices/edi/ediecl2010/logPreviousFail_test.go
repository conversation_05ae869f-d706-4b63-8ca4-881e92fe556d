package ediecl2010

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"synapse-its.com/shared/devices/edi/helper"
	"synapse-its.com/shared/pubsubdata"
)

// buildValidLogPreviousFailMsg creates a valid log previous fail message with the specified number of records
func buildValidLogPreviousFailMsg(numRecords int) []byte {
	msg := make([]byte, HeaderLength+PreviousFailRecordsCountLength+numRecords*PF2018LogLength+ChecksumLength)

	// Set header bytes
	msg[0] = 0x01             // Message type identifier
	msg[1] = 0x01             // Device address
	msg[2] = 0x01             // Command code
	msg[3] = 0x00             // Response status
	msg[4] = 0x00             // Reserved
	msg[5] = 0x00             // Reserved
	msg[6] = 0x00             // Reserved
	msg[7] = byte(numRecords) // Number of records

	// Set records
	for i := range numRecords {
		offset := PreviousFailRecordsStartOffset + i*PF2018LogLength

		// Set fault status byte (bit 7: d_gyr_status, bits 0-6: fault code)
		msg[offset] = 0x01 // Fault code 1: +24VDC Low Fault

		// Set fault status (3 bytes)
		msg[offset+1] = 0x00
		msg[offset+2] = 0x00
		msg[offset+3] = 0x00

		// Set green status (3 bytes)
		msg[offset+4] = 0x00
		msg[offset+5] = 0x00
		msg[offset+6] = 0x00

		// Set yellow status (3 bytes)
		msg[offset+7] = 0x00
		msg[offset+8] = 0x00
		msg[offset+9] = 0x00

		// Set red status (3 bytes)
		msg[offset+10] = 0x00
		msg[offset+11] = 0x00
		msg[offset+12] = 0x00

		// Set NV status byte
		msg[offset+13] = 0x00

		// Set AC line voltage and frequency
		msg[offset+14] = 0x32 // Voltage (50V)
		msg[offset+15] = 0x3C // Frequency (60 Hz)

		// Set date/time (6 bytes, BCD format)
		msg[offset+16] = 0x56 // sec (BCD: 56)
		msg[offset+17] = 0x34 // min (BCD: 34)
		msg[offset+18] = 0x12 // hour (BCD: 12)
		msg[offset+19] = 0x15 // day (BCD: 15)
		msg[offset+20] = 0x07 // month (BCD: 07)
		msg[offset+21] = 0x21 // year (BCD: 21)

		// Set temperature
		msg[offset+22] = 0x5A // 90 - 40 = 50

		// Set T24VDC Input (if comm version > 0x36)
		msg[offset+23] = 0x32 // First voltage reading
		msg[offset+24] = 0x32 // Second voltage reading
		msg[offset+25] = 0x32 // Third voltage reading
		msg[offset+26] = 0x32 // Fourth voltage reading

		// Set MC Coil EE voltage
		msg[offset+27] = 0x32

		// Set Special Function voltages
		msg[offset+28] = 0x32 // Special Function 2
		msg[offset+29] = 0x32 // Special Function 1

		// Set T48VDC Signal Bus voltage
		msg[offset+30] = 0x32

		// Set channel voltage readings (55 bytes)
		for j := 0; j < 55; j++ {
			msg[offset+31+j] = 0x32 // Sample voltage value
		}

		// Set recurrent pulse statuses (9 bytes)
		for j := 0; j < 9; j++ {
			msg[offset+86+j] = 0x00
		}

		// Set GFON status (2 bytes)
		msg[offset+95] = 0x00
		msg[offset+96] = 0x00
	}

	// Calculate and set checksum (sum of all previous bytes)
	var sum byte
	for _, b := range msg[:len(msg)-1] {
		sum += b
	}
	msg[len(msg)-1] = ^sum // One's complement of sum

	return msg
}

func Test_parseRecurrentPulseStatus(t *testing.T) {
	tests := []struct {
		name        string
		green       int
		yellow      int
		red         int
		maxChannels int
		wantNil     bool
		wantGreen   map[int]bool
		wantYellow  map[int]bool
		wantRed     map[int]bool
	}{
		{
			name:        "all status zero",
			green:       0,
			yellow:      0,
			red:         0,
			maxChannels: 24,
			wantNil:     true,
		},
		{
			name:        "all status zero with maxChannels 0",
			green:       0,
			yellow:      0,
			red:         0,
			maxChannels: 0,
			wantNil:     true,
		},
		{
			name:        "only green set",
			green:       0xFF,
			yellow:      0,
			red:         0,
			maxChannels: 24,
			wantGreen:   map[int]bool{0: true, 1: true, 2: true, 3: true, 4: true, 5: true, 6: true, 7: true},
		},
		{
			name:        "only yellow set",
			green:       0,
			yellow:      0xFF,
			red:         0,
			maxChannels: 24,
			wantYellow:  map[int]bool{0: true, 1: true, 2: true, 3: true, 4: true, 5: true, 6: true, 7: true},
		},
		{
			name:        "only red set",
			green:       0,
			yellow:      0,
			red:         0xFF,
			maxChannels: 24,
			wantRed:     map[int]bool{0: true, 1: true, 2: true, 3: true, 4: true, 5: true, 6: true, 7: true},
		},
		{
			name:        "mixed status bits",
			green:       0x01,
			yellow:      0x02,
			red:         0x04,
			maxChannels: 8,
			wantGreen:   map[int]bool{0: true},
			wantYellow:  map[int]bool{1: true},
			wantRed:     map[int]bool{2: true},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotGreen, gotYellow, gotRed := parseRecurrentPulseStatus(tt.green, tt.yellow, tt.red, tt.maxChannels)
			if tt.wantNil {
				assert.Nil(t, gotGreen)
				assert.Nil(t, gotYellow)
				assert.Nil(t, gotRed)
				return
			}
			for i := 0; i < tt.maxChannels; i++ {
				wantG := false
				wantY := false
				wantR := false
				if v, ok := tt.wantGreen[i]; ok {
					wantG = v
				}
				if v, ok := tt.wantYellow[i]; ok {
					wantY = v
				}
				if v, ok := tt.wantRed[i]; ok {
					wantR = v
				}
				assert.Equal(t, wantG, gotGreen[i], "green[%d]", i)
				assert.Equal(t, wantY, gotYellow[i], "yellow[%d]", i)
				assert.Equal(t, wantR, gotRed[i], "red[%d]", i)
			}
		})
	}
}

func Test_parseFaultStatus(t *testing.T) {
	tests := []struct {
		name            string
		faultStatus     int
		showFaultStatus bool
		maxChannels     int
		setBits         []int
		wantNil         bool
	}{
		{"showFaultStatus false", 0xFF, false, 24, nil, true},
		{"all bits set", 0xFFFFFF, true, 24, func() (s []int) {
			for i := 0; i < 24; i++ {
				s = append(s, i)
			}
			return
		}(), false},
		{"no bits set", 0, true, 24, nil, false},
		{"mixed bits", 0x0F0F0F, true, 24, []int{0, 1, 2, 3, 8, 9, 10, 11, 16, 17, 18, 19}, false},
		{"maxChannels 0", 0xFF, true, 0, nil, true},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.wantNil {
				assert.Nil(t, parseFaultStatus(tt.faultStatus, tt.showFaultStatus, tt.maxChannels))
				return
			}
			want := make([]bool, tt.maxChannels)
			for _, i := range tt.setBits {
				want[i] = true
			}
			got := parseFaultStatus(tt.faultStatus, tt.showFaultStatus, tt.maxChannels)
			assert.Equal(t, want, got)
		})
	}
}

func Test_parseConflictingChannels(t *testing.T) {
	tests := []struct {
		name        string
		fault       byte
		gfonStatus  int
		maxChannels int
		setBits     []int
		wantNil     bool
	}{
		{"non-conflict fault", 1, 0xFF, 24, nil, true},
		{"conflict fault all bits set", 22, 0xFFFFFF, 24, func() (s []int) {
			for i := 0; i < 24; i++ {
				s = append(s, i)
			}
			return
		}(), false},
		{"conflict fault no bits set", 22, 0, 24, nil, false},
		{"conflict fault mixed bits", 22, 0x0F0F0F, 24, []int{0, 1, 2, 3, 8, 9, 10, 11, 16, 17, 18, 19}, false},
		{"maxChannels 0", 22, 0xFF, 0, nil, true},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.wantNil {
				assert.Nil(t, parseConflictingChannels(tt.fault, tt.gfonStatus, tt.maxChannels))
				return
			}
			want := make([]bool, tt.maxChannels)
			for _, i := range tt.setBits {
				want[i] = true
			}
			got := parseConflictingChannels(tt.fault, tt.gfonStatus, tt.maxChannels)
			assert.Equal(t, want, got)
		})
	}
}

func Test_parseRmsVoltages(t *testing.T) {
	tests := []struct {
		name          string
		responseBytes []byte
		startByte     int
		headerDetail  *helper.HeaderRecord
		setValue      int32
		volt220       bool
		voltDC        bool
		wantValue     int32
		maxChannels   int
		wantNil       bool
	}{
		{"normal voltage readings", make([]byte, 100), 0, &helper.HeaderRecord{MaxChannels: 4, Volt220: false, VoltDC: false}, 50, false, false, 50, 4, false},
		{"220V configuration", make([]byte, 100), 0, &helper.HeaderRecord{MaxChannels: 4, Volt220: true, VoltDC: false}, 50, true, false, 100, 4, false},
		{"DC voltage configuration", make([]byte, 100), 0, &helper.HeaderRecord{MaxChannels: 4, Volt220: false, VoltDC: true}, 48, false, true, 12, 4, false},
		{"maxChannels 0", make([]byte, 100), 0, &helper.HeaderRecord{MaxChannels: 0, Volt220: false, VoltDC: false}, 0, false, false, 0, 0, true},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Set the voltage values in the correct offsets
			for i := 0; i < tt.maxChannels; i++ {
				// greenOffset = 84, yellowOffset = 66, redOffset = 48
				tt.responseBytes[84-i] = byte(tt.setValue)
				tt.responseBytes[66-i] = byte(tt.setValue)
				tt.responseBytes[48-i] = byte(tt.setValue)
			}
			if tt.wantNil {
				gotGreen, gotYellow, gotRed := parseRmsVoltages(tt.responseBytes, tt.startByte, tt.headerDetail)
				assert.Nil(t, gotGreen)
				assert.Nil(t, gotYellow)
				assert.Nil(t, gotRed)
				return
			}
			want := make([]int32, tt.maxChannels)
			for i := 0; i < tt.maxChannels; i++ {
				want[i] = tt.wantValue
			}
			gotGreen, gotYellow, gotRed := parseRmsVoltages(tt.responseBytes, tt.startByte, tt.headerDetail)
			assert.Equal(t, want, gotGreen)
			assert.Equal(t, want, gotYellow)
			assert.Equal(t, want, gotRed)
		})
	}
}

func Test_formatT48VDCOrRedEnable(t *testing.T) {
	tests := []struct {
		name         string
		status       byte
		volt         byte
		headerDetail *helper.HeaderRecord
		wantT48VDC   string
		wantRed      string
	}{
		{
			name:   "DC voltage, red enable off",
			status: 0x00,
			volt:   48,
			headerDetail: &helper.HeaderRecord{
				VoltDC: true,
			},
			wantT48VDC: "12 Vrms",
			wantRed:    "",
		},
		{
			name:   "DC voltage, red enable on",
			status: 0x01,
			volt:   48,
			headerDetail: &helper.HeaderRecord{
				VoltDC: true,
			},
			wantT48VDC: "12 Vrms",
			wantRed:    "Active (12 Vrms)",
		},
		{
			name:   "AC voltage, red enable off",
			status: 0x00,
			volt:   48,
			headerDetail: &helper.HeaderRecord{
				VoltDC: false,
			},
			wantT48VDC: "",
			wantRed:    "Off (48 Vrms)",
		},
		{
			name:   "AC voltage, red enable on",
			status: 0x01,
			volt:   48,
			headerDetail: &helper.HeaderRecord{
				VoltDC: false,
			},
			wantT48VDC: "",
			wantRed:    "Active (48 Vrms)",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotT48VDC, gotRed := formatT48VDCOrRedEnable(tt.status, tt.volt, tt.headerDetail)
			assert.Equal(t, tt.wantT48VDC, gotT48VDC)
			assert.Equal(t, tt.wantRed, gotRed)
		})
	}
}

func Test_formatAcLine(t *testing.T) {
	tests := []struct {
		name    string
		volt    byte
		freq    byte
		volt220 bool
		want    string
	}{
		{
			name:    "normal voltage",
			volt:    120,
			freq:    60,
			volt220: false,
			want:    "120 Vrms @ 60 Hz",
		},
		{
			name:    "220V configuration",
			volt:    120,
			freq:    60,
			volt220: true,
			want:    "240 Vrms @ 60 Hz",
		},
		{
			name:    "zero values",
			volt:    0,
			freq:    0,
			volt220: false,
			want:    "0 Vrms @ 0 Hz",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := formatAcLine(tt.volt, tt.freq, tt.volt220)
			assert.Equal(t, tt.want, got)
		})
	}
}

func Test_formatMCCoilEE(t *testing.T) {
	tests := []struct {
		name   string
		status byte
		volt   byte
		want   string
	}{
		{
			name:   "flash mode",
			status: 0x08,
			volt:   50,
			want:   "Flash (50 Vrms)",
		},
		{
			name:   "auto mode",
			status: 0x00,
			volt:   50,
			want:   "Auto (50 Vrms)",
		},
		{
			name:   "zero voltage",
			status: 0x08,
			volt:   0,
			want:   "Flash (0 Vrms)",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := formatMCCoilEE(tt.status, tt.volt)
			assert.Equal(t, tt.want, got)
		})
	}
}

func Test_formatSpecialFunction(t *testing.T) {
	tests := []struct {
		name         string
		status       byte
		volt         byte
		bit          int
		headerDetail *helper.HeaderRecord
		want         string
	}{
		{
			name:   "function 1 active",
			status: 0x02,
			volt:   50,
			bit:    1,
			headerDetail: &helper.HeaderRecord{
				VoltDC: false,
			},
			want: "Active (50 Vrms)",
		},
		{
			name:   "function 1 off",
			status: 0x00,
			volt:   50,
			bit:    1,
			headerDetail: &helper.HeaderRecord{
				VoltDC: false,
			},
			want: "Off (50 Vrms)",
		},
		{
			name:   "function 2 active",
			status: 0x04,
			volt:   50,
			bit:    2,
			headerDetail: &helper.HeaderRecord{
				VoltDC: false,
			},
			want: "Active (50 Vrms)",
		},
		{
			name:   "DC voltage",
			status: 0x02,
			volt:   50,
			bit:    1,
			headerDetail: &helper.HeaderRecord{
				VoltDC: true,
			},
			want: "Active (12 Vrms)",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := formatSpecialFunction(tt.status, tt.volt, tt.bit, tt.headerDetail)
			assert.Equal(t, tt.want, got)
		})
	}
}

func Test_formatWDTMonitor(t *testing.T) {
	tests := []struct {
		name   string
		status byte
		want   string
	}{
		{
			name:   "active",
			status: 0x80,
			want:   "Active",
		},
		{
			name:   "off",
			status: 0x00,
			want:   "Off",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := formatWDTMonitor(tt.status)
			assert.Equal(t, tt.want, got)
		})
	}
}

func Test_formatT24VDCInput(t *testing.T) {
	tests := []struct {
		name        string
		volt1       byte
		volt2       byte
		commVersion int
		want        string
	}{
		{
			name:        "old comm version",
			volt1:       50,
			volt2:       50,
			commVersion: 0x35,
			want:        "",
		},
		{
			name:        "new comm version",
			volt1:       50,
			volt2:       50,
			commVersion: 0x37,
			want:        "6.2 Vrms, 1.6 Vac",
		},
		{
			name:        "zero values",
			volt1:       0,
			volt2:       0,
			commVersion: 0x37,
			want:        "0.0 Vrms, 0.0 Vac",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := formatT24VDCInput(tt.volt1, tt.volt2, tt.commVersion)
			assert.Equal(t, tt.want, got)
		})
	}
}

func Test_getFaultStatus2018(t *testing.T) {
	tests := []struct {
		name         string
		fault        byte
		faultStatus  int
		nvStat       byte
		headerRecord *helper.HeaderRecord
		wantString   string
		wantShow     bool
	}{
		{
			name:        "+24VDC Low Fault",
			fault:       1,
			faultStatus: 0,
			nvStat:      0,
			headerRecord: &helper.HeaderRecord{
				CommVersion: 0x30,
			},
			wantString: "+24VDC Low Fault",
			wantShow:   false,
		},
		{
			name:        "CU Watchdog Fault",
			fault:       2,
			faultStatus: 0,
			nvStat:      0x40,
			headerRecord: &helper.HeaderRecord{
				CommVersion: 0x30,
			},
			wantString: "CU Watchdog Fault",
			wantShow:   false,
		},
		{
			name:        "Conflict Fault",
			fault:       3,
			faultStatus: 0,
			nvStat:      0,
			headerRecord: &helper.HeaderRecord{
				CommVersion: 0x30,
			},
			wantString: "Conflict Fault",
			wantShow:   true,
		},
		{
			name:        "Dual Indication Fault",
			fault:       4,
			faultStatus: 0,
			nvStat:      0,
			headerRecord: &helper.HeaderRecord{
				CommVersion: 0x30,
			},
			wantString: "Dual Indication Fault",
			wantShow:   true,
		},
		{
			name:        "Red Fail Fault",
			fault:       5,
			faultStatus: 0,
			nvStat:      0,
			headerRecord: &helper.HeaderRecord{
				CommVersion: 0x30,
			},
			wantString: "Red Fail Fault",
			wantShow:   true,
		},
		{
			name:        "Clearance (Skipped Yellow) Fault",
			fault:       6,
			faultStatus: 0,
			nvStat:      0,
			headerRecord: &helper.HeaderRecord{
				CommVersion: 0x30,
			},
			wantString: "Clearance (Skipped Yellow) Fault",
			wantShow:   true,
		},
		{
			name:        "BND Fault",
			fault:       7,
			faultStatus: 0,
			nvStat:      0,
			headerRecord: &helper.HeaderRecord{
				CommVersion: 0x30,
			},
			wantString: "BND Fault",
			wantShow:   true,
		},
		{
			name:        "Diagnostic Fault - RMS Engine A/D error",
			fault:       8,
			faultStatus: 1430,
			nvStat:      0,
			headerRecord: &helper.HeaderRecord{
				CommVersion: 0x30,
			},
			wantString: "Diagnostic Fault (RMS Engine A/D error)",
			wantShow:   true,
		},
		{
			name:        "Diagnostic Fault - RMS Engine comm error",
			fault:       8,
			faultStatus: 1670,
			nvStat:      0,
			headerRecord: &helper.HeaderRecord{
				CommVersion: 0x30,
			},
			wantString: "Diagnostic Fault (RMS Engine comm error)",
			wantShow:   true,
		},
		{
			name:        "Diagnostic Fault - EEprom error",
			fault:       8,
			faultStatus: 2000,
			nvStat:      0,
			headerRecord: &helper.HeaderRecord{
				CommVersion: 0x30,
			},
			wantString: "Diagnostic Fault (EEprom error)",
			wantShow:   true,
		},
		{
			name:        "Diagnostic Fault - program card serial path",
			fault:       8,
			faultStatus: 2500,
			nvStat:      0,
			headerRecord: &helper.HeaderRecord{
				CommVersion: 0x30,
			},
			wantString: "Diagnostic Fault (program card serial path)",
			wantShow:   true,
		},
		{
			name:        "Diagnostic Fault - switch serial path",
			fault:       8,
			faultStatus: 3300,
			nvStat:      0,
			headerRecord: &helper.HeaderRecord{
				CommVersion: 0x30,
			},
			wantString: "Diagnostic Fault (switch serial path)",
			wantShow:   true,
		},
		{
			name:        "Diagnostic Fault - display serial path",
			fault:       8,
			faultStatus: 5000,
			nvStat:      0,
			headerRecord: &helper.HeaderRecord{
				CommVersion: 0x30,
			},
			wantString: "Diagnostic Fault (display serial path)",
			wantShow:   true,
		},
		{
			name:        "Diagnostic Fault - logic serial path",
			fault:       8,
			faultStatus: 10000,
			nvStat:      0,
			headerRecord: &helper.HeaderRecord{
				CommVersion: 0x30,
			},
			wantString: "Diagnostic Fault (logic serial path)",
			wantShow:   true,
		},
		{
			name:        "Program Card Ajar Fault",
			fault:       9,
			faultStatus: 0,
			nvStat:      0,
			headerRecord: &helper.HeaderRecord{
				CommVersion: 0x30,
			},
			wantString: "Program Card Ajar Fault",
			wantShow:   false,
		},
		{
			name:        "Red Cable Fault",
			fault:       12,
			faultStatus: 0,
			nvStat:      0,
			headerRecord: &helper.HeaderRecord{
				CommVersion: 0x30,
			},
			wantString: "Red Cable Fault",
			wantShow:   true,
		},
		{
			name:        "Configuration Change Fault",
			fault:       13,
			faultStatus: 123,
			nvStat:      0,
			headerRecord: &helper.HeaderRecord{
				CommVersion: 0x40,
			},
			wantString: "Configuration Change Fault (Check Value = 123)",
			wantShow:   false,
		},
		{
			name:        "Clearance (Short Yellow) Fault",
			fault:       14,
			faultStatus: 0,
			nvStat:      0,
			headerRecord: &helper.HeaderRecord{
				CommVersion: 0x30,
			},
			wantString: "Clearance (Short Yellow) Fault",
			wantShow:   true,
		},
		{
			name:        "Recurrent Pulse Conflict",
			fault:       15,
			faultStatus: 0,
			nvStat:      0,
			headerRecord: &helper.HeaderRecord{
				CommVersion: 0x30,
			},
			wantString: "Recurrent Pulse Conflict",
			wantShow:   true,
		},
		{
			name:        "Recurrent Pulse Dual Indication",
			fault:       16,
			faultStatus: 0,
			nvStat:      0,
			headerRecord: &helper.HeaderRecord{
				CommVersion: 0x30,
			},
			wantString: "Recurrent Pulse Dual Indication",
			wantShow:   true,
		},
		{
			name:        "Recurrent Pulse Red Fail",
			fault:       17,
			faultStatus: 0,
			nvStat:      0,
			headerRecord: &helper.HeaderRecord{
				CommVersion: 0x30,
			},
			wantString: "Recurrent Pulse Red Fail",
			wantShow:   true,
		},
		{
			name:        "+48VDC Fault",
			fault:       18,
			faultStatus: 0,
			nvStat:      0,
			headerRecord: &helper.HeaderRecord{
				CommVersion: 0x30,
			},
			wantString: "+48VDC Fault",
			wantShow:   true,
		},
		{
			name:        "Data Key Absent",
			fault:       19,
			faultStatus: 0,
			nvStat:      0,
			headerRecord: &helper.HeaderRecord{
				CommVersion: 0x30,
			},
			wantString: "Data Key Absent",
			wantShow:   false,
		},
		{
			name:        "Data Key FCS Error",
			fault:       20,
			faultStatus: 0,
			nvStat:      0,
			headerRecord: &helper.HeaderRecord{
				CommVersion: 0x30,
			},
			wantString: "Data Key FCS Error",
			wantShow:   false,
		},
		{
			name:        "Data Key Invalid Parameter Error",
			fault:       21,
			faultStatus: 0,
			nvStat:      0,
			headerRecord: &helper.HeaderRecord{
				CommVersion: 0x30,
			},
			wantString: "Data Key Invalid Parameter Error",
			wantShow:   false,
		},
		{
			name:        "Minimum Yellow + Red Clearance Fault",
			fault:       22,
			faultStatus: 0,
			nvStat:      0,
			headerRecord: &helper.HeaderRecord{
				CommVersion: 0x30,
			},
			wantString: "Minimum Yellow + Red Clearance Fault",
			wantShow:   false,
		},
		{
			name:        "+24VDC High Fault",
			fault:       23,
			faultStatus: 0,
			nvStat:      0,
			headerRecord: &helper.HeaderRecord{
				CommVersion: 0x30,
			},
			wantString: "+24VDC High Fault",
			wantShow:   false,
		},
		{
			name:        "+24VDC Ripple Fault",
			fault:       24,
			faultStatus: 0,
			nvStat:      0,
			headerRecord: &helper.HeaderRecord{
				CommVersion: 0x30,
			},
			wantString: "+24VDC Ripple Fault",
			wantShow:   false,
		},
		{
			name:        "Program Card Fault",
			fault:       25,
			faultStatus: 0,
			nvStat:      0,
			headerRecord: &helper.HeaderRecord{
				CommVersion: 0x30,
			},
			wantString: "Program Card Fault (Program Card is not 18 Channel)",
			wantShow:   false,
		},
		{
			name:        "Fault with previous Watchdog Fault Status and NVPrevWD bit set",
			fault:       1,
			faultStatus: 0,
			nvStat:      0x40,
			headerRecord: &helper.HeaderRecord{
				CommVersion: 0x30,
			},
			wantString: "+24VDC Low Fault with previous Watchdog Fault Status",
			wantShow:   false,
		},
		{
			name:        "Fault with previous Watchdog Fault Status and NVPrevWD bit not set",
			fault:       1,
			faultStatus: 0,
			nvStat:      0x00,
			headerRecord: &helper.HeaderRecord{
				CommVersion: 0x30,
			},
			wantString: "+24VDC Low Fault",
			wantShow:   false,
		},
		{
			name:        "Diagnostic Fault - unknown status",
			fault:       8,
			faultStatus: 9999,
			nvStat:      0,
			headerRecord: &helper.HeaderRecord{
				CommVersion: 0x30,
			},
			wantString: "",
			wantShow:   true,
		},
		{
			name:        "FYA Flash Rate Fault",
			fault:       27,
			faultStatus: 0,
			nvStat:      0,
			headerRecord: &helper.HeaderRecord{
				CommVersion: 0x30,
			},
			wantString: "FYA Flash Rate Fault",
			wantShow:   true,
		},
		{
			name:        "Undefined fault type",
			fault:       99,
			faultStatus: 0,
			nvStat:      0,
			headerRecord: &helper.HeaderRecord{
				CommVersion: 0x30,
			},
			wantString: "undefined fault type",
			wantShow:   true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotString, gotShow := getFaultStatus2018(tt.headerRecord, tt.fault, tt.faultStatus, tt.nvStat)
			assert.Equal(t, tt.wantString, gotString)
			assert.Equal(t, tt.wantShow, gotShow)
		})
	}
}

func Test_parseRecord(t *testing.T) {
	tests := []struct {
		name          string
		responseBytes []byte
		startByte     int
		headerDetail  *helper.HeaderRecord
		httpHeader    *pubsubdata.HeaderDetails
		want          *helper.LogPreviousFailRecord
		wantErr       bool
	}{
		{
			name: "valid record with all fields",
			responseBytes: func() []byte {
				bytes := make([]byte, 100)
				// Set fault status byte
				bytes[0] = 0x01 // Fault code 1: +24VDC Low Fault
				// Set fault status (3 bytes)
				bytes[1] = 0x00
				bytes[2] = 0x00
				bytes[3] = 0x00
				// Set green status (3 bytes)
				bytes[4] = 0x00
				bytes[5] = 0x00
				bytes[6] = 0x00
				// Set yellow status (3 bytes)
				bytes[7] = 0x00
				bytes[8] = 0x00
				bytes[9] = 0x00
				// Set red status (3 bytes)
				bytes[10] = 0x00
				bytes[11] = 0x00
				bytes[12] = 0x00
				// Set NV status byte
				bytes[13] = 0x00
				// Set AC line voltage and frequency
				bytes[14] = 0x32 // 50V
				bytes[15] = 0x3C // 60Hz
				// Set date/time (BCD format)
				bytes[16] = 0x56 // sec
				bytes[17] = 0x34 // min
				bytes[18] = 0x12 // hour
				bytes[19] = 0x15 // day
				bytes[20] = 0x07 // month
				bytes[21] = 0x21 // year (2021)
				// Set temperature
				bytes[22] = 0x5A // 90 - 40 = 50
				// Set T24VDC Input
				bytes[23] = 0x32
				bytes[24] = 0x32
				bytes[25] = 0x32
				bytes[26] = 0x32
				// Set MC Coil EE voltage
				bytes[27] = 0x32
				// Set Special Function voltages
				bytes[28] = 0x32 // Special Function 2
				bytes[29] = 0x32 // Special Function 1
				// Set T48VDC Signal Bus voltage
				bytes[30] = 0x32
				// Set channel voltage readings
				for i := 31; i < 86; i++ {
					bytes[i] = 0x32
				}
				// Set recurrent pulse statuses
				for i := 86; i < 95; i++ {
					bytes[i] = 0x00
				}
				// Set GFON status
				bytes[95] = 0x00
				bytes[96] = 0x00
				return bytes
			}(),
			startByte: 0,
			headerDetail: &helper.HeaderRecord{
				MaxChannels: 4,
				Volt220:     false,
				VoltDC:      false,
				CommVersion: 0x37,
			},
			httpHeader: &pubsubdata.HeaderDetails{
				GatewayTimezone: "UTC",
			},
			want: &helper.LogPreviousFailRecord{
				Fault:                             "+24VDC Low Fault",
				Temperature:                       50,
				AcLine:                            "50 Vrms @ 60 Hz",
				T48VDCSignalBus:                   "",
				RedEnable:                         "Off (50 Vrms)",
				MCCoilEE:                          "Auto (50 Vrms)",
				SpecialFunction1:                  "Off (50 Vrms)",
				SpecialFunction2:                  "Off (50 Vrms)",
				WDTMonitor:                        "Off",
				T24VDCInput:                       "6.2 Vrms, 1.6 Vac",
				FaultStatus:                       nil,
				ChannelGreenStatus:                []bool{false, false, false, false},
				ChannelYellowStatus:               []bool{false, false, false, false},
				ChannelRedStatus:                  []bool{false, false, false, false},
				NextConflictingChannels:           nil,
				ChannelGreenRecurrentPulseStatus:  []bool{false, false, false, false},
				ChannelYellowRecurrentPulseStatus: []bool{false, false, false, false},
				ChannelRedRecurrentPulseStatus:    []bool{false, false, false, false},
				ChannelRedRmsVoltage:              []int32{50, 50, 50, 50},
				ChannelYellowRmsVoltage:           []int32{50, 50, 50, 50},
				ChannelGreenRmsVoltage:            []int32{50, 50, 50, 50},
			},
			wantErr: false,
		},
		{
			name: "invalid date time",
			responseBytes: func() []byte {
				bytes := make([]byte, 100)
				// Set invalid date/time (BCD format)
				bytes[16] = 0x99 // Invalid seconds
				return bytes
			}(),
			startByte: 0,
			headerDetail: &helper.HeaderRecord{
				MaxChannels: 4,
				Volt220:     false,
				VoltDC:      false,
				CommVersion: 0x37,
			},
			httpHeader: &pubsubdata.HeaderDetails{
				GatewayTimezone: "UTC",
			},
			want:    nil,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := parsePreviousFailRecord(tt.responseBytes, tt.startByte, tt.headerDetail, tt.httpHeader)
			if tt.wantErr {
				assert.Error(t, err)
				return
			}
			assert.NoError(t, err)
			assert.Equal(t, tt.want.Fault, got.Fault)
			assert.Equal(t, tt.want.Temperature, got.Temperature)
			assert.Equal(t, tt.want.AcLine, got.AcLine)
			assert.Equal(t, tt.want.T48VDCSignalBus, got.T48VDCSignalBus)
			assert.Equal(t, tt.want.RedEnable, got.RedEnable)
			assert.Equal(t, tt.want.MCCoilEE, got.MCCoilEE)
			assert.Equal(t, tt.want.SpecialFunction1, got.SpecialFunction1)
			assert.Equal(t, tt.want.SpecialFunction2, got.SpecialFunction2)
			assert.Equal(t, tt.want.WDTMonitor, got.WDTMonitor)
			assert.Equal(t, tt.want.T24VDCInput, got.T24VDCInput)
			assert.Equal(t, tt.want.FaultStatus, got.FaultStatus)
			assert.Equal(t, tt.want.ChannelGreenStatus, got.ChannelGreenStatus)
			assert.Equal(t, tt.want.ChannelYellowStatus, got.ChannelYellowStatus)
			assert.Equal(t, tt.want.ChannelRedStatus, got.ChannelRedStatus)
			assert.Equal(t, tt.want.NextConflictingChannels, got.NextConflictingChannels)
			assert.Equal(t, tt.want.ChannelGreenRecurrentPulseStatus, got.ChannelGreenRecurrentPulseStatus)
			assert.Equal(t, tt.want.ChannelYellowRecurrentPulseStatus, got.ChannelYellowRecurrentPulseStatus)
			assert.Equal(t, tt.want.ChannelRedRecurrentPulseStatus, got.ChannelRedRecurrentPulseStatus)
			assert.Equal(t, tt.want.ChannelRedRmsVoltage, got.ChannelRedRmsVoltage)
			assert.Equal(t, tt.want.ChannelYellowRmsVoltage, got.ChannelYellowRmsVoltage)
			assert.Equal(t, tt.want.ChannelGreenRmsVoltage, got.ChannelGreenRmsVoltage)
		})
	}
}

func Test_LogPreviousFail(t *testing.T) {
	tests := []struct {
		name        string
		device      EDIECL2010
		httpHeader  *pubsubdata.HeaderDetails
		byteMsg     []byte
		header      *helper.HeaderRecord
		want        *helper.LogPreviousFailRecords
		wantErr     bool
		errContains string
	}{
		{
			name:   "nil header",
			device: EDIECL2010{},
			httpHeader: &pubsubdata.HeaderDetails{
				GatewayTimezone: "UTC",
			},
			byteMsg:     []byte{},
			header:      nil,
			want:        nil,
			wantErr:     true,
			errContains: "header is nil",
		},
		{
			name:   "invalid checksum",
			device: EDIECL2010{},
			httpHeader: &pubsubdata.HeaderDetails{
				GatewayTimezone: "UTC",
			},
			byteMsg: func() []byte {
				msg := buildValidLogPreviousFailMsg(1)
				msg[len(msg)-1] = ^msg[len(msg)-1] // Flipping bits of checksum always provides an invalid checksum
				return msg
			}(),
			header: &helper.HeaderRecord{
				Model:       helper.Ecl2010,
				MaxChannels: 4,
				Volt220:     false,
				VoltDC:      false,
				CommVersion: 0x37,
			},
			want:        nil,
			wantErr:     true,
			errContains: "checksum failure",
		},
		{
			name:   "valid message with one record",
			device: EDIECL2010{},
			httpHeader: &pubsubdata.HeaderDetails{
				GatewayTimezone: "UTC",
			},
			byteMsg: buildValidLogPreviousFailMsg(1),
			header: &helper.HeaderRecord{
				Model:       helper.Ecl2010,
				MaxChannels: 4,
				Volt220:     false,
				VoltDC:      false,
				CommVersion: 0x37,
			},
			want: &helper.LogPreviousFailRecords{
				DeviceModel: DeviceModel,
				Records: []helper.LogPreviousFailRecord{
					{
						Fault:                             "+24VDC Low Fault",
						Temperature:                       50,
						AcLine:                            "50 Vrms @ 60 Hz",
						T48VDCSignalBus:                   "",
						RedEnable:                         "Off (50 Vrms)",
						MCCoilEE:                          "Auto (50 Vrms)",
						SpecialFunction1:                  "Off (50 Vrms)",
						SpecialFunction2:                  "Off (50 Vrms)",
						WDTMonitor:                        "Off",
						T24VDCInput:                       "6.2 Vrms, 1.6 Vac",
						FaultStatus:                       nil,
						ChannelGreenStatus:                []bool{false, false, false, false},
						ChannelYellowStatus:               []bool{false, false, false, false},
						ChannelRedStatus:                  []bool{false, false, false, false},
						NextConflictingChannels:           nil,
						ChannelGreenRecurrentPulseStatus:  []bool{false, false, false, false},
						ChannelYellowRecurrentPulseStatus: []bool{false, false, false, false},
						ChannelRedRecurrentPulseStatus:    []bool{false, false, false, false},
						ChannelRedRmsVoltage:              []int32{50, 50, 50, 50},
						ChannelYellowRmsVoltage:           []int32{50, 50, 50, 50},
						ChannelGreenRmsVoltage:            []int32{50, 50, 50, 50},
					},
				},
			},
			wantErr: false,
		},
		{
			name:   "invalid message length",
			device: EDIECL2010{},
			httpHeader: &pubsubdata.HeaderDetails{
				GatewayTimezone: "UTC",
			},
			byteMsg: func() []byte {
				// Create a message that's too short but has valid checksum
				msg := make([]byte, HeaderLength+2) // Header + 1 record count byte + checksum

				// Set header bytes
				msg[0] = 0x01 // Message type identifier
				msg[1] = 0x01 // Device address
				msg[2] = 0x01 // Command code
				msg[3] = 0x00 // Response status
				msg[4] = 0x00 // Reserved
				msg[5] = 0x00 // Reserved
				msg[6] = 0x00 // Reserved
				msg[7] = 0x01 // Number of records (1)

				// Calculate and set checksum
				var sum byte
				for _, b := range msg[:len(msg)-1] {
					sum += b
				}
				msg[len(msg)-1] = ^sum // One's complement of sum

				return msg
			}(),
			header: &helper.HeaderRecord{
				Model:       helper.Ecl2010,
				MaxChannels: 4,
				Volt220:     false,
				VoltDC:      false,
				CommVersion: 0x37,
			},
			want:        nil,
			wantErr:     true,
			errContains: "error byte length",
		},
		{
			name:   "unsupported device",
			device: EDIECL2010{},
			httpHeader: &pubsubdata.HeaderDetails{
				GatewayTimezone: "UTC",
			},
			byteMsg: buildValidLogPreviousFailMsg(1),
			header: &helper.HeaderRecord{
				Model:            helper.CMUip2212_hv,
				MaxChannels:      4,
				Volt220:          false,
				VoltDC:           false,
				CommVersion:      0x37,
				FirmwareRevision: 0x51, // > 0x50
			},
			want:        nil,
			wantErr:     true,
			errContains: "unsupported device",
		},
		{
			name:   "zero records",
			device: EDIECL2010{},
			httpHeader: &pubsubdata.HeaderDetails{
				GatewayTimezone: "UTC",
			},
			byteMsg: buildValidLogPreviousFailMsg(0),
			header: &helper.HeaderRecord{
				Model:       helper.Ecl2010,
				MaxChannels: 4,
				Volt220:     false,
				VoltDC:      false,
				CommVersion: 0x37,
			},
			want: &helper.LogPreviousFailRecords{
				DeviceModel: DeviceModel,
				Records:     nil,
			},
			wantErr: false,
		},
		{
			name:   "invalid BCD date-time in record",
			device: EDIECL2010{},
			httpHeader: &pubsubdata.HeaderDetails{
				GatewayTimezone: "UTC",
			},
			byteMsg: func() []byte {
				msg := buildValidLogPreviousFailMsg(1)
				// Set invalid BCD values for date-time
				msg[PreviousFailRecordsStartOffset+16] = 0x99 // Invalid seconds
				msg[PreviousFailRecordsStartOffset+17] = 0x99 // Invalid minutes
				msg[PreviousFailRecordsStartOffset+18] = 0x99 // Invalid hours
				msg[PreviousFailRecordsStartOffset+19] = 0x99 // Invalid day
				msg[PreviousFailRecordsStartOffset+20] = 0x99 // Invalid month
				msg[PreviousFailRecordsStartOffset+21] = 0x99 // Invalid year
				// Recalculate checksum
				var sum byte
				for i := 0; i < len(msg)-1; i++ {
					sum += msg[i]
				}
				msg[len(msg)-1] = ^sum
				return msg
			}(),
			header: &helper.HeaderRecord{
				Model:       helper.Ecl2010,
				MaxChannels: 4,
				Volt220:     false,
				VoltDC:      false,
				CommVersion: 0x37,
			},
			want:        nil,
			wantErr:     true,
			errContains: "bad month",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := tt.device.LogPreviousFail(tt.httpHeader, tt.byteMsg, tt.header)
			if tt.wantErr {
				assert.Error(t, err)
				if tt.errContains != "" {
					assert.Contains(t, err.Error(), tt.errContains)
				}
				return
			}
			assert.NoError(t, err)
			assert.Equal(t, tt.want.DeviceModel, got.DeviceModel)
			assert.Equal(t, len(tt.want.Records), len(got.Records))
			for i, wantRecord := range tt.want.Records {
				gotRecord := got.Records[i]
				assert.Equal(t, wantRecord.Fault, gotRecord.Fault)
				assert.Equal(t, wantRecord.Temperature, gotRecord.Temperature)
				assert.Equal(t, wantRecord.AcLine, gotRecord.AcLine)
				assert.Equal(t, wantRecord.T48VDCSignalBus, gotRecord.T48VDCSignalBus)
				assert.Equal(t, wantRecord.RedEnable, gotRecord.RedEnable)
				assert.Equal(t, wantRecord.MCCoilEE, gotRecord.MCCoilEE)
				assert.Equal(t, wantRecord.SpecialFunction1, gotRecord.SpecialFunction1)
				assert.Equal(t, wantRecord.SpecialFunction2, gotRecord.SpecialFunction2)
				assert.Equal(t, wantRecord.WDTMonitor, gotRecord.WDTMonitor)
				assert.Equal(t, wantRecord.T24VDCInput, gotRecord.T24VDCInput)
				assert.Equal(t, wantRecord.FaultStatus, gotRecord.FaultStatus)
				assert.Equal(t, wantRecord.ChannelGreenStatus, gotRecord.ChannelGreenStatus)
				assert.Equal(t, wantRecord.ChannelYellowStatus, gotRecord.ChannelYellowStatus)
				assert.Equal(t, wantRecord.ChannelRedStatus, gotRecord.ChannelRedStatus)
				assert.Equal(t, wantRecord.NextConflictingChannels, gotRecord.NextConflictingChannels)
				assert.Equal(t, wantRecord.ChannelGreenRecurrentPulseStatus, gotRecord.ChannelGreenRecurrentPulseStatus)
				assert.Equal(t, wantRecord.ChannelYellowRecurrentPulseStatus, gotRecord.ChannelYellowRecurrentPulseStatus)
				assert.Equal(t, wantRecord.ChannelRedRecurrentPulseStatus, gotRecord.ChannelRedRecurrentPulseStatus)
				assert.Equal(t, wantRecord.ChannelRedRmsVoltage, gotRecord.ChannelRedRmsVoltage)
				assert.Equal(t, wantRecord.ChannelYellowRmsVoltage, gotRecord.ChannelYellowRmsVoltage)
				assert.Equal(t, wantRecord.ChannelGreenRmsVoltage, gotRecord.ChannelGreenRmsVoltage)
			}
		})
	}
}

package jwttokens

import (
	"fmt"
)

func ValidateRoleInt(role int64) (roleName string, err error) {
	switch role {
	case 1:
		return "user", nil
	case 2:
		return "integrator", nil
	default:
		return "unknown", fmt.<PERSON><PERSON><PERSON>("unknown role (%d)", role)
	}
}

func ValidateRoleString(role string) (roleName string, err error) {
	switch role {
	case "user":
		return "user", nil
	case "integrator":
		return "integrator", nil
	default:
		return "unknown", fmt.<PERSON><PERSON><PERSON>("unknown role (%s)", role)
	}
}

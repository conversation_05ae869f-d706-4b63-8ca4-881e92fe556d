package ingest

import "slices"

// MessageType is a custom type representing acceptable message types.
type MessageType string

const (
	// List the allowed message types.
	MessageTypeRMSData           MessageType = "rmsData"
	MessageTypeRMSEngine         MessageType = "rmsEngine"
	MessageTypeMonitorName       MessageType = "monitorName"
	MessageTypeMacAddress        MessageType = "macAddress"
	MessageTypeFaultLogs         MessageType = "faultLogs"
	MessageTypePerfStats         MessageType = "perfStats"
	MessageTypeGatewayLog        MessageType = "gatewayLog"
	MessageTypeFaultNotification MessageType = "faultNotification"
)

// AllowedMessageTypes returns a slice containing all valid MessageType constants.
func AllowedMessageTypes() []MessageType {
	return []MessageType{
		MessageTypeRMSData,
		MessageTypeRMSEngine,
		MessageTypeMacAddress,
		MessageTypeMonitorName,
		MessageTypeFaultLogs,
		MessageTypePerfStats,
		MessageTypeGatewayLog,
		MessageTypeFaultNotification,
	}
}

// IsValid checks if the MessageType has one of the allowed values.
func (mt MessageType) IsValid() bool {
	return slices.Contains(AllowedMessageTypes(), mt)
}

// These are the valid versions
var ApprovedMessageVersions = []string{
	"v1",
}

type dbAuthData struct {
	OrganizationIdentifier string `db:"organizationidentifier"`
}

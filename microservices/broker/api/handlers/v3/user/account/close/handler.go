package close

import (
	"context"
	"net/http"

	authorizer "synapse-its.com/shared/api/authorizer"
	response "synapse-its.com/shared/api/response"
	connect "synapse-its.com/shared/connect"
	"synapse-its.com/shared/logger"
)

// HandlerDeps bundles dependencies for injection and testing.
type HandlerDeps struct {
	UserInfoFromContext func(ctx context.Context) (*authorizer.UserInfo, bool)
	GetConnections      func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error)
	InactivateAccount   func(db connect.DatabaseExecutor, userID int64) error
	RemoveTokens        func(db connect.DatabaseExecutor, userID int64) error
}

// HandlerWithDeps returns an http.HandlerFunc with injected deps.
func HandlerWithDeps(deps HandlerDeps) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()

		// Get user info
		userInfo, ok := deps.UserInfoFromContext(ctx)
		if !ok {
			logger.Error("Unable to retrieve user info from request context")
			response.CreateInternalErrorResponse(w)
			return
		}

		// Get connections
		conns, err := deps.GetConnections(ctx)
		if err != nil {
			logger.Errorf("%v", err)
			response.CreateInternalErrorResponse(w)
			return
		}
		pg := conns.Postgres

		// Inactivate account
		if err := deps.InactivateAccount(pg, userInfo.ID); err != nil {
			logger.Errorf("error - setting user account to inactive. user_id: (%v), err: (%v)", userInfo.ID, err)
			response.CreateInternalErrorResponse(w)
			return
		}

		// Remove tokens
		if err := deps.RemoveTokens(pg, userInfo.ID); err != nil {
			logger.Errorf("error - removing user's tokens. user_id: (%v), err: (%v)", userInfo.ID, err)
			response.CreateInternalErrorResponse(w)
			return
		}

		response.CreateSuccessResponse("", w)
	}
}

// Handler is the production-ready HTTP handler using default deps.
var Handler = HandlerWithDeps(HandlerDeps{
	UserInfoFromContext: authorizer.UserInfoFromContext,
	GetConnections:      connect.GetConnections,
	InactivateAccount:   inactivateUserAccount,
	RemoveTokens:        removeUserTokens,
})

// inactivateUserAccount and removeUserTokens remain unchanged below.
var inactivateUserAccount = func(db connect.DatabaseExecutor, userID int64) error {
	query := "UPDATE {{User}} SET IsEnabled = 0 WHERE Id = $1"
	result, err := db.Exec(query, userID)
	if err != nil {
		return err
	}
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return err
	}
	logger.Debugf("UserId (%v) has been inactivated, row(s) affected = (%v)", userID, rowsAffected)
	return nil
}

var removeUserTokens = func(db connect.DatabaseExecutor, userID int64) error {
	query := "DELETE FROM {{UserToken}} WHERE UserId = $1"
	result, err := db.Exec(query, userID)
	if err != nil {
		return err
	}
	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return err
	}
	logger.Debugf("(%v) UserTokens for UserId (%v) have been deleted", rowsAffected, userID)
	return nil
}

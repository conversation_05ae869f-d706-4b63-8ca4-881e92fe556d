import { Component } from '@angular/core';
import { AuthService, UserProfile } from './services/auth.service';
import { catchError, of, Observable } from 'rxjs';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.css'],
  standalone: false,
})
export class AppComponent {
  user$: Observable<UserProfile | null>;

  constructor(private auth: AuthService) {
    this.user$ = this.auth.getProfile().pipe(
      catchError(() => of(null))
    )
  }

  login() { this.auth.login(); }
  logout() { this.auth.logout(); }
}

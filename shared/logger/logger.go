package logger

import (
	"encoding/json"
	"fmt"
	"os"
	"runtime"
	"runtime/debug"
	"time"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

var (
	Logger   *zap.Logger
	exitFunc = os.Exit
)

func init() {
	logLevel := getLogLevel()

	// Set up zap configuration for production (JSON output)
	config := zap.Config{
		Level:       zap.NewAtomicLevelAt(logLevel),
		Development: false,
		Encoding:    "json",
		EncoderConfig: zapcore.EncoderConfig{
			TimeKey:        "time",
			LevelKey:       "severity",
			NameKey:        "logger",
			MessageKey:     "message",
			LineEnding:     zapcore.DefaultLineEnding,
			EncodeLevel:    zapcore.LowercaseLevelEncoder,
			EncodeTime:     zapcore.RFC3339NanoTimeEncoder,
			EncodeDuration: zapcore.NanosDurationEncoder,
			EncodeCaller:   zapcore.ShortCallerEncoder,
		},
		OutputPaths:      []string{"stdout"},
		ErrorOutputPaths: []string{"stderr"},
	}

	Logger = buildLogger(config)
}

func buildLogger(config zap.Config) *zap.Logger {
	logger, err := config.Build()
	if err != nil {
		// Emit a one-off JSON record on stderr and exit
		fallback := map[string]any{
			"time":     time.Now().Format(time.RFC3339Nano),
			"severity": zapcore.FatalLevel.String(), // "fatal"
			"logger":   "",                          // no name yet
			"message":  fmt.Sprintf("failed to initialize logger: %v", err),
		}
		b, _ := json.Marshal(fallback)
		// ensure it ends with a newline, like zap does
		os.Stderr.Write(append(b, '\n'))
		exitFunc(1)
	}

	return logger
}

// sourceLocationField returns a zap.Field for Cloud Logging's source location.
// The 'skip' parameter allows you to control how many stack frames to skip.
func sourceLocationField(skip int) zap.Field {
	pc, file, line, ok := runtime.Caller(skip)
	funcName := "unknown"
	if ok {
		if fn := runtime.FuncForPC(pc); fn != nil {
			funcName = fn.Name()
		}
	}
	return zap.Any("logging.googleapis.com/sourceLocation", map[string]any{
		"file":     file,
		"line":     line,
		"function": funcName,
	})
}

func getLogLevel() zapcore.Level {
	// Default to INFO level.
	logLevel := zapcore.InfoLevel
	if lvl, ok := os.LookupEnv("LOG_LEVEL"); ok {
		if err := logLevel.Set(lvl); err != nil {
			logLevel = zapcore.InfoLevel // fallback to Info on error
		}
	}
	return logLevel
}

// The following functions create formatted log messages and include the
// source location field automatically. The zap.AddCallerSkip(1) ensures that
// the wrapper function is skipped, and we pass an additional skip value to our
// sourceLocationField to point to the actual caller.

// Debug logs a debug message.
func Debug(msg any, fields ...zap.Field) {
	loc := sourceLocationField(2)
	switch v := msg.(type) {
	case error:
		Logger.Debug(v.Error(), append([]zap.Field{loc, zap.Error(v)}, fields...)...)
	case string:
		Logger.Debug(v, append([]zap.Field{loc}, fields...)...)
	default:
		Logger.Debug(fmt.Sprint(v), append([]zap.Field{loc}, fields...)...)
	}
}

// Info logs an informational message.
func Info(msg any, fields ...zap.Field) {
	loc := sourceLocationField(2)
	switch v := msg.(type) {
	case error:
		Logger.Info(v.Error(), append([]zap.Field{loc, zap.Error(v)}, fields...)...)
	case string:
		Logger.Info(v, append([]zap.Field{loc}, fields...)...)
	default:
		Logger.Info(fmt.Sprint(v), append([]zap.Field{loc}, fields...)...)
	}
}

// Warning logs a warning message.
func Warn(msg any, fields ...zap.Field) {
	loc := sourceLocationField(2)
	switch v := msg.(type) {
	case error:
		Logger.Warn(v.Error(), append([]zap.Field{loc, zap.Error(v)}, fields...)...)
	case string:
		Logger.Warn(v, append([]zap.Field{loc}, fields...)...)
	default:
		Logger.Warn(fmt.Sprint(v), append([]zap.Field{loc}, fields...)...)
	}
}

// Error logs an error message.
func Error(msg any, fields ...zap.Field) {
	loc := sourceLocationField(2)
	stack := string(debug.Stack())
	switch v := msg.(type) {
	case error:
		msg := fmt.Sprintf("%s\nStack trace:\n%s", v.Error(), stack)
		Logger.Error(msg, append([]zap.Field{loc, zap.Error(v)}, fields...)...)
	case string:
		msg := fmt.Sprintf("%s\nStack trace:\n%s", v, stack)
		Logger.Error(msg, append([]zap.Field{loc}, fields...)...)
	default:
		msg := fmt.Sprintf("%v\nStack trace:\n%s", v, stack)
		Logger.Error(msg, append([]zap.Field{loc}, fields...)...)
	}
}

// Fatal logs a fatal error message and exits the application.
func Fatal(msg any, fields ...zap.Field) {
	loc := sourceLocationField(2)
	stack := string(debug.Stack())
	var allFields []zap.Field
	var msgStr string
	switch v := msg.(type) {
	case error:
		msgStr = fmt.Sprintf("%s\nStack trace:\n%s", v.Error(), stack)
		allFields = append([]zap.Field{loc, zap.Error(v)}, fields...)
	case string:
		msgStr = fmt.Sprintf("%s\nStack trace:\n%s", v, stack)
		allFields = append([]zap.Field{loc}, fields...)
	default:
		msgStr = fmt.Sprintf("%v\nStack trace:\n%s", v, stack)
		allFields = append([]zap.Field{loc}, fields...)
	}

	// write a Fatal-level entry, but don’t call os.Exit for unit testing
	entry := zapcore.Entry{Level: zapcore.FatalLevel, Message: msgStr, Time: time.Now()}
	Logger.Core().With(allFields).Write(entry, nil)
	exitFunc(1)
}

// Format logging
// Accepts formatted string and arguments
func Debugf(format string, args ...any) {
	msg := fmt.Sprintf(format, args...)
	Logger.Debug(msg, sourceLocationField(2))
}

// Info logs an informational message using a format string.
func Infof(format string, args ...any) {
	msg := fmt.Sprintf(format, args...)
	Logger.Info(msg, sourceLocationField(2))
}

// Warning logs a warning message using a format string.
func Warnf(format string, args ...any) {
	msg := fmt.Sprintf(format, args...)
	Logger.Warn(msg, sourceLocationField(2))
}

// Error logs an error message using a format string.
func Errorf(format string, args ...any) {
	msg := fmt.Sprintf(format, args...)
	msg = fmt.Sprintf("%s\nStack trace:\n%s", msg, string(debug.Stack()))
	Logger.Error(msg, sourceLocationField(2))
}

// Fatal logs a fatal error message using a format string and exits the application.
func Fatalf(format string, args ...any) {
	msg := fmt.Sprintf(format, args...)
	msg = fmt.Sprintf("%s\nStack trace:\n%s", msg, string(debug.Stack()))

	// write a Fatal-level entry, but don’t call os.Exit for unit testing
	entry := zapcore.Entry{Level: zapcore.FatalLevel, Message: msg, Time: time.Now()}
	Logger.Core().With([]zapcore.Field{sourceLocationField(2)}).Write(entry, nil)
	exitFunc(1)
}

# mocks

---

## Overview

The `mocks` package provides in-memory and error-injection fakes for unit testing database executors, schema migration executors, Pub/Sub, Redis, and includes unexported structs for testing private-field mapping.

---

## Package Layout

```
shared/
├── mocks/
│   ├── dbExecutor.go
│   ├── errors.go
│   ├── mock.go
│   ├── schemaMigrationExecutor.go
│   └── unexported/
│       └── schemas.go
```

---

## Features

- **FakeDBExecutor**: configurable call limits and override hooks for `connect.DatabaseExecutor`  
- **FakeSchemaMigrationExecutor**: similar behavior for `schema_mgmt.SchemaMigrationExecutor`  
- **Pub/Sub Mocks**: `FakePubsubClient`, `FakePubsubtopic`, `FakePubsubSubscription`  
- **Redis Mock**: via `redismock.NewClientMock()`  
- **FakeConns()**: returns pre-wired `*connect.Connections`  
- **generateJwtKeys()**: base64 RSA key generation for JWT tests  
- **Unexported Field Testing**: `unexported.Hidden` struct  

---

## Usage

```go
conns := mocks.FakeConns()
// Customize DB behavior
fakeDB := conns.Postgres.(*mocks.FakeDBExecutor)
fakeDB.QueryGenericFunc = func(query string, args ...interface{}) ([]map[string]interface{}, error) {
    return []map[string]interface{}{{"id": 42}}, nil
}
result, err := MyServiceFetch(ctx, conns)
// Test unexported mapping
hidden := unexported.Hidden{secret: "value"}
```

---

## API Reference

### Functions

```go
func FakeConns() *connect.Connections
func generateJwtKeys() (privateBase64, publicBase64 string, err error)
```

### Types

```go
type FakeDBExecutor
type FakeSchemaMigrationExecutor
type FakePubsubClient
type FakePubsubtopic
type FakePubsubSubscription
type unexported.Hidden
```

### Errors

```go
var ErrFakeDBExecutorExecCallLimit error
var ErrFakeDBExecutorQueryGenericCallLimit error
var ErrFakeSchemaMigrationExecutorEnsureSchemaMigrationsTableCallLimit error
``` 

---

## Internals

- **Call-Limit Logic**: `EnableFailAfter`, `*CallFailAfter` fields  
- **Override Hooks**: `*Func` attributes for custom behavior  
- **Message Buffering**: in-memory storage for Pub/Sub mocks  
- **Schema Parsing**: support for MD5 validation in migration executor mocks  

---

## Example

```go
func TestSomething(t *testing.T) {
    conns := mocks.FakeConns()
    // ...
}
```

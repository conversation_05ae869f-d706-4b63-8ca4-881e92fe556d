package integration

// TODO: Need to figure out why we cant set a empty array in an array
import (
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"synapse-its.com/shared/bqbatch"
	"synapse-its.com/shared/schemas"
)

func TestBigQueryBatchInsert_Schemas(t *testing.T) {
	// assert := assert.New(t)
	bq, err := awaitBigQuerySetup()
	require.NoError(t, err, "BigQuery client setup")
	batch := bqbatch.NewDefault(bq, nil)

	// 	// common metadata
	now := time.Now().UTC()
	orgID := "ORG1"
	sgw := "GW1"
	// tz := "UTC"
	topic := "topic1"
	pubsubID := "PS1"
	// deviceID := "DEV1"
	header := schemas.HeaderRecord{
		MonitorId:        1,
		Model:            2,
		FirmwareVersion:  3,
		FirmwareRevision: 4,
		CommVersion:      5,
		Volt220:          true,
		VoltDC:           false,
		MainsDC:          true,
		PowerDownLevel:   10,
		BlackoutLevel:    20,
		MaxChannels:      16,
	}

	// 2) EtlRawMessages
	erm := &schemas.EdiRawMessages{
		Topic:                     topic,
		OrganizationIdentifier:    orgID,
		SoftwareGatewayIdentifier: sgw,
		MessageType:               "test type",
		MessageVersion:            "v1",
		PubsubID:                  pubsubID,
		PubsubTimestamp:           now,
		Data:                      []byte("raw message data"),
		Attributes:                []schemas.KV{{Key: "test key", Value: "test value"}},
		OrderingKey:               "test ordering key",
		DeliveryAttempt:           -1,
	}
	require.NoError(t, batch.Add(erm))

	// 	// 3) LogMonitorReset
	// 	lmr := &schemas.LogMonitorReset{
	// 		OrganizationIdentifier: orgID,
	// 		SoftwareGatewayID:      sgw,
	// 		TZ:                     tz,
	// 		Topic:                  topic,
	// 		PubsubTimestamp:        now,
	// 		PubsubID:               pubsubID,
	// 		DeviceID:               deviceID,
	// 		Header:                 header,
	// 		Records: []schemas.LogMonitorResetRecord{
	// 			{EventTimestamp: now.Add(-time.Hour), ResetType: "manual"},
	// 		},
	// 		DeviceModel: "MODEL-X",
	// 		RawMessage:  []byte{0x01, 0x02},
	// 		LogUUID:     "UUID-1234",
	// 	}
	// 	require.NoError(t, batch.Add(lmr))

	// 	// 4) LogPreviousFail
	// 	lpf := &schemas.LogPreviousFail{
	// 		OrganizationIdentifier: orgID,
	// 		SoftwareGatewayID:      sgw,
	// 		TZ:                     tz,
	// 		Topic:                  topic,
	// 		PubsubTimestamp:        now,
	// 		PubsubID:               pubsubID,
	// 		DeviceID:               deviceID,
	// 		Header:                 header,
	// 		Records: []schemas.LogPreviousFailRecord{
	// 			{
	// 				DateTime:                          now.Add(-2 * time.Hour),
	// 				Fault:                             "fault-A",
	// 				ACLine:                            "120V",
	// 				T48VDCSignalBus:                   "OK",
	// 				RedEnable:                         "ENABLED",
	// 				MCCoilEE:                          "OFF",
	// 				SpecialFunction1:                  "SF1",
	// 				SpecialFunction2:                  "SF2",
	// 				WDTMonitor:                        "WDT",
	// 				T24VDCInput:                       "24V",
	// 				Temperature:                       75,
	// 				LsFlashBit:                        true,
	// 				FaultStatus:                       []bool{true, false},
	// 				ChannelGreenStatus:                []bool{true, true, false},
	// 				ChannelYellowStatus:               []bool{false, true},
	// 				ChannelRedStatus:                  []bool{true},
	// 				ChannelWalkStatus:                 []bool{},
	// 				ChannelGreenFieldCheckStatus:      []bool{false},
	// 				ChannelYellowFieldCheckStatus:     []bool{true},
	// 				ChannelRedFieldCheckStatus:        []bool{true, false},
	// 				ChannelWalkFieldCheckStatus:       []bool{},
	// 				ChannelGreenRecurrentPulseStatus:  []bool{false},
	// 				ChannelYellowRecurrentPulseStatus: []bool{true},
	// 				ChannelRedRecurrentPulseStatus:    []bool{true, true},
	// 				ChannelWalkRecurrentPulseStatus:   []bool{},
	// 				ChannelGreenRmsVoltage:            []int64{100, 101},
	// 				ChannelYellowRmsVoltage:           []int64{200},
	// 				ChannelRedRmsVoltage:              []int64{300, 301, 302},
	// 				ChannelWalkRmsVoltage:             []int64{},
	// 				NextConflictingChannels:           []bool{false},
	// 			},
	// 		},
	// 		DeviceModel: "MODEL-X",
	// 		RawMessage:  []byte{0x0A, 0x0B},
	// 		LogUUID:     "UUID-5678",
	// 	}
	// 	require.NoError(t, batch.Add(lpf))

	// 	// 5) LogACLineEvent
	// 	lac := &schemas.LogACLineEvent{
	// 		OrganizationIdentifier: orgID,
	// 		SoftwareGatewayID:      sgw,
	// 		TZ:                     tz,
	// 		Topic:                  topic,
	// 		PubsubTimestamp:        now,
	// 		PubsubID:               pubsubID,
	// 		DeviceID:               deviceID,
	// 		Header:                 header,
	// 		Record: []schemas.LogACLineEventRecord{
	// 			{EventType: "AC ON", DateTime: now.Add(-3 * time.Hour), LineVoltageRms: 120, LineFrequencyHz: 60},
	// 		},
	// 		DeviceModel: "MODEL-X",
	// 		RawMessage:  []byte{0x0C},
	// 		LogUUID:     "UUID-91011",
	// 		VoltageType: 1,
	// 	}
	// 	require.NoError(t, batch.Add(lac))

	// 	// 6) LogFaultSignalSequence
	// 	lfs := &schemas.LogFaultSignalSequence{
	// 		OrganizationIdentifier: orgID,
	// 		SoftwareGatewayID:      sgw,
	// 		TZ:                     tz,
	// 		Topic:                  topic,
	// 		PubsubTimestamp:        now,
	// 		PubsubID:               pubsubID,
	// 		DeviceID:               deviceID,
	// 		Header:                 header,
	// 		RawMessage:             []byte{0x0D},
	// 		FaultType:              "F-TYPE",
	// 		Records: []schemas.TraceBuffer{
	// 			{
	// 				BufferRawBytes: []byte{0xFF},
	// 				Timestamp:      123,
	// 				Reds:           []bool{true, false},
	// 				Yellows:        []bool{false},
	// 				Greens:         []bool{true},
	// 				Walks:          []bool{},
	// 				EE_SF_RE:       false,
	// 				AcVoltage:      121,
	// 			},
	// 		},
	// 		DeviceModel: "MODEL-X",
	// 		LogUUID:     "UUID-121314",
	// 	}
	// 	require.NoError(t, batch.Add(lfs))

	// 7) LogConfiguration
	record := schemas.ConfigurationChangeLogRecord{
		DateTime:        time.Date(2025, 5, 1, 10, 4, 0, 0, time.UTC),
		Ch01Permissives: []string{"P"},
		Ch02Permissives: []string{"P"},
		Ch03Permissives: []string{"P"},
		Ch04Permissives: []string{"P"},
		Ch05Permissives: []string{"P"},
		Ch06Permissives: []string{"P"},
		Ch07Permissives: []string{"P"},
		Ch08Permissives: []string{"P"},
		Ch09Permissives: []string{"P"},
		Ch10Permissives: []string{"P"},
		Ch11Permissives: []string{"P"},
		Ch12Permissives: []string{"P"},
		Ch13Permissives: []string{"P"},
		Ch14Permissives: []string{"P"},
		Ch15Permissives: []string{"P"},
		Ch16Permissives: []string{"P"},
		Ch17Permissives: []string{"P"},
		Ch18Permissives: []string{"P"},
		Ch19Permissives: []string{"P"},
		Ch20Permissives: []string{"P"},
		Ch21Permissives: []string{"P"},
		Ch22Permissives: []string{"P"},
		Ch23Permissives: []string{"P"},
		Ch24Permissives: []string{"P"},
		Ch25Permissives: []string{"P"},
		Ch26Permissives: []string{"P"},
		Ch27Permissives: []string{"P"},
		Ch28Permissives: []string{"P"},
		Ch29Permissives: []string{"P"},
		Ch30Permissives: []string{"P"},
		Ch31Permissives: []string{"P"},

		RedFailEnable:                   []bool{true},
		GreenYellowDualEnable:           []bool{false},
		YellowRedDualEnable:             []bool{true},
		GreenRedDualEnable:              []bool{false},
		MinimumYellowClearanceEnable:    []bool{true},
		MinimumYellowRedClearanceEnable: []bool{false},
		FieldCheckEnableGreen:           []bool{true},
		FieldCheckEnableYellow:          []bool{false},
		FieldCheckEnableRed:             []bool{true},
		YellowEnable:                    []bool{false},

		WalkEnableTs1:             true,
		RedFaultTiming:            "Fast",
		RecurrentPulse:            false,
		WatchdogTiming:            "Medium",
		WatchdogEnableSwitch:      true,
		ProgramCardMemory:         false,
		GYEnable:                  true,
		MinimumFlashTime:          "Short",
		CvmLatchEnable:            false,
		LogCvmFaults:              true,
		X24VIiInputThreshold:      "ThresholdX",
		X24VLatchEnable:           true,
		X24VoltInhibit:            false,
		Port1Disable:              true,
		TypeMode:                  "Mode1",
		LEDGuardThresholds:        true,
		ForceType16Mode:           false,
		Type12WithSdlcMode:        true,
		VmCvm24V3XDayLatch:        false,
		RedFailEnabledBySSM:       true,
		DualIndicationFaultTiming: "DualX",
		WDTErrorClearOnPU:         false,
		MinimumFlash:              true,
		ConfigChangeFault:         false,
		RedCableFault:             true,
		AcLineBrownout:            "Brownout1",
		PinEEPolarity:             "Polarity1",

		FlashingYellowArrows:   []string{"Arrow1"},
		FyaRedAndYellowEnable:  "EnableY",
		FyaRedAndGreenDisable:  "DisableG",
		FyaYellowTrapDetection: true,
		FYAFlashRateFault:      false,
		FyaFlashRateDetection:  true,
		Pplt5Suppression:       "Suppress1",
		CheckValue:             "Check1",
		ChangeSource:           "Change1",

		RedVirtualChannel: []schemas.VirtualSetting{
			{Color: "Red", Enabled: true, SourceChannel: 1, SourceColor: "Blue"},
		},
		YellowVirtualChannel: []schemas.VirtualSetting{
			{Color: "Yellow", Enabled: false, SourceChannel: 2, SourceColor: "Green"},
		},
		GreenVirtualChannel: []schemas.VirtualSetting{
			{Color: "Green", Enabled: true, SourceChannel: 3, SourceColor: "Red"},
		},

		CurrentSenseRedEnabled:      []bool{true},
		CurrentSenseYellowEnabled:   []bool{false},
		CurrentSenseGreenEnabled:    []bool{true},
		CurrentSenseRedThreshold:    []int{10},
		CurrentSenseYellowThreshold: []int{20},
		CurrentSenseGreenThreshold:  []int{30},

		DarkChannelX01: []bool{true},
		DarkChannelX02: []bool{false},
		DarkChannelX03: []bool{true},
		DarkChannelX04: []bool{false},
	}

	// 3) Assemble the top‐level LogConfiguration
	cfg := &schemas.LogConfiguration{
		OrganizationIdentifier: "DEV5AC4410CB8F3C8E29CE9210E5913B7B3B872B2BC",
		SoftwareGatewayID:      "d5c29fc65d7dc9e0798f68e4f4b982853587acfb5686e981c517849973198e9f",
		TZ:                     "UTC",
		Topic:                  "topic1",
		PubsubTimestamp:        time.Date(2025, 5, 1, 10, 0, 0, 0, time.UTC),
		PubsubID:               "PS1",
		DeviceID:               "3fbe60189eef4bb6ab9c03663cc7b3ba",
		Header:                 &header,
		DeviceModel:            "ModelX",
		Record:                 []schemas.ConfigurationChangeLogRecord{record},
		RawMessage:             []byte("config1_raw"),
		LogUUID:                "event1-uuid",
	}

	require.NoError(t, batch.Add(cfg))

	// 	// 8) DlqMessages
	// 	dlq := &schemas.DlqMessages{
	// 		OriginalTopic:   topic,
	// 		ID:              "DLQ-1",
	// 		Data:            "bad",
	// 		Attributes:      "none",
	// 		PublishTime:     now,
	// 		DeliveryAttempt: "1",
	// 		OrderingKey:     "ok",
	// 		PubsubTimestamp: now,
	// 		PubsubID:        pubsubID,
	// 	}
	// 	require.NoError(t, batch.Add(dlq))

	// 	// 9) RmsEngine
	// 	engine := &schemas.RmsEngine{
	// 		OrganizationIdentifier: orgID,
	// 		SoftwareGatewayID:      sgw,
	// 		TZ:                     tz,
	// 		Topic:                  topic,
	// 		PubsubTimestamp:        now,
	// 		PubsubID:               pubsubID,
	// 		DeviceID:               deviceID,
	// 		Header:                 header,
	// 		RmsVersion:             100,
	// 		RmsRevision:            200,
	// 		RawMessage:             []byte{0x0F},
	// 	}
	// 	require.NoError(t, batch.Add(engine))

	// 	// 10) MacAddress
	// 	mac := &schemas.MacAddress{
	// 		OrganizationIdentifier: orgID,
	// 		SoftwareGatewayID:      sgw,
	// 		TZ:                     tz,
	// 		Topic:                  topic,
	// 		PubsubTimestamp:        now,
	// 		PubsubID:               pubsubID,
	// 		DeviceID:               deviceID,
	// 		MacAddress:             "AA:BB:CC:DD:EE:FF",
	// 	}
	// 	require.NoError(t, batch.Add(mac))

	// 	// 11) RmsData
	// 	rms := &schemas.RmsData{
	// 		OrganizationIdentifier: orgID,
	// 		SoftwareGatewayID:      sgw,
	// 		TZ:                     tz,
	// 		Topic:                  topic,
	// 		PubsubTimestamp:        now,
	// 		PubsubID:               pubsubID,
	// 		DeviceID:               deviceID,
	// 		Header:                 header,
	// 		IsFaulted:              true,
	// 		Fault:                  "F1",
	// 		FaultStatus:            "OK",
	// 		MonitorTime:            now.Add(-5 * time.Minute),
	// 		TemperatureF:           85,
	// 		ChannelGreenStatus: schemas.ChannelStatusStruct{
	// 			Channel01: bigquery.NullBool{Bool: true, Valid: true},
	// 		},
	// 		ChannelYellowStatus: schemas.ChannelStatusStruct{},
	// 		ChannelRedStatus:    schemas.ChannelStatusStruct{},
	// 		ChannelGreenVoltage: schemas.ChannelVoltageStruct{
	// 			Channel01: bigquery.NullInt64{Int64: 123, Valid: true},
	// 		},
	// 		ChannelYellowVoltage: schemas.ChannelVoltageStruct{},
	// 		ChannelRedVoltage:    schemas.ChannelVoltageStruct{},
	// 		RawMessage:           []byte{0x10},
	// 	}
	// 	require.NoError(t, batch.Add(rms))

	// 	// 12) FaultNotification
	// 	notify := &schemas.FaultNotification{
	// 		OrganizationIdentifier: orgID,
	// 		SoftwareGatewayID:      sgw,
	// 		TZ:                     tz,
	// 		Topic:                  topic,
	// 		PubsubTimestamp:        now,
	// 		PubsubID:               pubsubID,
	// 		DeviceID:               deviceID,
	// 		Header:                 header,
	// 		IsFaulted:              false,
	// 		Fault:                  "",
	// 		FaultStatus:            "",
	// 		MonitorTime:            now,
	// 		TemperatureF:           0,
	// 		ChannelGreenStatus:     schemas.ChannelStatusStruct{},
	// 		ChannelYellowStatus:    schemas.ChannelStatusStruct{},
	// 		ChannelRedStatus:       schemas.ChannelStatusStruct{},
	// 		ChannelGreenVoltage:    schemas.ChannelVoltageStruct{},
	// 		ChannelYellowVoltage:   schemas.ChannelVoltageStruct{},
	// 		ChannelRedVoltage:      schemas.ChannelVoltageStruct{},
	// 		RawMessage:             []byte{0x11},
	// 	}
	// 	require.NoError(t, batch.Add(notify))

	// 	// 13) GatewayPerformanceStatistics
	// 	gps := &schemas.GatewayPerformanceStatistics{
	// 		OrganizationIdentifier:  orgID,
	// 		SoftwareGatewayID:       sgw,
	// 		TZ:                      tz,
	// 		Topic:                   topic,
	// 		PubsubTimestamp:         now,
	// 		PubsubID:                pubsubID,
	// 		MessageTime:             now,
	// 		SoftwareGatewayDeviceID: "SGWDEV1",
	// 		Count:                   5,
	// 		LastExecutedTime:        now.Add(-time.Minute),
	// 		LastExecutedElapsedTime: 500,
	// 		TotalTime:               2000,
	// 		MinTime:                 100,
	// 		MaxTime:                 800,
	// 		ErrorCount:              1,
	// 		RawMessage:              []byte{0x12},
	// 	}
	// 	require.NoError(t, batch.Add(gps))

	// 	// 14) FaultLogs
	// 	fl := &schemas.FaultLogs{
	// 		OrganizationIdentifier: orgID,
	// 		SoftwareGatewayID:      sgw,
	// 		TZ:                     tz,
	// 		Topic:                  topic,
	// 		PubsubTimestamp:        now,
	// 		PubsubID:               pubsubID,
	// 		DeviceID:               deviceID,
	// 		RawLogMessages: schemas.RawLogMessages{
	// 			LogMonitorReset:        [][]byte{[]byte("A")},
	// 			LogPreviousFail:        [][]byte{[]byte("B")},
	// 			LogACLineEvent:         [][]byte{[]byte("C")},
	// 			LogFaultSignalSequence: [][]byte{[]byte("D")},
	// 			LogConfiguration:       [][]byte{[]byte("E")},
	// 		},
	// 		LogUUID: "UUID-181920",
	// 	}
	// 	require.NoError(t, batch.Add(fl))

	// 	// 15) flush
	require.NoError(t, batch.Shutdown(), "batch.Shutdown")
	time.Sleep(time.Second)
	query := fmt.Sprintf(`
		SELECT *
		FROM {{%s}}
		`, schemas.T_logConfiguration)
	rows := []schemas.LogConfiguration{}
	_ = bq.QueryGenericSlice(&rows, query)
	// logger.Errorf("Emulator is stupid: %v", rows)
	// assert.NoError(err, "Error querying BigQuery")
}

import { Component, OnInit } from '@angular/core';
import { AuthService, UserProfile } from '../services/auth.service';

@Component({
  selector: 'app-header',
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.css'],
  standalone: false
})
export class HeaderComponent implements OnInit {
  userName: string | null = null;

  constructor(private auth: AuthService) { }

  ngOnInit() {
    this.auth.getProfile().subscribe({
      next: (profile: UserProfile) => {
        this.userName = profile['name'] ?? profile.email;
      },
      error: () => {
        this.userName = null;
      }
    });
  }

  login() {
    this.auth.login();
  }

  logout() {
    this.auth.logout();
  }
}

package authenticate

import (
	"context"
	"database/sql"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	jwttokens "synapse-its.com/shared/api/jwttokens"
	connect "synapse-its.com/shared/connect"
)

// Test_NewTokenHandler tests the NewTokenHandler function
func Test_NewTokenHandler(t *testing.T) {
	t.Parallel()
	// Create a new token handler with default dependencies
	handler := NewTokenHandler()

	// Verify that the handler is properly initialized with default dependencies
	assert.NotNil(t, handler, "Handler should not be nil")

	// Check that all dependencies are set
	assert.NotNil(t, handler.DBProvider, "DBProvider should be set")
	assert.NotNil(t, handler.HasTokenExpired, "HasTokenExpired should be set")
	assert.NotNil(t, handler.JwtCreator, "JwtCreator should be set")
	assert.NotNil(t, handler.TokenPers<PERSON>, "TokenPersister should be set")
	assert.NotNil(t, handler.ValidateRoleFunc, "ValidateRoleFunc should be set")
}

// Test_TokenHandler_TokenAccess tests the TokenAccess method of the TokenHandler struct
func Test_TokenHandler_TokenAccess(t *testing.T) {
	t.Parallel()
	// Define test cases using table-driven test pattern
	tests := []struct {
		name               string                                     // Test case name
		token              string                                     // Input token
		setupMocks         func(*TokenHandler, *MockDatabaseExecutor) // Mock setup function
		expectedStatusCode int                                        // Expected HTTP status code
		wantErr            bool                                       // Whether an error is expected
	}{
		{
			name:  "successful_token_access",
			token: "valid-token",
			setupMocks: func(h *TokenHandler, db *MockDatabaseExecutor) {
				// Setup connection provider
				h.DBProvider = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return &connect.Connections{Postgres: db}, nil
				}

				// Mock QueryRowStruct to return a valid user
				db.On("QueryRowStruct", mock.AnythingOfType("*authenticate.dbUser"), mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					user := args.Get(0).(*dbUser)
					user.Id = 123
					user.UserName = "testuser"
					user.OrganizationIdentifier = "org1"
					user.RoleId = 1
					user.APIKey = "apikey123"
					user.WebEnabled = 1
					user.WebTokenDurationSeconds = 3600
					user.ExpirationUTC = time.Now().Add(1 * time.Hour).Format(time.DateTime)
				}).Return(nil)

				// Mock Exec for token deletion
				mockResult := new(MockSQLResult)
				mockResult.On("LastInsertId").Return(int64(1), nil)
				mockResult.On("RowsAffected").Return(int64(1), nil)
				db.On("Exec", mock.Anything, mock.Anything).Return(mockResult, nil)

				// Mock HasTokenExpired
				h.HasTokenExpired = func(expirationTimeStr string) (bool, error) {
					return false, nil
				}

				// Mock ValidateRoleFunc
				h.ValidateRoleFunc = func(roleID int64) (string, error) {
					return "user", nil
				}

				// Mock JwtCreator
				h.JwtCreator = func(username string, duration time.Duration, role int64, userPermissions jwttokens.UserPermissions) (string, time.Time, error) {
					return "jwt-token", time.Now().Add(duration), nil
				}

				// Mock TokenPersister
				h.TokenPersister = func(pg connect.DatabaseExecutor, userID int64, jwt string, expiresAt time.Time) error {
					return nil
				}
			},
			expectedStatusCode: http.StatusOK,
			wantErr:            false,
		},
		{
			name:  "db_connection_error",
			token: "valid-token",
			setupMocks: func(h *TokenHandler, db *MockDatabaseExecutor) {
				// Setup connection provider to return error
				h.DBProvider = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return nil, errors.New("connection error")
				}
			},
			expectedStatusCode: http.StatusInternalServerError,
			wantErr:            true,
		},
		{
			name:  "token_not_found",
			token: "invalid-token",
			setupMocks: func(h *TokenHandler, db *MockDatabaseExecutor) {
				// Setup connection provider
				h.DBProvider = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return &connect.Connections{Postgres: db}, nil
				}

				// Mock QueryRowStruct to return no rows
				db.On("QueryRowStruct", mock.AnythingOfType("*authenticate.dbUser"), mock.Anything, mock.Anything).Return(sql.ErrNoRows)
			},
			expectedStatusCode: http.StatusUnauthorized,
			wantErr:            true,
		},
		{
			name:  "db_query_error",
			token: "valid-token",
			setupMocks: func(h *TokenHandler, db *MockDatabaseExecutor) {
				// Setup connection provider
				h.DBProvider = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return &connect.Connections{Postgres: db}, nil
				}

				// Mock QueryRowStruct to return a database error
				db.On("QueryRowStruct", mock.AnythingOfType("*authenticate.dbUser"), mock.Anything, mock.Anything).Return(errors.New("database error"))
			},
			expectedStatusCode: http.StatusUnauthorized,
			wantErr:            true,
		},
		{
			name:  "token_deletion_error",
			token: "valid-token",
			setupMocks: func(h *TokenHandler, db *MockDatabaseExecutor) {
				// Setup connection provider
				h.DBProvider = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return &connect.Connections{Postgres: db}, nil
				}

				// Mock QueryRowStruct to return a valid user
				db.On("QueryRowStruct", mock.AnythingOfType("*authenticate.dbUser"), mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					user := args.Get(0).(*dbUser)
					user.Id = 123
					user.UserName = "testuser"
					user.OrganizationIdentifier = "org1"
					user.RoleId = 1
					user.APIKey = "apikey123"
					user.WebEnabled = 1
					user.WebTokenDurationSeconds = 3600
					user.ExpirationUTC = time.Now().Add(1 * time.Hour).Format(time.DateTime)
				}).Return(nil)

				// Mock Exec for token deletion to return error
				mockResult := new(MockSQLResult)
				mockResult.On("LastInsertId").Return(int64(1), nil)
				mockResult.On("RowsAffected").Return(int64(1), nil)
				db.On("Exec", mock.Anything, mock.Anything).Return(mockResult, errors.New("deletion error"))
			},
			expectedStatusCode: http.StatusUnauthorized,
			wantErr:            true,
		},
		{
			name:  "token_expired",
			token: "expired-token",
			setupMocks: func(h *TokenHandler, db *MockDatabaseExecutor) {
				// Setup connection provider
				h.DBProvider = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return &connect.Connections{Postgres: db}, nil
				}

				// Mock QueryRowStruct to return a valid user
				db.On("QueryRowStruct", mock.AnythingOfType("*authenticate.dbUser"), mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					user := args.Get(0).(*dbUser)
					user.Id = 123
					user.UserName = "testuser"
					user.OrganizationIdentifier = "org1"
					user.RoleId = 1
					user.APIKey = "apikey123"
					user.WebEnabled = 1
					user.WebTokenDurationSeconds = 3600
					user.ExpirationUTC = time.Now().Add(-1 * time.Hour).Format(time.DateTime)
				}).Return(nil)

				// Mock Exec for token deletion
				mockResult := new(MockSQLResult)
				mockResult.On("LastInsertId").Return(int64(1), nil)
				mockResult.On("RowsAffected").Return(int64(1), nil)
				db.On("Exec", mock.Anything, mock.Anything).Return(mockResult, nil)

				// Mock HasTokenExpired to return true (expired)
				h.HasTokenExpired = func(expirationTimeStr string) (bool, error) {
					return true, nil
				}
			},
			expectedStatusCode: http.StatusUnauthorized,
			wantErr:            true,
		},
		{
			name:  "token_expiry_check_error",
			token: "valid-token",
			setupMocks: func(h *TokenHandler, db *MockDatabaseExecutor) {
				// Setup connection provider
				h.DBProvider = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return &connect.Connections{Postgres: db}, nil
				}

				// Mock QueryRowStruct to return a valid user
				db.On("QueryRowStruct", mock.AnythingOfType("*authenticate.dbUser"), mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					user := args.Get(0).(*dbUser)
					user.Id = 123
					user.UserName = "testuser"
					user.OrganizationIdentifier = "org1"
					user.RoleId = 1
					user.APIKey = "apikey123"
					user.WebEnabled = 1
					user.WebTokenDurationSeconds = 3600
					user.ExpirationUTC = "invalid-date-format"
				}).Return(nil)

				// Mock Exec for token deletion
				mockResult := new(MockSQLResult)
				mockResult.On("LastInsertId").Return(int64(1), nil)
				mockResult.On("RowsAffected").Return(int64(1), nil)
				db.On("Exec", mock.Anything, mock.Anything).Return(mockResult, nil)

				// Mock HasTokenExpired to return error
				h.HasTokenExpired = func(expirationTimeStr string) (bool, error) {
					return false, errors.New("invalid date format")
				}
			},
			expectedStatusCode: http.StatusUnauthorized,
			wantErr:            true,
		},
		{
			name:  "role_validation_error",
			token: "valid-token",
			setupMocks: func(h *TokenHandler, db *MockDatabaseExecutor) {
				// Setup connection provider
				h.DBProvider = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return &connect.Connections{Postgres: db}, nil
				}

				// Mock QueryRowStruct to return a valid user
				db.On("QueryRowStruct", mock.AnythingOfType("*authenticate.dbUser"), mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					user := args.Get(0).(*dbUser)
					user.Id = 123
					user.UserName = "testuser"
					user.OrganizationIdentifier = "org1"
					user.RoleId = 999 // Invalid role ID
					user.APIKey = "apikey123"
					user.WebEnabled = 1
					user.WebTokenDurationSeconds = 3600
					user.ExpirationUTC = time.Now().Add(1 * time.Hour).Format(time.DateTime)
				}).Return(nil)

				// Mock Exec for token deletion
				mockResult := new(MockSQLResult)
				mockResult.On("LastInsertId").Return(int64(1), nil)
				mockResult.On("RowsAffected").Return(int64(1), nil)
				db.On("Exec", mock.Anything, mock.Anything).Return(mockResult, nil)

				// Mock HasTokenExpired
				h.HasTokenExpired = func(expirationTimeStr string) (bool, error) {
					return false, nil
				}

				// Mock ValidateRoleFunc to return error
				h.ValidateRoleFunc = func(roleID int64) (string, error) {
					return "", errors.New("invalid role ID")
				}
			},
			expectedStatusCode: http.StatusUnauthorized,
			wantErr:            true,
		},
		{
			name:  "web_access_not_enabled",
			token: "valid-token",
			setupMocks: func(h *TokenHandler, db *MockDatabaseExecutor) {
				// Setup connection provider
				h.DBProvider = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return &connect.Connections{Postgres: db}, nil
				}

				// Mock QueryRowStruct to return a valid user with web access disabled
				db.On("QueryRowStruct", mock.AnythingOfType("*authenticate.dbUser"), mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					user := args.Get(0).(*dbUser)
					user.Id = 123
					user.UserName = "testuser"
					user.OrganizationIdentifier = "org1"
					user.RoleId = 1
					user.APIKey = "apikey123"
					user.WebEnabled = 0 // Web access disabled
					user.WebTokenDurationSeconds = 3600
					user.ExpirationUTC = time.Now().Add(1 * time.Hour).Format(time.DateTime)
				}).Return(nil)

				// Mock Exec for token deletion
				mockResult := new(MockSQLResult)
				mockResult.On("LastInsertId").Return(int64(1), nil)
				mockResult.On("RowsAffected").Return(int64(1), nil)
				db.On("Exec", mock.Anything, mock.Anything).Return(mockResult, nil)

				// Mock HasTokenExpired
				h.HasTokenExpired = func(expirationTimeStr string) (bool, error) {
					return false, nil
				}

				// Mock ValidateRoleFunc
				h.ValidateRoleFunc = func(roleID int64) (string, error) {
					return "user", nil
				}
			},
			expectedStatusCode: http.StatusUnauthorized,
			wantErr:            true,
		},
		{
			name:  "jwt_creation_error",
			token: "valid-token",
			setupMocks: func(h *TokenHandler, db *MockDatabaseExecutor) {
				// Setup connection provider
				h.DBProvider = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return &connect.Connections{Postgres: db}, nil
				}

				// Mock QueryRowStruct to return a valid user
				db.On("QueryRowStruct", mock.AnythingOfType("*authenticate.dbUser"), mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					user := args.Get(0).(*dbUser)
					user.Id = 123
					user.UserName = "testuser"
					user.OrganizationIdentifier = "org1"
					user.RoleId = 1
					user.APIKey = "apikey123"
					user.WebEnabled = 1
					user.WebTokenDurationSeconds = 3600
					user.ExpirationUTC = time.Now().Add(1 * time.Hour).Format(time.DateTime)
				}).Return(nil)

				// Mock Exec for token deletion
				mockResult := new(MockSQLResult)
				mockResult.On("LastInsertId").Return(int64(1), nil)
				mockResult.On("RowsAffected").Return(int64(1), nil)
				db.On("Exec", mock.Anything, mock.Anything).Return(mockResult, nil)

				// Mock HasTokenExpired
				h.HasTokenExpired = func(expirationTimeStr string) (bool, error) {
					return false, nil
				}

				// Mock ValidateRoleFunc
				h.ValidateRoleFunc = func(roleID int64) (string, error) {
					return "user", nil
				}

				// Mock JwtCreator to return error
				h.JwtCreator = func(username string, duration time.Duration, role int64, userPermissions jwttokens.UserPermissions) (string, time.Time, error) {
					return "", time.Time{}, errors.New("JWT creation error")
				}
			},
			expectedStatusCode: http.StatusUnauthorized,
			wantErr:            true,
		},
		{
			name:  "token_persistence_error",
			token: "valid-token",
			setupMocks: func(h *TokenHandler, db *MockDatabaseExecutor) {
				// Setup connection provider
				h.DBProvider = func(ctx context.Context, checkConnections ...bool) (*connect.Connections, error) {
					return &connect.Connections{Postgres: db}, nil
				}

				// Mock QueryRowStruct to return a valid user
				db.On("QueryRowStruct", mock.AnythingOfType("*authenticate.dbUser"), mock.Anything, mock.Anything).Run(func(args mock.Arguments) {
					user := args.Get(0).(*dbUser)
					user.Id = 123
					user.UserName = "testuser"
					user.OrganizationIdentifier = "org1"
					user.RoleId = 1
					user.APIKey = "apikey123"
					user.WebEnabled = 1
					user.WebTokenDurationSeconds = 3600
					user.ExpirationUTC = time.Now().Add(1 * time.Hour).Format(time.DateTime)
				}).Return(nil)

				// Mock Exec for token deletion
				mockResult := new(MockSQLResult)
				mockResult.On("LastInsertId").Return(int64(1), nil)
				mockResult.On("RowsAffected").Return(int64(1), nil)
				db.On("Exec", mock.Anything, mock.Anything).Return(mockResult, nil)

				// Mock HasTokenExpired
				h.HasTokenExpired = func(expirationTimeStr string) (bool, error) {
					return false, nil
				}

				// Mock ValidateRoleFunc
				h.ValidateRoleFunc = func(roleID int64) (string, error) {
					return "user", nil
				}

				// Mock JwtCreator
				h.JwtCreator = func(username string, duration time.Duration, role int64, userPermissions jwttokens.UserPermissions) (string, time.Time, error) {
					return "jwt-token", time.Now().Add(duration), nil
				}

				// Mock TokenPersister to return error
				h.TokenPersister = func(pg connect.DatabaseExecutor, userID int64, jwt string, expiresAt time.Time) error {
					return errors.New("token persistence error")
				}
			},
			expectedStatusCode: http.StatusUnauthorized,
			wantErr:            true,
		},
	}

	// Execute each test case
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			// Create handler and mock DB
			handler := &TokenHandler{}
			mockDB := new(MockDatabaseExecutor)

			// Setup mocks
			tt.setupMocks(handler, mockDB)

			// Create request and response recorder
			req, err := http.NewRequest("GET", "/token?token="+tt.token, nil)
			assert.NoError(t, err, "Failed to create HTTP request")
			w := httptest.NewRecorder()

			// Call the handler
			handler.TokenAccess(tt.token, w, req)

			// Assert response status code
			assert.Equal(t, tt.expectedStatusCode, w.Code, "Expected status code %d but got %d", tt.expectedStatusCode, w.Code)

			// Verify all mock expectations were met
			mockDB.AssertExpectations(t)
		})
	}
}

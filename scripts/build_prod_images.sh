#!/usr/bin/env bash
set -e

# Determine the directory where the script resides.
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
# Compute the repository base directory (assuming the script is in a subdirectory, e.g., "scripts").
REPO_DIR="$(dirname "$SCRIPT_DIR")"

# Use git from the repository base to determine the current branch.
BRANCH=$(git -C "$REPO_DIR" rev-parse --abbrev-ref HEAD)

# Determine the sorted, concatenated list of tags for the current commit.
TAGS=$(git -C "$REPO_DIR" tag --points-at HEAD | sort | paste -sd "-" -)
if [ -z "$TAGS" ]; then
  TAGS="untagged"
fi

echo "Building images with branch: $BRANCH and tags: $TAGS"

# List of microservices to build.
SERVICES=(coordinator etl broker)

# Loop through each service and build the production image.
for service in "${SERVICES[@]}"; do
  echo "Building image for $service..."
  
  docker build \
    --target prod \
    --build-arg GIT_BRANCH="$BRANCH" \
    --build-arg GIT_TAG="$TAGS" \
    -t synapse-its-data-core-"$service":latest \
    -f "$REPO_DIR/microservices/$service/Dockerfile" \
    "$REPO_DIR"
  
  echo "Built synapse-its-data-core-$service:latest"
done
